# M-FAITH 数据集构建方案：一种人机协同的方法

## 1. 核心原则：借鉴并超越

我们的数据集（命名为 **M-FAITH**）构建，将严格遵循并升级《Beyond Facts》论文中验证过的**半自动化、人机协同（Semi-automated, Human-in-the-loop）**核心原则。我们拒绝纯手工的"作坊式"生产，也摒弃纯AI生成的"黑箱式"生产，而是取两者之长，在保证**数据质量**的同时，实现**规模化**和**多样性**。

**核心思想：** 将AI（强大的MLLM）的角色从"被评估的对象"转变为"数据生产的协作者"，而人类专家的角色则从"繁重的体力劳动者"提升为"最终的质量控制者"。

---

## 2. 数据构建的详细流程

我们将整个流程分解为四个独立的、可执行的步骤：

### **第一步：图像源的筛选与准备 (Image Curation)**

-   **数据来源：**
    -   主要使用 **COCO 2017 `val` 验证集**。因为它场景丰富、物体标注质量高，是学术界公认的基准。
    -   未来可扩展至 Flickr30k, Visual Genome 等数据集以增加多样性。
-   **筛选标准：** 我们并非随机选取图片，而是要程序化地筛选出更"有趣"、更可能诱发意图幻觉的图片。标准包括：
    -   **物体丰富度：** 图片中包含至少3个以上被标注的物体。
    -   **关系复杂度：** 图片中存在多个物体间的互动或复杂的空间关系。
    -   **避免简单图：** 排除那些只有一个中心物体、背景纯净的"图标式"图片。

### **第二步：AI出题——自动化生成"图-指令-答案"三元组 (AI-Powered Generation)**

这是我们提升效率的关键。我们将使用一个能力顶尖的 MLLM（如 GPT-4o, Claude 3.5 Sonnet, 文心4.0-V）作为**"出题人（Generator Model）"**。

-   **输入：** 将一张经过筛选的图片输入给"出题人"。
-   **任务（Prompt）：** 给"出题人"下达一个精心设计的指令（Meta-Prompt），要求它扮演"数据工程师"的角色，并严格按照JSON格式输出以下内容：
    1.  **`instruction.text` (复杂指令):**
        -   要求它创造一个包含3-5个多样化约束的自然语言指令。
        -   指令必须包含至少一个**否定/排除性约束**（如"不要提..."）。
        -   指令必须包含至少一个**关系/属性约束**（如"...左边的..."、"...是什么颜色"）。
    2.  **`instruction.constraints` (原子化约束):**
        -   要求它将自己设计的复杂指令，精确地分解为一系列不可再分的、可独立验证的原子约束。
        -   每个约束都必须标注其类型（如 `exclusion`, `count`, `spatial_relation` 等），这个类型与我们 `visual_taxonomy.md` 中的分类法对应。
    3.  **`reference_answer` (黄金参考答案):**
        -   要求它生成一个100%完美遵循其设计的所有约束的、高质量的参考答案。

### **第三步：人工审核与精炼 (Human-in-the-Loop Refinement)**

这是我们保证质量的关键。我们将招募标注人员（可以是研究生或经过培训的众包人员），对AI生成的"三元组"进行审核。

-   **输入：** 标注平台会向审核员展示【原始图片】以及AI生成的【指令】、【约束】和【参考答案】。
-   **任务：** 审核员的核心任务是"找茬"和"改进"，而非从零创造。他们需要回答以下几个问题：
    1.  **指令质量：** 指令是否清晰、自然？约束是否合理且有挑战性？（*不行就pass或少量修改*）
    2.  **约束分解：** 原子化约束是否准确、无遗漏地反映了原始指令的意图？（*可在此处增、删、改*）
    3.  **答案质量：** 参考答案是否真的100%完美？是否可以写得更详细、更自然？（*可在此处进行编辑*）

-   **效率提升：** 相比于让审核员自己盯着图片苦思冥想设计难题，审核一个AI生成的高质量初稿，并将其中80%的内容直接采纳，效率至少可以提升5-10倍。

### **第四步：数据格式化与入库 (Finalization)**

-   经过人工审核并最终确认的数据，将被格式化为标准的JSONL格式。
-   每一条数据都将包含唯一的`id`，图片信息，以及经过人类最终确认的指令、约束和参考答案。
-   这些数据共同构成了我们的 **M-FAITH** 评估基准，为后续的模型评测提供高质量的"弹药"。

---

## 3. 结论

通过这套"AI大规模生成 + 人类精准调优"的流水线，我们能够以一种兼具**成本效益**和**质量控制**的方式，系统性地构建出一个专门用于诊断 MLLM 意图幻觉的大规模、高质量评估基准。这个方法论本身，就是我们研究项目的一个重要组成部分和贡献。 