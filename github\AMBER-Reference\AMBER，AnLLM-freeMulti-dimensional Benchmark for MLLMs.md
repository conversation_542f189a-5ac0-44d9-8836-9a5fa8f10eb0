# AMBER: An LLM-free Multi-dimensional Benchmark for MLLMs Hallucination Evaluation

Junyang $\mathbf { W a n g ^ { 1 * \dagger } }$ , <PERSON><PERSON>1∗, Guo<PERSON> $\mathbf { X } \mathbf { u } ^ { 2 }$ , <PERSON>1, <PERSON><PERSON> $\mathbf { G u } ^ { 1 }$ ,   
<PERSON><PERSON><PERSON> Jia1, <PERSON><PERSON><PERSON> $\mathbf { W a n g ^ { 1 } }$ , Haiyang $\mathbf { X } \mathbf { u } ^ { 2 }$ , Ming $\mathbf { Y a n } ^ { 2 }$ , <PERSON> Zhang2, <PERSON><PERSON>o $\mathbf { S a n g ^ { 1 , 3 \ddagger } }$ 1Beijing Jiaotong University 2Alibaba Group 3Peng Cheng Lab {junyangwang, yhangwang, zhangj_, yukai.gu, jiaht1101, jiaqiw, jtsang}@bjtu.edu.cn {guohai.xgh, shuofeng.xhy, ym119608, zj122146}@alibaba-inc.com

# Abstract

Despite making significant progress in multimodal tasks, current Multi-modal Large Language Models (MLLMs) encounter the significant challenge of hallucinations, which may lead to harmful consequences. Therefore, evaluating MLLMs’ hallucinations is becoming increasingly important in model improvement and practical application deployment. Previous works are limited in high evaluation costs (e.g., relying on humans or advanced LLMs) and insufficient evaluation dimensions (e.g., types of tasks and hallucinations). In this paper, we propose an LLM-free multi-dimensional benchmark AMBER, which can be used to evaluate both generative task and discriminative task including existence, attribute and relation hallucination. Based on AMBER, we design a lowcost and efficient evaluation pipeline. Additionally, we conduct a comprehensive evaluation and detailed analysis of mainstream MLLMs including GPT-4V(ision), and also give guideline suggestions for mitigating hallucinations. The data and code of AMBER are available at https://github.com/junyangwang0410/ AMBER.

# 1 Introduction

GPT-4 exhibited a range of remarkable abilities in vision-language tasks (OpenAI, 2023). Subsequently, many works developed Multi-modal Large Language models (MLLMs) with similar capabilities to GPT-4 by integrating visual encoders into Large Language Models (LLMs), e.g., MiniGPT-4, LLaVA and mPLUG-Owl (Liu et al., 2023d; Zhu et al., 2023; Ye et al., 2023b; Dai et al., 2023; Liu et al., 2023c; Chen et al., 2023). However, MLLMs occasionally generate content that seems plausible but is unfaithful to the given image, which is called hallucination (Liu et al., 2023b; Ye et al., 2023a;

HallucinationinDiscriminativeTask #Prompt: Is there a person in this image? Yes,there isaperson inthe image. #Prompt: Does the girafe sit in this image? Yes,the giraffe issittinginthe image. Relation Hallucination #Prompt:Is there direct contact between the giraffeand tree? Yes,thegiraffeiseatingtheleavesfromthetree. HallucinationinGenerativeTask #Prompt: Describe this image. The image features a grass landscape.A giraffe has been standing in the grass.There is a man next toit.The man is siting ona bench taking pictures of the giraffe.There is a tree on the left side of the picture.Alake can be seen faintly in the distance.The skyis cloudy blue.Thesun is clearly visible.

Yin et al., 2023). This may lead to harmful consequences, especially when users without sufficient domain knowledge over-rely on these models.

Therefore, evaluating MLLMs’ hallucinations is an important step in the actual application deployment and the subsequent enhancement of reliability. The hallucinations of MLLMs mainly occur in two major types of task: generative and discriminative, as shown in the Figure 1. However, there are several limitations of existing evaluation methods for each type of task. For the generative task, most evaluation methods suffer from a limitation wherein they rely on additional LLMs. This reliance results in a huge cost, posing challenges for scalable applications. For example, Zhou et al. (2023b); Zhai et al. (2023) evaluated MLLMs relying on humans or GPT-4. Wang et al. (2023a); Gunjal et al. (2023) proposed to train a hallucinatory detection model for evaluation. Regarding the discriminative task, Li et al. (2023) proposed an object existence hallucination evaluation method, but lacked evaluation on other types of hallucinations such as those related to attributes and relations. Note that there is no method incorporating all the above types of tasks and hallucinations. We summarize these methods in Table 1.

To address the aforementioned problems, we propose AMBER (An LLM-free Multi-dimensional

<html><body><table><tr><td>Evaluation Method</td><td>Task Type</td><td>Hallucination Type</td><td>LLM-free</td></tr><tr><td>POPE (Li et al., 2023)</td><td>Discriminative</td><td>Exis.</td><td>√</td></tr><tr><td>M-HalDetect (Gunjal et al., 2023)</td><td>Generative</td><td>N/A</td><td>×</td></tr><tr><td>HaELM (Wang et al., 2023a)</td><td>Generative</td><td>N/A</td><td>×</td></tr><tr><td>Halle-Switch (Zhai et al., 2023)</td><td>Generative</td><td>N/A</td><td>×</td></tr><tr><td>AMBER (ours)</td><td>Generative&Discriminative</td><td>Exis. & Attr. & Rel.</td><td>√</td></tr></table></body></html>

Table 1: Comparison with existing hallucination evaluation methods. We utilize the following abbreviations: Exis.- Existence; Attr.-Attribute; Rel.-Relation.

Benchmark) for MLLMs hallucination evaluation. First, we collect a batch of high-quality images with clear content and well-defined objects, which have not been used for training MLLMs. Then, we provide comprehensive annotations to facilitate the evaluation of both generative and discriminative tasks, covering three types of hallucination: existence, attribute and relation, as shown in Figure 3. Accordingly, we develop an LLM-free evaluation pipeline (see Figure 3). Based on our proposed AMBER, we conduct multi-dimensional hallucination evaluations on nine mainstream MLLMs, including GPT-4V, and observe persistent hallucination issues. Furthermore, we investigate the characteristics of hallucinations in MLLMs, their contributing factors, and offer effective mitigation strategies based on our findings, such as enriching the structure and distribution of training data, using more powerful visual or language models, controlling the response length, etc.

We summarize the contributions as follows:

• We meticulously construct a benchmark AMBER for evaluating hallucinations in both the generative task and discriminative task of MLLMs. AMBER provides comprehensive coverage of evaluations for various types of hallucination, including those of existence, attribute and relation.

• We develop an LLM-free evaluation pipeline based on AMBER, that caters to both types of task and different hallucination types.

• Employing AMBER, we evaluate and analyze the mainstream MLLMs including the most advanced GPT-4V. And we give guideline suggestions for mitigating hallucinations.

# 2 Related Work

# 2.1 MLLMs

Large Language Models (LLMs), represented by GPT-4, are currently demonstrating powerful language processing capabilities, greatly propelling the development of natural language processing (OpenAI, 2023; Touvron et al., 2023a,b; Chiang et al., 2023). In order to apply the formidable capabilities of LLMs to multi-modal tasks, researchers have integrated visual modules with LLMs, giving rise to Multi-modal Large Language Models (MLLMs). MLLMs achieve multitask learning in the multi-modal domain through instruction learning and showcase robust capabilities in many traditional multi-modal tasks, such as visual question answering, image captioning and prompt-based object detection (Liu et al., 2023d; Zhu et al., 2023; Ye et al., 2023b; Dai et al., 2023; Liu et al., 2023c; Chen et al., 2023; Ye et al., 2023c).

# 2.2 Hallucination in MLLMs

While MLLMs have exhibited excellent performance, the MLLMs often generate content in their responses that is unfaithful to the given images, which is called hallucination (Liu et al., 2023b). Hallucinations are believed to occur for the following reasons: (1) Insufficiently diverse training data with some errors (Liu et al., 2023b); (2) MLLMs losing attention to the image when generating the responses (Wang et al., 2023a); (3) Information loss in visual input after the image is fed into the visual encoder, making it unable to completely align with the textual description (Zhai et al., 2023). Hallucinations often occur as descriptions or judgments that are reasonable but not faithful to the images, making them highly deceptive and misleading. Therefore, the presence of hallucinations significantly restricts the application scenarios.

There are currently three main types of methods for evaluating hallucinations in MLLMs. The first

Input Image Annotations contact Existence: {dog,doll, bear, carpet, floor} dog # Existing objects in the image Attribute: {dog-action-lie-run,doll-number-1-2} # A correct and a wrong attribute for the objects, # the attribute tags are state，number and action Relation: {dog-doll-yes,dog-carpet-yes} # Two objects with or without contact，annotated by yes # and no，respectively Hallucinatory target objects: {light, sofa, table,person,cat} # Non-existent but reasonable objects in the image

is based on human or GPT-4 (Zhou et al., 2023b; Zhai et al., 2023). This type of method is relatively reliable but comes with a high cost. This cost becomes particularly prominent in academic research where the effectiveness of experimental setups often requires multiple evaluations. The second is based on hallucinatory detection models (Wang et al., 2023a; Gunjal et al., 2023). In this type of method, hallucinatory image descriptions are first collected, and then a model is trained to determine whether a given description contains hallucinations. However, the model’s performance is highly dependent on hallucinatory data. Additionally, the substantial training costs and inapplicability to discriminative task also exist. The third method is based on object detection (Li et al., 2023). It involves using detectors to extract all the objects in an image and then asking the MLLMs whether a specific object exists in the image. The MLLMs’ response of “yes” or “no” is used to determine the presence of hallucinations. This method is only applicable to discriminative task and only evaluate the existence hallucination.

To address the issues of high cost, requirement of LLM and incomplete evaluations of the types of task and hallucination in the existing methods, we introduce AMBER, an LLM-free multidimensional benchmark.

# 3 The AMBER Benchmark

In this section, we introduce the process of constructing AMBER and the pipeline for evaluating MLLMs’ hallucinations.

# 3.1 Dataset Construction

The construction of the dataset consists of three steps: collecting images, annotating images and designing prompt templates. We will detail each of these steps below.

Collecting Images. Collecting high-quality and diverse images is the foundational step in our work to objectively evaluate the hallucinations of MLLMs. Existing works indicated that mainstream annotated datasets have been widely used for MLLMs training, resulting in an inflated performance of trained MLLMs on benchmarks composed of such data (Zhou et al., 2023a). To avoid this, we source images from test sets of multi-modal open-source datasets and copyright-free image repositories on the Internet. For image quality, we manually select images with clear content and well-defined objects. During the annotation phase, images too challenging for accurate annotation are discarded to avoid incomplete annotations. Additionally, we employed multi-dimensional object filtering to ensure the diversity of the images as far as possible. After filtering, the total number of images in our dataset is 1,004. Detailed data statistics are shown in Appendix A.1.

Annotating Images. In this step, we meticulously annotate 4 types of content for each image. We take an example in Figure 2 to illustrate these annotations.

• Existence refers to all visible objects in the image, including background objects like the “carpet” and “floor”. • Attribute refers to the attributes of the existing objects in the image. We annotate them from three perspectives: state (such as color, shape, etc.), number (typically marked when an object appears more than once in the image) and action (actions of humans or animals in the image). For example, in “dog-action-lierun”, “dog” is the object, “action” indicates the type of attribute, ”lie“ and ”run“ represent the correct and incorrect annotations of “action” respectively. Note that we set up in

Image Evaluation on Discriminative Task Evaluation Data Presoiisimage Label: No ResponseofMLLMs No, there is no person in this image. AnntationHslucination Prothegirafesitin hisimage? Label: No Yes,the giraffe is sitting in this image Response ofMLLMs: X Annotations for Attribute Hallucination TPrompt: Label:No Isthere directcontactbetween the giraffeand tree? ResponseofMLLMs: Yesthegiafeiseaigtleavsthetr Annotations for RelationHallucination Evaluation on Generative Task   
Prompt: Evaluation Data Mman,mad bencnts CHAIR=⑤/[ = 0.5   
iDescribe this image. lake,sun 个 Final Objectsl   
!Label: Rbj={grass,giraffe,grass,man   
|Aobj ={grass, tree,mountain, giraffe,sky,cloud } man,bench, giraffe, lake, sky, sun } Annotations for Hallucination   
Language Objects Filtering Obiects   
i been standing in the grass.There isa man next to it. Toolkit   
! The man is sitting on a bench taking pictures of the   
giraee X ache   
cloudyblue.Thesunisclearly visible. Noun Extraction picture,lake,distance,sky, sun } Initial Objects

correct annotations to construct counterfactual prompts (see Designing Prompt Templates).

• Relation refers to whether there is direct contact between two objects in an image.

• Hallucinatory target objects explicitly do not exist in the image, but they are likely to be imagined by MLLMs based on the image.

During the annotation phase, multiple groups of annotators label the collected image. We then coordinate the results to achieve a unified annotation set. To reduce potential mistakes from annotators, we manually inspect the annotation outcomes and correct any erroneous annotations during the annotation validation process.

Designing Prompt Templates. In the last step, we design the prompt templates for evaluation on both generative and discriminative task. For the generative task, we use the most commonly used generative prompt, “Describe this image.”, to obtain descriptions of images from MLLMs.

For the discriminative task, we design corresponding prompts based on different types of hallucinations. We use three prompts “Is the {object} {state} in this image?”, “Is/Are there {number} {object} in this image?” and “Does the {object} $\{ a c t i o n \}$ in this image?” to evaluate state, number and action perspectives of the attribute hallucination, respectively. For relation hallucination, we use the prompt “Is there direct contact between the $\{ o b j e c t I \}$ and {object 2} in this image?”. For the hallucinatory target objects, we construct the counterfactual prompt “Is there a $\{ o b j e c t \}$ in this image?”.

# 3.2 Evaluation

In this subsection, we introduce the evaluation pipeline for AMBER, as shown in Figure 3. We first describe how to obtain and process the responses from MLLMs, then clarify the metrics.

# 3.2.1 Prompting MLLMs

MLLMs Respondents. we select nine mainstream MLLMs for evaluation, including LLaVA (Liu et al., 2023d), MiniGPT-4 (Zhu et al., 2023), mPLUG-Owl (Ye et al., 2023b), InstructBlip (Dai et al., 2023), LLaVA-1.5 (Liu et al., 2023c), QwenVL (Bai et al., 2023), CogVLM (Wang et al., 2023b), mPLUG-Owl2 (Ye et al., 2023c) and GPT4V (OpenAI, 2023).

Response Processing. AMBER provides a set of $I n p u t { = } \{ I m g , I n s \}$ , where Img represents the image, $I n s$ denotes the instruction constructed according to prompt templates and the annotations of the image. As shown in the Figure 3, we obtain the initial response $R$ by fitting Input into MLLM.

Regarding the generative task, we first extract nouns from $R$ by language toolkits (e.g., nltk) to obtain the initial objects $R _ { o b j } { = } \{ o b j _ { 1 } ^ { R } , ~ o b j _ { 2 } ^ { R }$ $\cdots , o b j _ { n } ^ { R } \}$ . Then, we construct an objects list $X _ { o b j } { = } \{ o b j _ { 1 } ^ { X } , o b j _ { 2 } ^ { X } , . . . , o b j _ { n } ^ { X } \}$ consisting of all annotated objects in AMBER to filter unnecessarily objects in $R _ { o b j }$ like “picture”, “distance” and “side” as shown in Figure 3. Finally we obtain the final objects $\boldsymbol { R _ { o b j } ^ { ' } }$ by the follow formula:

$$
R _ { o b j } ^ { ' } = R _ { o b j } \cap X _ { o b j } .
$$

For the discriminative task, since the queries are in the form of a true/false, we only need to determine if the responses contain “yes” or “no”.

# 3.2.2 Metrics.

Metrics on the Generative Task. We introduce four metrics for evaluating hallucinations on the generative task.

CHAIR (Rohrbach et al., 2018) is a commonly used metric for evaluating hallucinations. It measures the frequency of hallucinatory objects appearing in the responses. AMBER provides an annotated objects list $A _ { o b j } { = } \{ o b j _ { 1 } ^ { A } , o b j _ { 2 } ^ { A } , . . . , o b j _ { n } ^ { A } \}$ as seen in Figure 3. $C H A I R ( R )$ is represented by the following formula:

$$
C H A I R ( R ) = 1 - \frac { l e n ( R _ { o b j } ^ { ' } \cap A _ { o b j } ) } { l e n ( R _ { o b j } ^ { ' } ) } .
$$

Cover measures the object coverage of responses. Specifically, it quantifies the proportion of objects mentioned in the response $\hat { R _ { o b j } ^ { ' } }$ relative to the objects identified in the $A _ { o b j }$ . An ideal response is considered to be one that minimizes hallucinatory content without significantly compromising the coverage of objects in the image. Cover $( R )$ is represented by the following formula:

$$
C o \nu e r ( R ) = \frac { l e n ( R _ { o b j } ^ { ' } \cap A _ { o b j } ) } { l e n ( A _ { o b j } ) } .
$$

Hal represents the proportion of responses with hallucinations. For a response $R$ , if $C H A I R ( R ) \neq 0$ , then $R$ is considered to have hallucinations. $H a l ( R )$ is represented by the following formula:

$$
H a l ( R ) = { \left\{ \begin{array} { l l } { 1 } & { { \mathrm { i f } } C H A I R ( R ) \neq 0 , } \\ { 0 } & { { \mathrm { o t h e r w i s e } } . } \end{array} \right. }
$$

Cog. To assess whether the hallucinations in MLLMs are similar to those in human cognition, we utilize a set of hallucinatory target objects, denoted as $H _ { o b j } { = } \{ o b j _ { 1 } ^ { H } , o b j _ { 2 } ^ { H } , . . . , o b j _ { n } ^ { H } \}$ . These objects are employed to develop a metric named Cog for evaluating the likelihood of MLLMs generating the objects from $H _ { o b j }$ . $C o g ( R )$ is represented by the following formula:

$$
C o g ( R ) = \frac { l e n ( R _ { o b j } ^ { ' } \cap H _ { o b j } ) } { l e n ( R _ { o b j } ^ { ' } ) } .
$$

Note that we calculate the average values of the four mentioned metrics over all the queries in AMBER, represented by CHAIR, Cover, Hal, and $C o g$ , respectively.

Metrics on Discriminative Task. Since MLLMs are constrained to answer with a binary “yes” or “no” on discriminative task, we use standard classification metrics: Accuracy, Precision, Recall and $F l$ . As AMBER is designed for hallucination evaluation, both Precision and Recall are calculated under the hallucinatory questions (where the ground truth is “no”). To prevent MLLMs from being biased towards rejection and obtaining high scores, Accuracy is still calculated for all questions.

AMBER Score. To comprehensively evaluate the performance of various MLLMs under both types of task, we introduce the AMBER Score to integrate the CHAIR on generative task and the $F l$ on discriminative task. The AMBER Score is represented by the following formula:

$$
A M B E R S c o r e = \frac { 1 } { 2 } \times ( 1 - C H A I R + F I ) .
$$

# 4 Results

# 4.1 AMBER Overall Results

As shown in Table 2, the performance of nine models in terms of hallucination is presented in ascending order based on AMBER Score. Notably, GPT-4V stands out as the top performer, exhibiting impressive results in both generative and discriminative tasks. However, GPT-4V still presents some instances of hallucinations, which we will discuss in detail in section 4.3. In second place is QwenVL, which demonstrates a lower incidence of hallucinations in both task types and excels in three out of four metrics. Additionally, we observe that both mPLUG-Owl2 and LLaVA-1.5 show significant improvements over their first-generation counterparts, mPLUG-Owl and LLaVA, respectively. We speculate that this might be due to the upgraded versions utilizing stronger foundational LLMs and the enhanced quality of training data. Furthermore, MLLMs tend to produce more hallucinations in discriminative task compared to the generative task, and we will introduce them separately below.

<html><body><table><tr><td rowspan="2">Model</td><td colspan="4">GENERATIVE TASK</td><td colspan="4">DISCRIMINATIVE TASK</td><td rowspan="2">AMBER Score</td></tr><tr><td>CHAIR↓</td><td>Cover↑</td><td>Hal↓</td><td>Cog</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td></tr><tr><td>mPLUG-Owl</td><td>21.6</td><td>50.1</td><td>76.1</td><td>11.5</td><td>40.1</td><td>92.8</td><td>10.5</td><td>18.9</td><td>48.7</td></tr><tr><td>LLaVA</td><td>11.5</td><td>51.0</td><td>48.8</td><td>5.5</td><td>42.7</td><td>74.1</td><td>21.0</td><td>32.7</td><td>60.6</td></tr><tr><td>MiniGPT-4</td><td>13.6</td><td>63.0</td><td>65.3</td><td>11.3</td><td>63.6</td><td>90.5</td><td>50.4</td><td>64.7</td><td>75.6</td></tr><tr><td>CogVLM</td><td>5.6</td><td>57.2</td><td>23.6</td><td>1.3</td><td>69.0</td><td>88.9</td><td>60.9</td><td>72.3</td><td>83.4</td></tr><tr><td>LLaVA-1.5</td><td>7.8</td><td>51.0</td><td>36.4</td><td>4.2</td><td>72.0</td><td>93.2</td><td>62.4</td><td>74.7</td><td>83.5</td></tr><tr><td>mPLUG-Owl2</td><td>10.6</td><td>52.0</td><td>39.9</td><td>4.5</td><td>75.6</td><td>95.0</td><td>66.9</td><td>78.5</td><td>84.0</td></tr><tr><td>InstructBLIP</td><td>8.8</td><td>52.2</td><td>38.2</td><td>4.4</td><td>76.5</td><td>84.5</td><td>79.0</td><td>81.7</td><td>86.5</td></tr><tr><td>Qwen-VL</td><td>5.5</td><td>49.4</td><td>23.6</td><td>1.9</td><td>81.2</td><td>90.8</td><td>79.7</td><td>84.9</td><td>89.7</td></tr><tr><td>GPT-4V</td><td>4.6</td><td>67.1</td><td>30.7</td><td>2.6</td><td>83.4</td><td>84.9</td><td>90.1</td><td>87.4</td><td>91.4</td></tr></table></body></html>

Table 2: The overall evaluation results of AMBER on generative task and discriminative task.

Results on Generative Task. We observe that various MLLMs generally exceed a Hal value of $30 \%$ and even the most advanced MLLMs still have around $5 \%$ hallucinatory objects when facing openended generative instructions. This indicates that reducing hallucinations in the generative task remains a significant challenge. CogVLM and QwenVL demonstrate significant advantages with high values of CHAIR, Hal and Cog. MiniGPT-4 excels in the Cover but with more hallucinations. The high Cog indicates that MiniGPT-4 is more prone to associations and also to make mistakes.

Results on Discriminative Task. We find that the recall values for all MLLMs are significantly lower than the precision values. This indicates that MLLMs tend to give affirmative responses, suggesting that they are easily misled by the hallucinatory content of the questions, as shown in (Li et al., 2023). We find that Qwen-VL and InstructBLIP exhibit a significant advantage in discriminative task. Actually, they also perform well in a similar VQA-v2 (Goyal et al., 2017) benchmark. We believe an effective way to mitigate hallucinations is to enhance MLLMs’ capabilities on discriminative benchmarks such as VQA-v2.

# 4.2 Multi-dimensional Results

Benefiting from the fine-grained annotations of AMBER, we conduct a multi-dimensional analysis on existence, attribute and relation hallucination on discriminative tasks. We will introduce them one

by one below.

Existence Hallucination. The results in Table 3 indicate that current mainstream MLLMs are not heavily troubled by the existence hallucination. However, we can see that all the precision values are close to 1, but the recall values don’t match accordingly. The common issue that MLLMs are more inclined to provide affirmative responses inspires the idea of incorporating more negative samples during the training phase.

Attribute Hallucination. In contrast, attribute hallucination poses more significant challenges. Even the best-performing MLLM achieves only an $F l$ value of around 0.8. We conduct a detailed analysis of attribute hallucination from perspectives of state, number and action in Appendix A.3.1.

Relation Hallucination. MLLMs are most susceptible to induction and hallucination in terms of relation. The accuracy values of most MLLMs are only around 0.7. This may stem from the fact that current multi-modal instruction fine-tuning datasets offer limited detail about the relationships between objects, instead focusing more on individual objects. Therefore, we suggest data creators enrich the object descriptions from attribute and relation perspectives.

# 4.3 GPT-4V(ision)

Existing works demonstrate that GPT-4V also produces a few hallucinations in certain scenarios (Liu et al., $2 0 2 3 \mathrm { a }$ ; Wu et al., 2023; Cui et al., 2023). Our evaluations using AMBER, confirm these findings, and we illustrate some specific examples of hallucinations in Appendix 4.3. However, GPT-4V stands out as the most robust among current MLLMs. As detailed in Table 2, GPT-4V not only presents the least hallucinations but also achieves the highest

<html><body><table><tr><td rowspan="2">Model</td><td colspan="3">EXISTENCE</td><td colspan="4">ATTRIBUTE</td><td colspan="4">RELATION</td></tr><tr><td>P.</td><td>R.</td><td>F1</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td></tr><tr><td>mPLUG-Owl</td><td>99.7</td><td>9.4</td><td>17.2</td><td>55.7</td><td>87.5</td><td>13.2</td><td>22.9</td><td>59.6</td><td>81.5</td><td>3.2</td><td>6.2</td></tr><tr><td>LLaVA</td><td>99.9</td><td>4.4</td><td>8.4</td><td>62.9</td><td>78.9</td><td>35.1</td><td>48.6</td><td>63.8</td><td>55.7</td><td>60.8</td><td>58.1</td></tr><tr><td>MiniGPT-4</td><td>99.9</td><td>66.7</td><td>80.0</td><td>61.7</td><td>82.7</td><td>29.7</td><td>43.7</td><td>63.4</td><td>56.7</td><td>49.3</td><td>52.7</td></tr><tr><td>CogVLM</td><td>100</td><td>73.3</td><td>84.5</td><td>66.8</td><td>79.7</td><td>44.9</td><td>57.4</td><td>66.7</td><td>59.8</td><td>59.9</td><td>59.8</td></tr><tr><td>LLaVA-1.5</td><td>100</td><td>71.5</td><td>83.3</td><td>72.0</td><td>88.0</td><td>51.0</td><td>64.6</td><td>73.9</td><td>72.0</td><td>60.2</td><td>65.6</td></tr><tr><td>mPLUG-Owl2</td><td>100</td><td>80.4</td><td>89.1</td><td>76.6</td><td>88.1</td><td>61.5</td><td>72.4</td><td>58.6</td><td>81.8</td><td>40.6</td><td>54.3</td></tr><tr><td>InstructBLIP</td><td>100</td><td>80.2</td><td>89.0</td><td>76.1</td><td>75.9</td><td>76.7</td><td>76.3</td><td>66.8</td><td>56.7</td><td>83.6</td><td>67.6</td></tr><tr><td>Qwen-VL</td><td>100</td><td>83.5</td><td>91.0</td><td>81.9</td><td>83.7</td><td>79.2</td><td>81.4</td><td>71.3</td><td>69.1</td><td>55.4</td><td>61.6</td></tr><tr><td>GPT-4V</td><td>100</td><td>89.6</td><td>94.5</td><td>80.4</td><td>75.2</td><td>90.6</td><td>82.2</td><td>84.0</td><td>77.1</td><td>90.3</td><td>83.2</td></tr></table></body></html>

Table 3: The detailed evaluation results on discriminative task, where EXISTENCE, ATTRIBUTE and RELATION represent existence hallucination, attribute hallucination and relation hallucination, respectively. Since the the questions used to evaluate the existence hallucination are all negative responses, recall and accuracy are equivalent.

![](images/7ea6f86814ba9a14d793de529031041e898363392e228d89bb0fd2d1a25effb5.jpg)  
Figure 4: The frequency of hallucinatory objects appearing at different relative positions in MLLMs’ responses.

image object coverage in the generative task, according to the CHAIR and Cover. This highlights the advanced capabilities of GPT-4V, as it surpasses the existing MLLMs in the trade-off between hallucinations and coverage. Regarding discriminative task, GPT-4V continues to make a strong impression, as indicated in Table 2. Table 3 further shows that GPT-4V achieved the best performance across all three types of hallucination.

# 4.4 Analysis of Hallucinations in MLLMs

Position of Hallucination Appearance. We focus on the generative task and select four models with relatively low levels of hallucination to investigate where hallucinatory objects tend to appear. The experimental results are depicted in Figure 4. It is evident that hallucinatory objects frequently occur in the middle and latter parts of the responses, which may be attributable to snowballing or error accumulation.

![](images/e1e6b20bbda322d8f9fd0e0ce8a403f4f25907b34a24ee2933311edc0d25f4cc.jpg)  
Figure 5: Trends of the metrics Hal and Cover for different MLLMs with respect to the relative response length.

Hallucination vs. Object Coverage. To analyze the relationship between the hallucination and object coverage, we truncate the responses at different lengths and calculate Cover and Hal to evaluate object coverage and hallucination, respectively. As shown in Figure 5, there is a noticeable tradeoff trend that, as the response length increases, both object coverage and hallucinations tend to increase correspondingly. While more conservative responses might reduce hallucinations, it is important to ensure that this does not compromise the quality of the response.

Tendency to Respond Yes or No. A notable challenge for MLLMs is their propensity to produce affirmative responses. This tendency occurs when responding to prompts in discriminative task, irrespective of whether these responses accurately reflect the content of the images. We compile the response tendencies of MLLMs as shown in Table 4. The results show that the Yes Ratio values of almost all MLLMs are higher than No Ratio. Only

Table 4: The ratio of MLLMs’ responses with “Yes” or “No” on discriminative tasks, where w/ Hal and w/o Hal represent the ground truth for queries with “Yes” and “No”, respectively.   

<html><body><table><tr><td rowspan="2">Model</td><td colspan="2">Yes Ratio</td><td colspan="2">No Ratio</td></tr><tr><td>All.</td><td>w/ Hal.</td><td>All.</td><td>w/o Hal.</td></tr><tr><td>CogVLM</td><td>71.8</td><td>27.5</td><td>28.2</td><td>5.7</td></tr><tr><td>LLaVA-1.5</td><td>71.0</td><td>24.5</td><td>29.0</td><td>3.5</td></tr><tr><td>mPLUG-Owl2</td><td>69.6</td><td>22.3</td><td>28.2</td><td>4.3</td></tr><tr><td>InstructBLIP</td><td>49.5</td><td>11.7</td><td>50.5</td><td>12.2</td></tr><tr><td>Qwen-VL</td><td>41.8</td><td>12.2</td><td>58.2</td><td>20.5</td></tr><tr><td>GPT-4V</td><td>39.7</td><td>4.7</td><td>60.3</td><td>15.0</td></tr></table></body></html>

InstructBLIP does not show a clear tendency, while Qwen-VL and GPT-4V tend to provide negative responses. The response tendencies of MLLMs significantly influence their propensity to generate erroneous content. Therefore, it is crucial to incorporate negative responses into training data to reduce such hallucinations.

# 4.5 Ablation Study

Visual Resolution. Previous studies have demonstrated that enhancing image resolution can lead to improved performance. We modify the image resolution of AMBER to investigate its impact on hallucinations. The results are shown in Table 5. Following the increase in resolution, there is a decrease in the level of hallucinations. It is worth noting that GPT-4V is minimally affected by resolution. This also illustrates the robustness of GPT-4V in terms of hallucination for image resolution.

Table 5: AMBER Score variation with image resolution changes.   

<html><body><table><tr><td rowspan="2">Model</td><td colspan="2">Relative Resolution</td></tr><tr><td>25% 50%</td><td>75% 100%</td></tr><tr><td>CogVLM</td><td>83.0 83.1</td><td>83.3 83.4</td></tr><tr><td>LLaVA-1.5</td><td>82.9 83.1</td><td>83.5 83.5</td></tr><tr><td>Qwen-VL</td><td>89.1 89.3</td><td>89.6 89.7</td></tr><tr><td>GPT-4V</td><td>91.2 91.4</td><td>91.3 91.4</td></tr></table></body></html>

The Scales of LLMs. We investigate the impact of hallucinations by varying the scales of LLMs, and the results are shown in Table 6. As the number of parameters in the LLM increases, MiniGPT-4 shows a rising tendency for hallucinations in the generative task and a reduced incidence in discriminative tasks. In contrast, LLaVA-1.5 shows an opposing trend. The impact of LLM’s scales on these two MLLMs does not demonstrate a consistent pattern. This variance suggests that hallucination formation in MLLMs might be more influenced by their vision or connectivity modules.

<html><body><table><tr><td rowspan="2">Model</td><td colspan="2">Generative</td><td colspan="2">Descriminative</td></tr><tr><td>CHAIR↓</td><td>Cover↑</td><td>Acc</td><td>F1</td></tr><tr><td rowspan="2">MiniGPT-47B MiniGPT-413B</td><td>13.6</td><td>63.0</td><td>63.6</td><td>64.7</td></tr><tr><td>14.3</td><td>63.6</td><td>64.9</td><td>66.7</td></tr><tr><td>LLaVA-1.57B</td><td>7.8</td><td>51.0</td><td>72.0</td><td>74.4</td></tr><tr><td>LLaVA-1.513B</td><td>7.0</td><td>50.8</td><td>72.2</td><td>73.1</td></tr></table></body></html>

Table 6: Results of MLLMs under different scales of LLMs.

Training Data. Academic datasets and multimodal instruction data have been widely used in the training phase of MLLMs. To assess their impact on producing hallucinations, we evaluate MiniGPTv2 in both stage-2 and stage-3. In stage-2, the model was trained on academic datasets, During stage-3, it received additional training with the incorporation of multi-modal instructional data The results presented in Table 7 indicate that stage-3 exhibits fewer hallucinations on the generative task but shows an increase in discriminative task. This phenomenon can be attributed to the structured output format of academic datasets, which is more conducive to enhancing the model’s discriminative capabilities. While the addition of multi-modal instruction data strengthens the model’s generative capacity, it also introduces challenges to discriminative abilities in an open-ended generation.

<html><body><table><tr><td rowspan="2">Training Data</td><td colspan="2">Generative</td><td colspan="2">Descriminative</td></tr><tr><td>CHAIR↓</td><td>Cover↑</td><td>Acc</td><td>F1</td></tr><tr><td>Stage-2</td><td>35.0</td><td>22.1</td><td>37.5</td><td>16.6</td></tr><tr><td>Stage-3</td><td>32.8</td><td>31.6</td><td>36.2</td><td>10.8</td></tr></table></body></html>

Table 7: Results of MiniGPT-v2 in different stages.

# 5 Conclusion

MLLMs are rapidly advancing, but the hallucinations still require attention. Existing methods for hallucination evaluation suffer from high costs and insufficient evaluation dimensions. In this work, we introduce AMBER, an LLM-free multidimensional benchmark for the hallucination evaluation of MLLMs. AMBER does not rely on additional LLMs and evaluates across various tasks and hallucination types. We conducted a detailed analysis of mainstream MLLMs by AMBER, exploring factors related to hallucinations and providing suggestions for mitigation. In future work, we will delve deeper into mitigating hallucinations and advance the reliability of MLLMs.

# 6 Limitations

The AMBER proposed in this work offers significant advantages in terms of evaluation efficiency, cost and quality. However, AMBER still has some limitations. Firstly, while AMBER provides evaluation algorithms for attribute and relation hallucination, these evaluations are still limited to discriminative task. This is because we cannot guarantee that MLLMs can provide effective descriptions of attributes and relations in the generative task. Secondly, there is a possibility of mistakes in extracting the object using language toolkits. For example, “orange” can be used as an adjective to describe color, but we find that nltk might identify it as a noun. Similar situations also include compound nouns, for example, the “water” of “water bottle” may be mistakenly considered as a hallucination object. Lastly, the hallucinatory target objects we annotated are still limited, which means that less common objects in images may be overlooked. As MLLMs’ recognition capabilities improve, the number of hallucinatory target objects should also increase accordingly.

# References

Jinze Bai, Shuai Bai, Shusheng Yang, Shijie Wang, Sinan Tan, Peng Wang, Junyang Lin, Chang Zhou, and Jingren Zhou. 2023. Qwen-vl: A frontier large vision-language model with versatile abilities. arXiv preprint arXiv:2308.12966. Jun Chen, Deyao Zhu Xiaoqian Shen Xiang Li, Zechun Liu Pengchuan Zhang, Raghuraman Krishnamoorthi Vikas Chandra Yunyang Xiong, and Mohamed Elhoseiny. 2023. Minigpt-v2: Large language model as a unified interface for vision-language multi-task learning. arXiv preprint arXiv:2310.09478. Wei-Lin Chiang, Zhuohan Li, Zi Lin, Ying Sheng, Zhanghao Wu, Hao Zhang, Lianmin Zheng, Siyuan Zhuang, Yonghao Zhuang, Joseph E Gonzalez, et al. 2023. Vicuna: An open-source chatbot impressing gpt-4 with $9 0 \% ^ { * }$ chatgpt quality. See https://vicuna. lmsys. org (accessed 14 April 2023). Chenhang Cui, Yiyang Zhou, Xinyu Yang, Shirley Wu, Linjun Zhang, James Zou, and Huaxiu Yao. 2023. Holistic analysis of hallucination in gpt-4v (ision):

Bias and interference challenges. arXiv preprint arXiv:2311.03287.

Wenliang Dai, Junnan Li, Dongxu Li, Anthony Meng Huat Tiong, Junqi Zhao, Weisheng Wang, Boyang Li, Pascale Fung, and Steven Hoi. 2023. Instructblip: Towards general-purpose vision-language models with instruction tuning. arXiv preprint arXiv:2305.06500.   
Yash Goyal, Tejas Khot, Douglas Summers-Stay, Dhruv Batra, and Devi Parikh. 2017. Making the V in VQA matter: Elevating the role of image understanding in Visual Question Answering. In Conference on Computer Vision and Pattern Recognition (CVPR).   
Anisha Gunjal, Jihan Yin, and Erhan Bas. 2023. Detecting and preventing hallucinations in large vision language models. arXiv preprint arXiv:2308.06394.   
Yifan Li, Yifan Du, Kun Zhou, Jinpeng Wang, Wayne Xin Zhao, and Ji-Rong Wen. 2023. Evaluating object hallucination in large vision-language models. arXiv preprint arXiv:2305.10355.   
Tsung-Yi Lin, Michael Maire, Serge Belongie, James Hays, Pietro Perona, Deva Ramanan, Piotr Dollár, and C Lawrence Zitnick. 2014. Microsoft coco: Common objects in context. In European conference on computer vision, pages 740–755. Springer.   
Fuxiao Liu, Tianrui Guan, Zongxia Li, Lichang Chen, Yaser Yacoob, Dinesh Manocha, and Tianyi Zhou. 2023a. Hallusionbench: You see what you think? or you think what you see? an image-context reasoning benchmark challenging for gpt-4v (ision), llava-1.5, and other multi-modality models. arXiv preprint arXiv:2310.14566.   
Fuxiao Liu, Kevin Lin, Linjie Li, Jianfeng Wang, Yaser Yacoob, and Lijuan Wang. 2023b. Aligning large multi-modal model with robust instruction tuning. arXiv preprint arXiv:2306.14565.   
Haotian Liu, Chunyuan Li, Yuheng Li, and Yong Jae Lee. 2023c. Improved baselines with visual instruction tuning. arXiv preprint arXiv:2310.03744.   
Haotian Liu, Chunyuan Li, Qingyang Wu, and Yong Jae Lee. 2023d. Visual instruction tuning. arXiv preprint arXiv:2304.08485.   
OpenAI. 2023. Gpt-4 technical report. ArXiv, abs/2303.08774.   
Anna Rohrbach, Lisa Anne Hendricks, Kaylee Burns, Trevor Darrell, and Kate Saenko. 2018. Object hallucination in image captioning. arXiv preprint arXiv:1809.02156.   
Hugo Touvron, Thibaut Lavril, Gautier Izacard, Xavier Martinet, Marie-Anne Lachaux, Timothée Lacroix, Baptiste Rozière, Naman Goyal, Eric Hambro, Faisal Azhar, et al. 2023a. Llama: Open and efficient foundation language models. arXiv preprint arXiv:2302.13971.

Hugo Touvron, Louis Martin, Kevin Stone, Peter Albert, Amjad Almahairi, Yasmine Babaei, Nikolay Bashlykov, Soumya Batra, Prajjwal Bhargava, Shruti Bhosale, et al. 2023b. Llama 2: Open foundation and fine-tuned chat models. arXiv preprint arXiv:2307.09288.

Deyao Zhu, Jun Chen, Xiaoqian Shen, Xiang Li, and Mohamed Elhoseiny. 2023. Minigpt-4: Enhancing vision-language understanding with advanced large language models. arXiv preprint arXiv:2304.10592.

Junyang Wang, Yiyang Zhou, Guohai Xu, Pengcheng Shi, Chenlin Zhao, Haiyang Xu, Qinghao Ye, Ming Yan, Ji Zhang, Jihua Zhu, et al. 2023a. Evaluation and analysis of hallucination in large vision-language models. arXiv preprint arXiv:2308.15126.   
Weihan Wang, Qingsong Lv, Wenmeng Yu, Wenyi Hong, Ji Qi, Yan Wang, Junhui Ji, Zhuoyi Yang, Lei Zhao, Xixuan Song, et al. 2023b. Cogvlm: Visual expert for pretrained language models. arXiv preprint arXiv:2311.03079.   
Yang Wu, Shilong Wang, Hao Yang, Tian Zheng, Hongbo Zhang, Yanyan Zhao, and Bing Qin. 2023. An early evaluation of gpt-4v (ision). arXiv preprint arXiv:2310.16534.   
Hongbin Ye, Tong Liu, Aijia Zhang, Wei Hua, and Weiqiang Jia. 2023a. Cognitive mirage: A review of hallucinations in large language models. arXiv preprint arXiv:2309.06794.   
Qinghao Ye, Haiyang Xu, Guohai Xu, Jiabo Ye, Ming Yan, Yiyang Zhou, Junyang Wang, Anwen Hu, Pengcheng Shi, Yaya Shi, et al. 2023b. mplug-owl: Modularization empowers large language models with multimodality. arXiv preprint arXiv:2304.14178.   
Qinghao Ye, Haiyang Xu, Jiabo Ye, Ming Yan, Haowei Liu, Qi Qian, Ji Zhang, Fei Huang, and Jingren Zhou. 2023c. mplug-owl2: Revolutionizing multi-modal large language model with modality collaboration. arXiv preprint arXiv:2311.04257.   
Shukang Yin, Chaoyou Fu, Sirui Zhao, Tong Xu, Hao Wang, Dianbo Sui, Yunhang Shen, Ke Li, Xing Sun, and Enhong Chen. 2023. Woodpecker: Hallucination correction for multimodal large language models. arXiv preprint arXiv:2310.16045.   
Bohan Zhai, Shijia Yang, Xiangchen Zhao, Chenfeng Xu, Sheng Shen, Dongdi Zhao, Kurt Keutzer, Manling Li, Tan Yan, and Xiangjun Fan. 2023. Halleswitch: Rethinking and controlling object existence hallucinations in large vision language models for detailed caption. arXiv preprint arXiv:2310.01779.   
Kun Zhou, Yutao Zhu, Zhipeng Chen, Wentong Chen, Wayne Xin Zhao, Xu Chen, Yankai Lin, Ji-Rong Wen, and Jiawei Han. 2023a. Don’t make your llm an evaluation benchmark cheater. arXiv preprint arXiv:2311.01964.   
Yiyang Zhou, Chenhang Cui, Jaehong Yoon, Linjun Zhang, Zhun Deng, Chelsea Finn, Mohit Bansal, and Huaxiu Yao. 2023b. Analyzing and mitigating object hallucination in large vision-language models. arXiv preprint arXiv:2310.00754.

# A Appendix

# A.1 Details of AMBER

# A.1.1 Data Source

Our images are sourced from the MS-COCO 2014 (Lin et al., 2014) test set and $\mathrm { U n S p l a s h } ^ { \mathrm { 1 } }$ . It is important to note that the MS-COCO 2014 test set does not include annotations and is therefore not used as training data. UnSplash adopts the Creative Commons Zero (CC0) license, allowing free use anywhere without requiring attribution to the source or author.

# A.1.2 Data Dtatistics

We present the data statistics for AMBER in Table 8. The category column represents the number of different categories present in the data. It is worth mentioning that the resultant annotations cover 337 objects, which is more than 4 times the number of objects in the existing benchmarks (e.g., 80 specific objects in coco).

<html><body><table><tr><td colspan="2">Image</td><td>1004</td></tr><tr><td rowspan="2">Categories</td><td>Object</td><td>337</td></tr><tr><td>Attribute</td><td>350</td></tr><tr><td rowspan="4">Prompt</td><td>Generation</td><td>1004</td></tr><tr><td>Existence</td><td>4924</td></tr><tr><td>Attribute</td><td>7628</td></tr><tr><td>Relation</td><td>1664</td></tr></table></body></html>

Table 8: Data Statistics.

# A.1.3 Object Distribution

In Figure 6, we present the distribution of detectable objects in AMBER. These objects cover 14 major categories, and the distribution of each category is relatively balanced, without a significant long-tail phenomenon. Compared to existing benchmarks, AMBER achieves the first coverage of objects in Nature, Architecture, and Street View. Additionally, in other categories, AMBER achieves maximum extension, such as in the Fruit category, where the annotations support the detection of over a dozen common fruits compared to existing methods that can only detect three types of fruits.

# A.2 MLLMs Respondents

Table 9 shows the detailed configurations of the MLLMs we evaluated. These MLLMs include both early models like LLaVA and recent ones like Qwen-VL. For fairness, we used the official hyperparameters provided for generation for each MLLM. Responses from MLLMs were not truncated based on length. It is worth noting that we did not evaluate MiniGPT-v2 (Chen et al., 2023). This is because MiniGPT-v2 uses tags to control the response format, which we consider unfair as additional information for other models. We plan to evaluate more MLLMs in future work to create a comprehensive list.

![](images/35db0da3ea7711f4346765c724d101e9f722424af03cddce843ae792701cdd75.jpg)  
Figure 6: The distribution of objects in AMBER.

# A.3 Results

# A.3.1 Detailed Results of Attribute Hallucination

We dissected the attribute hallucination of AMBER into three perspectives—state, number, and action, obtaining refined evaluations for attribute hallucination, as presented in Table 10.

In terms of state and number, MLLMs generally perform poorly. discriminative task related to state and number investigate MLLMs’ fine-grained examination of both single and multiple objects. We recommend that data creators focus on the state and number details of objects.

In terms of action, we find that MLLMs generally perform well. It is noteworthy that, in this perspective, the performance of currently available open-source MLLMs is better than that of GPT-4V.

We believe this may be related to the bias in the training data. Existing multi-modal instruction datasets often have richer descriptions of object action, while information about the states and number of objects is relatively scarce. We suggest data creators enhance coverage of more attribute dimensions for the objects.

# A.3.2 Analysis of Hallucination Tendency to Respond Yes or No

For the tendency of discriminative task, we have added the results for mPLUG-Owl, MiniGPT-4, and LLaVA to the Table 12. These MLLMs perform relatively poorly on AMBER. From the results, it can be seen that they exhibit a stronger inclination towards positive responses, with over $30 \%$ being affirmative replies to hallucinationrelated questions, while non-hallucination questions receive less than $5 \%$ negative responses. The excessively strong tendency toward positive responses makes MLLMs highly susceptible to the induction of hallucinations in questions, leading to incorrect replies.

Table 9: The architecture and parameters of MLLMs evaluated by AMBER.   

<html><body><table><tr><td>Model</td><td>Vision Encoder</td><td>#Params.</td><td>Language Model</td><td>#Params.</td></tr><tr><td>LLaVA (Liu et al., 2023d)</td><td>ViT-L/14</td><td>0.4B</td><td>Llama-2-Chat</td><td>7B</td></tr><tr><td>MiniGPT-4 (Zhu et al., 2023)</td><td>ViT-G/14</td><td>2.0B</td><td>Vicuna</td><td>7B&13B</td></tr><tr><td>mPLUG-Owl (Ye et al., 2023b)</td><td>ViT-L/14</td><td>0.4B</td><td>Llama</td><td>7B</td></tr><tr><td>InstructBLIP (Dai et al., 2023)</td><td>ViT-G/14</td><td>2.0B</td><td>Vicuna</td><td>7B</td></tr><tr><td>LLaVA-1.5 (Liu et al., 2023c)</td><td>ViT-L/14-336px</td><td>0.4B</td><td>Vicuna-v1.5</td><td>7B&13B</td></tr><tr><td>Qwen-VL (Bai et al., 2023)</td><td>ViT-G/14</td><td>2.0B</td><td>Qwen</td><td>7B</td></tr><tr><td>CogVLM (Wang et al., 2023b)</td><td>EVA2-CLIP-E</td><td>4.7B</td><td>Vicuna-v1.5</td><td>7B</td></tr><tr><td>mPLUG-Owl2 (Ye et al., 2023c)</td><td>ViT-L/14</td><td>0.4B</td><td>Llama-2-Chat</td><td>7B</td></tr><tr><td>GPT-4V (OpenAI, 2023)</td><td>Unknown</td><td>Unknown</td><td>GPT-4</td><td>Unknown</td></tr></table></body></html>

<html><body><table><tr><td rowspan="2">Model</td><td colspan="4">STATE</td><td colspan="4">NUMBER</td><td colspan="4">ACTION</td></tr><tr><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td></tr><tr><td>mPLUG-Owl</td><td>57.2</td><td>86.4</td><td>17.1</td><td>28.5</td><td>50.5</td><td>92.3</td><td>1.2</td><td>2.4</td><td>60.0</td><td>92.5</td><td>21.7</td><td>35.2</td></tr><tr><td>LLaVA</td><td>64.9</td><td>76.2</td><td>43.5</td><td>55.4</td><td>57.1</td><td>91.6</td><td>15.7</td><td>26.8</td><td>65.3</td><td>87.1</td><td>35.9</td><td>50.8</td></tr><tr><td>MiniGPT-4</td><td>67.8</td><td>84.7</td><td>43.3</td><td>57.3</td><td>49.9</td><td>48.7</td><td>3.8</td><td>7.0</td><td>56.3</td><td>85.7</td><td>15.2</td><td>25.8</td></tr><tr><td>CogVLM</td><td>64.7</td><td>76.7</td><td>42.1</td><td>54.4</td><td>64.6</td><td>79.8</td><td>39.0</td><td>52.4</td><td>85.1</td><td>91.4</td><td>77.5</td><td>83.9</td></tr><tr><td>LLaVA-1.5</td><td>68.5</td><td>86.6</td><td>43.8</td><td>58.2</td><td>75.4</td><td>87.7</td><td>59.1</td><td>70.6</td><td>84.1</td><td>93.8</td><td>73.0</td><td>82.1</td></tr><tr><td>mPLUG-Owl2</td><td>75.2</td><td>87.4</td><td>59.4</td><td>70.5</td><td>76.6</td><td>89.1</td><td>60.6</td><td>72.1</td><td>85.6</td><td>94.0</td><td>76.3</td><td>84.1</td></tr><tr><td>InstructBLIP</td><td>76.0</td><td>73.5</td><td>81.4</td><td>77.2</td><td>72.4</td><td>78.9</td><td>61.2</td><td>68.9</td><td>86.7</td><td>85.4</td><td>88.6</td><td>87.0</td></tr><tr><td>Qwen-VL</td><td>79.3</td><td>81.7</td><td>75.4</td><td>78.4</td><td>85.7</td><td>86.1</td><td>85.1</td><td>85.6</td><td>87.8</td><td>88.8</td><td>86.4</td><td>87.6</td></tr><tr><td>GPT-4V</td><td>76.5</td><td>73.1</td><td>88.9</td><td>79.1</td><td>88.4</td><td>85.6</td><td>92.5</td><td>88.9</td><td>83.0</td><td>76.4</td><td>95.5</td><td>84.9</td></tr></table></body></html>

Table 10: The detailed evaluation results of AMBER on attribute hallucination.

# Preference for Hallucinatory Objects

Figure 7 shows the preferences of multiple MLLMs in generating hallucinatory objects on generative task. It can be observed that each MLLM has its own preferences, for example, open-source MLLMs tend to generate hallucinations of people, while GPT-4V tends to hallucinate background objects such as the sky, sun, and clouds. We believe that the generation of hallucinations is closely related to the context of the images. Hallucinatory objects are often influenced by certain objects in the images, and errors occur during the embedding of visual modalities into the text modality.

In addition, we conducted an analysis of the correlation between people and the objects that various MLLMs are prone to hallucinate. We selected data containing people or instances where MLLMs generated hallucinations of humans and then counted the objects most frequently hallucinated by MLLMs in this data. The results are shown in Figure 8. It can be observed that objects such as boat, backpack, bench, etc., become the primary hallucinatory objects, and the occurrence of these objects is often related to humans. This once again confirms that the appearance of hallucinatory objects is influenced by the current scene in the image.

# A.3.3 Ablation Results

In Section 4.5, we explored several factors that may influence hallucinations. Due to space constraints,

Table 11: Results of the ablation experiment for complete metrics.   

<html><body><table><tr><td rowspan="2">Model</td><td colspan="4">GENERATIVE TASK</td><td colspan="4">DISCRIMINATIVE TASK</td><td rowspan="2">AMBER Score</td></tr><tr><td>CHAIR↓</td><td>Cover↑</td><td>Hal↓</td><td>Cog↓</td><td>Acc.</td><td>P.</td><td>R.</td><td>F1</td></tr><tr><td>MiniGPT-47B</td><td>13.6</td><td>63.0</td><td>65.3</td><td>11.3</td><td>63.6</td><td>90.5</td><td>50.4</td><td>64.7</td><td>75.6</td></tr><tr><td>MiniGPT-4 13B</td><td>14.3</td><td>63.6</td><td>60.3</td><td>9.6</td><td>64.9</td><td>90.1</td><td>52.9</td><td>66.7</td><td>76.2</td></tr><tr><td>LLaVA-1.57B</td><td>7.8</td><td>51.0</td><td>36.4</td><td>4.2</td><td>72.0</td><td>93.2</td><td>62.4</td><td>74.7</td><td>83.5</td></tr><tr><td>LLava-1.5 13B</td><td>7.0</td><td>51.8</td><td>33.1</td><td>3.3</td><td>72.2</td><td>96.0</td><td>59.0</td><td>73.1</td><td>83.1</td></tr><tr><td>MiniGPT-v2 stage2</td><td>35.0</td><td>22.1</td><td>57.6</td><td>8.3</td><td>37.5</td><td>72.3</td><td>9.4</td><td>16.6</td><td>40.8</td></tr><tr><td>MiniGPT-v2 stage3</td><td>32.8</td><td>31.6</td><td>67.4</td><td>10.4</td><td>36.2</td><td>74.2</td><td>5.8</td><td>10.8</td><td>39.0</td></tr></table></body></html>

![](images/410e76e81ac73a7d64b7437089d7c2d313b5e6522edde74d59aae3eee0fb2f70.jpg)  
Figure 7: Statistics of the top 20 hallucinatory objects of various MLLMs.

<html><body><table><tr><td rowspan="2">Model</td><td colspan="2">Yes Ratio</td><td colspan="2">No Ratio</td></tr><tr><td>All.</td><td>w/ Hal.</td><td>All. </td><td>w/o Hal.</td></tr><tr><td>mPLUG-Owl</td><td>92.4</td><td>43.3</td><td>7.6</td><td>0.9</td></tr><tr><td>LLaVA</td><td>77.7</td><td>32.4</td><td>22.3</td><td>4.7</td></tr><tr><td>MiniGPT-4</td><td>82.1</td><td>35.2</td><td>17.9</td><td>3.1</td></tr><tr><td>CogVLM</td><td>71.8</td><td>27.5</td><td>28.2</td><td>5.7</td></tr><tr><td>LLaVA-1.5</td><td>71.0</td><td>24.5</td><td>29.0</td><td>3.5</td></tr><tr><td>mPLUG-Owl2</td><td>69.6</td><td>22.3</td><td>28.2</td><td>4.3</td></tr><tr><td>InstructBLIP</td><td>49.5</td><td>11.7</td><td>50.5</td><td>12.2</td></tr><tr><td>Qwen-VL</td><td>41.8</td><td>12.2</td><td>58.2</td><td>20.5</td></tr><tr><td>GPT-4V</td><td>39.7</td><td>4.7</td><td>60.3</td><td>15.0</td></tr></table></body></html>

Table 12: The ratio of MLLMs’ response with “Yes” or “No” on discriminative task, where w/ Hal and w/o Hal represent the ground truth for queries with “Yes” and “No”, respectively.

![](images/b2529ee41a9c58b16dded4abcdd57f3838369f3def66467bef52408bf65c91de.jpg)  
Figure 8: Statistics of the top 5 hallucinatory objects most frequently generated by various MLLMs when the object “person” is present.

we only provided results for some indicators. In this subsection, we will delve into a more compre

hensive analysis of the complete results.

# The Scales of LLMs

The results are shown in Table 11. For MiniGPT4, the increase in the parameters of LLM led to an increase in hallucinations on generative task. However, it also exhibited superior performance in terms of object coverage and the proportion of hallucination occurrences. Overall, there was a certain improvement in generative quality without a significant increase in hallucinations. on discriminative task, MiniGPT-4 achieved comprehensive improvements.

For LLaVA-1.5, increasing the parameters of LLM resulted in a comprehensive reduction of hallucinations on generative task, and at the same time, entity coverage also improved. It is evident that the size of LLM has a significant impact on generative task. However, on discriminative task, the 13B model performed poorly. This could be because discriminative task have lower require

Describe this image. Describe this image.   
S The image depicts two cows in what appears to bea pasture with grass. The cow on the left is lying down on the grass, while the cow S This image featuresa mountain bike at the forefront,parked on the side of a gravel path or road. The bike hasa red seat and appears to on the right is standing and facing towards the camera.Both cows bewell-used,with visiblesuspensionand a waterbottle holder haveasimilar light brownand whitecoloration.The background Beyond the bike, in the middle distance, there are at least two zebras suggestsasunnydaywithaclearbluesky,andthevegetation wandering or grazing ina natural setting with rolling hills in the indicatesan open field, which isanatural environment for cattle. background.Thelandscape looksarid, with sparsevegetation, Theimagehasacasual,peacefulseting typicalofarurallandscape indicative ofa savannahorsemi-desert ecosystem.The $\mathbf { s k y }$ is with livestock. mostly clear with some scattered clouds.The entire scene suggests a setting common to regions suchas parts ofAfrica wherewildlife roams freelyand where mountain bikingmight bea leisure or exploratoryactivity. Describe this image. Theimage showstwo distinct species ofAfricanwildlife in what Describe this image.   
你 appears to bea savanna or grassland habitat.On the left side of the frame isa zebra,recognizableby itsdistinctiveblack-and-white The image depicts a modern bathroom.There isa full-sized bathtub striped pattern that covers itsbody.The zebra is grazing on the grass, S ontheright witha green sponge insideand silver fixtures, indicating its head lowered to the ground. On the right side of the image is an where the hot and cold water can be manipulated.To the left of the antelope,assuggestedbyitsoverallbodyshape,longface,andthe bathtub,there isatoiletwiththe lidclosed,anddirectlyahead there visible elongated horns.Theantelope is facing toward the camera, is a modest-sized pedestal sink with asilver faucet,matching the and we cansee its profile.Additionally,a small whitebird,which design of the bathtub's fixtures.Above the sink hangsa small mirror could be some species of egret, is also present on the ground near with rounded corners,and there'sa frosted glass window offering the zebra.The background features an open grassy area with sparse natural light while ensuring privacy. The wallsand floor are tiled in shrubberyandafewtrees,indicativeofanatural, wild landscape. a glossy cream-coloredtile,reflectingthe light and contributing to a The sky is partly cloudy. cleanand bright aesthetic. The bathroomappearswell-keptand tidy.

Figure 9: Some examples from AMBER that cause GPT-4V to generate hallucinations. The highlight fonts represent the hallucinations. The red fonts and blue fonts represent what AMBER detected w/ and w/o hallucinations, respectively.

ments for the capabilities of LLM compared to generative task, so increasing the parameter count did not lead to an improvement in performance.

# Training Data

In Table 11, we present the results of the training data ablation experiment on MiniGPT-v2 for complete metrics. The trends observed in Hal and Cog align with the analysis presented in the main text.

# A.3.4 Cases of Hallucination in GPT-4V

Figure 9 shows some examples of GPT-4V generating hallucinations with the evaluation of AMBER. From the cases, it can be observed that even GPT4V exhibits absurd illusions in AMBER. And we can see that AMBER successfully and accurately detects the hallucinations.