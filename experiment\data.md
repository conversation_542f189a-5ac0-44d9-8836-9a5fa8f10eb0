# AMBER (`junyangwang0410`) 数据集构建方法可行性分析

## 1. AMBER 数据集构建方法深度解析

在详细分析了 `junyangwang0410/AMBER` 项目的`data`目录下的 `annotations.json` 和各种 `query_*.json` 文件后，我们可以总结出其数据集的构建逻辑：

-   **核心思想**: 将对 MLLM 的评估转化为一系列的 **"关键词检测"** 和 **"封闭式问答"**。它不评估模型生成文本的流畅性或连贯性，只关心特定的信息点是否出现。

-   **数据构成**:
    1.  **基础标注 (`annotations.json`)**: 这是评估的"事实基础"。它不包含复杂的句子或段落，而是为每张图片预先标注了两组**关键词列表**：
        -   `"truth"`: 图片中**真实存在**的物体名词（如 `sky`, `person`, `car`）。
        -   `"hallu"`: 图片中**不存在**，但可能被模型误认的物体名词（如 `bird`, `sun`, `dog`）。
    2.  **评估问题 (`query_*.json`)**: 这些文件包含一系列指向特定图片的、高度模板化的问题。这些问题主要分为两类：
        -   **生成式问题 (`query_generative.json`)**: 通常是"请描述这张图片"这样的开放式指令，用于诱导模型生成一段描述。评估时，会检查这段描述中包含了多少`truth`关键词，以及是否包含了`hallu`关键词。
        -   **判别式问题 (`query_discriminative-*.json`)**: 这些是大量的Yes/No问题，用于精准探测模型对某个具体"事实点"的判断能力。例如：
            -   **存在性**: `"Is there a dog in this image?"`
            -   **属性**: `"Is the car red in this image?"`
            -   **关系**: `"Is the person on the bicycle?"`
            -   **数量**: `"Are there three people in this image?"`

-   **评估方法**: LLM-Free (无大语言模型评估)。整个评估过程不依赖一个强大的"裁判模型"，而是通过简单的脚本进行关键词匹配和答案比对，实现自动化、低成本的评估。

## 2. 可行性分析：我们能否基于此进行？

结论是：**AMBER 的数据集构建方法为我们提供了宝贵的思路，但我们不能直接复用或在其上简单修改，而必须进行"范式升级"**。原因如下：

| 对比维度 | AMBER | **我们的 M-FAITH 项目** | **分析与结论** |
| :--- | :--- | :--- | :--- |
| **核心关注点** | **事实性幻觉 (Factual Hallucination)**<br>- "图里有什么？" | **意图幻觉 (Intent Hallucination)**<br>- "你听懂我的话了吗？" | **目标不同，决定了方法不同**。AMBER 关心的是"事实对不对"，而我们关心的是"指令遵没遵守"。一个简单的 Yes/No 问题无法评估模型是否"忽略了不要提及颜色的约束"。 |
| **任务复杂度** | **原子化、简单**<br>所有问题都是关于单个事实点的判断。 | **复合型、复杂**<br>我们的指令包含多个、相互关联的约束（"描述A、不要提B、并说明C"）。 | **复杂度不匹配**。AMBER 的数据是"单点"的，而我们的任务是"多点"的。无法通过组合简单的Yes/No问题来构成一个复杂的意图。 |
| **评估范式** | **关键词匹配 (Keyword Matching)**<br>检查生成的文本是否包含/排除了某些词。 | **语义理解 (Semantic Understanding)**<br>需要一个强大的"裁判"来理解模型回答的**整体语义**是否满足约束。 | **范式不兼容**。例如，对于约束"不要提及颜色"，模型回答"那辆车是交通工具"，AMBER 的方法无法判断这个回答是否满足约束。而我们的"多模态裁判"则可以理解这句话并未提及颜色，从而做出正确判断。 |
| **构建成本** | **较低**。可以通过模板批量生成大量的 Yes/No 问题。 | **较高**。需要创造性地设计包含复杂意图的指令，并提供高质量的参考答案。 | **虽然 AMBER 的方法能节约时间，但它无法构建出能评估我们核心目标的有效数据。** |

## 3. 结论：从 "借鉴思路" 到 "自主构建"

综合来看，直接在 AMBER 数据集上进行修改或扩展是不可行的。这样做会从根本上偏离我们研究"意图幻觉"的核心目标。

但是，AMBER 给了我们一个非常重要的启示：**一个好的评估基准需要将复杂的评估任务分解为可度量的单元。**

-   **AMBER 的分解单元**: 单个事实点（物体、属性、关系）。
-   **我们的分解单元**: 单个意图约束（包含、排除、风格、逻辑等）。

因此，我们的正确路径是：

1.  **借鉴其"分解"思想**: 确认将复杂指令分解为原子化的`意图约束`是正确且可行的。
2.  **升级其"构建"方法**: 放弃其基于"事实点"和"关键词"的构建方法，采用我们之前设计的、通过 `experiment/data_generation.py` 脚本利用强大的 Qwen-VL 模型来**创造性地生成包含复杂意图的指令**。
3.  **升级其"评估"范式**: 放弃其"关键词匹配"的评估方法，采用我们设计的、基于"多模态裁判"的**深度语义理解评估方案**。

**总而言之，为了确保研究的创新性和有效性，我们必须坚持自主构建数据集的道路。`experiment/data_generation.py` 脚本正是实现这一目标最高效、最正确的工具。**

第一步：下载COCO官方的"图片目录"（标注文件）
这个文件就像一个电话本，记录了COCO数据集中所有图片的信息（ID、文件名、下载地址等），但它本身不包含图片，所以文件很小，下载很快。
访问COCO官网下载页面：
请在浏览器中打开这个地址：https://cocodataset.org/#download
找到并下载标注文件：
在页面中找到 "Annotations" (标注) 部分。我们需要的是验证集（Validation set）的标注，因为它规模适中，适合我们的研究。
请点击并下载 2017 Val images 对应的标注文件。
您会得到一个名为 annotations_trainval2017.zip 的压缩包。
第二步：解压并获取我们需要的核心文件
解压文件：
将您刚刚下载的 annotations_trainval2017.zip 文件解压到您电脑的任意位置。例如，您可以解压到 D:\datasets\。
找到核心文件：
解压后，您会得到一个 annotations 文件夹。我们需要的文件就在里面，它的名字是：
instances_val2017.json
这个文件就是我们的"图片目录"。

第三步：配置我们的Python脚本
现在，我们只需要告诉 data_generation.py 脚本这个"图片目录"放在哪里。
复制文件路径：
找到您刚刚解压得到的 instances_val2017.json 文件。
在文件管理器中，复制这个文件的完整路径。
在Windows上，您可以按住 Shift 键，然后右键点击文件，选择"复制为路径"。
得到的路径可能像这样："D:\datasets\annotations\instances_val2017.json"
修改脚本配置：
打开我们项目中的 experiment/data_generation.py 文件。
找到下面这行代码：
Apply to evaluation改进...
将 "PATH_TO_YOUR/annotations/instances_val2017.json" 这部分，替换为您刚刚复制的完整路径。
修改后可能看起来像这样（请确保路径中的反斜杠 \ 没有问题，如果报错可以尝试改成正斜杠 /）：
Apply to evaluation改进...

第四步：完成！开始运行
做完以上三步，所有的准备工作就全部完成了！
现在您可以直接在终端运行我们的脚本了：
Apply to evaluation改进...
Run
脚本会自动读取 instances_val2017.json，从中挑选图片，然后通过网络URL去请求模型，完全不需要您再下载任何一张图片。
这个方案是不是很清晰？请您现在就动手尝试一下，如果在哪一步卡住了，随时告诉我。

### 5.3 计算资源
- GPU服务器用于图像处理
- API调用额度预算
- 数据存储空间预算 

## 六、生成模型策略（未来优化）

### 6.1 当前挑战
在使用 `Qwen/Qwen2.5-VL-7B-Instruct` 模型进行初步数据生成时，观察到该模型在处理复杂JSON格式输出时存在不稳定性，会导致一定比例的API调用失败（由于生成了无效的JSON）。这在进行大规模数据生成时，可能会影响效率并增加成本。

### 6.2 优化方案 (方案A)
为了保证数据生成的高可靠性和高质量，未来在进行大规模（如1000+样本）生成时，推荐将数据生成脚本中的核心模型切换为遵循指令能力更强的模型，例如：
-   **OpenAI `gpt-4o`**
-   **Anthropic `claude-3-5-sonnet`**

**优势**:
-   **高可靠性**: 这类顶级模型能够更稳定、准确地生成符合我们严格要求的JSON结构，将失败率降至最低。
-   **高效率**: 显著减少因模型错误导致的API重试和样本丢弃，长期来看更经济高效。
-   **潜在更高质量**: 更强的模型可能创造出更复杂、更巧妙的指令和约束，进一步提升基准的评估能力。