# EVALALIGN: SUPERVISED FINE-TUNING MULTIMODAL LLMS WITH HUMAN-ALIGNED DATA FOR EVALUATING TEXT-TO-IMAGE MODELS

Zhiyu Tan1 Xiaomeng Yang2 Luozheng Qin2 Mengping Yang2 Cheng Zhang3 Hao Li1

1 Fudan University 2 Shanghai Academy of AI for Science 3 Carnegie Mellon University

https://sais-fuxi.github.io/projects/evalalign

# ABSTRACT

The recent advancements in text-to-image generative models have been remarkable. Yet, the field suffers from a lack of evaluation metrics that accurately reflect the performance of these models, particularly lacking fine-grained metrics that can guide the optimization of the models. In this paper, we propose EVALALIGN, a metric characterized by its accuracy, stability, and fine granularity. Our approach leverages the capabilities of Multimodal Large Language Models (MLLMs) pretrained on extensive data. We develop evaluation protocols that focus on two key dimensions: image faithfulness and text-image alignment. Each protocol comprises a set of detailed, fine-grained instructions linked to specific scoring options, enabling precise manual scoring of the generated images. We supervised fine-tune (SFT) the MLLM to align with human evaluative judgments, resulting in a robust evaluation model. Our evaluation across 24 text-to-image generation models demonstrate that EVALALIGN not only provides superior metric stability but also aligns more closely with human preferences than existing metrics, confirming its effectiveness and utility in model assessment.

# 1 INTRODUCTION

Text-to-image models, such as DALL·E series (Ramesh et al., 2022; Betker et al., 2023), Imagen (Saharia et al., 2022), and Stable Diffusion (Podell et al., 2023), have significantly impacted various domains such as entertainment, design, and education, by enabling high-quality image generation. These technologies not only advance the field of text-to-image generation but also bloom applications such as video generation (Blattmann et al., 2023; Zhang et al., 2023d; Tan et al., 2024b), image editing (Song et al., 2021; Huang et al., 2023b; Zhang et al., 2023c) and human image generation (Wang et al., 2024). Despite achieving incredible progress, the evaluation methods in this area are far from flawless and suffer heavily from data bias, as they are mainly trained on real images but are employed to evaluate synthesized images.

Since human-based evaluations are considerably costly in money and time, existing evaluation methods are primarily based on pretrained models, which are trained on real images. However, the trained real images are generated by humans and high in image faithfulness and text-image alignment because of their generation essence. Meanwhile, the evaluated images are synthesized by text-to-image models and encounter problems such as low image faithfulness or text-image alignment, constrained by the performance of generative models.

We dub the gap between the training data and the evaluated data as data bias, which may cause the evaluation models perform ill-suited on text-to-image evaluation. Because of the data bias, existing text-to-image evaluation methods performs poorly in synthesized image evaluations. Unfortunately, during our preliminary observation, nearly every synthesized images contain visual elements with low image faithfulness or text-image alignment, emphasize their significance on evaluation performance. Notably, there are also some works such as HPSv2 (Wu et al., 2023b) and PickScore Kirstain et al. (2024), where their evaluation models are trained synthesized images. However, in their evaluation settings, the utilized synthesized images are treated as real images as they don’t explicitly recognize the problem of synthesized images with low image faithfulness.

In view of these issues, we propose EVALALIGN, a comprehensive, fine-grained and interpretable metric on text-to-image model assessing with low cost but high accuracy. To build EVALALIGN, we first curate a dataset composed of fine-grained human feedback scores on synthesized images, with consideration of the corresponding prompts. The granularity of the feedback covers 11 skills categorized into two aspects: image faithfulness and text-image alignment. After that, we Supervised finetune (SFT) a Multimodal Large Language Model (MLLM) on the annotated dataset, aligning it with human prior on detailed and accurate text-to-image evaluation.

Owing to extensive pre-training and large model capacity, MLLMs demonstrate excellent image-text understanding and generalization capabilities. However, since the pre-training data does not include synthesized images with low image faithfulness or evaluation-related text instructions, using MLLMs directly for model evaluation may yield non-optimal results. Especially, we want to use MLLMs to support comprehensive and detailed evaluations, encompassing 11 skills and 2 aspects. The definitions and nuances of these may not be fully understood by the MLLM. Therefore, we employ SFT on a small amount of high-quality annotated data to align the MLLM with human judgement on evaluating synthesized images in criteria of 11 skills and 2 aspects. Notably, since the main intelligence of EVALALIGN stems from the annotated dataset and the utilized MLLM, we will make them accessible to the public.

In summary, our main contributions can be summarized as follows:

• We build a detailed human feedback dataset specifically designed to address the aforementioned challenges of text-to-image model evaluations. The annotated dataset is thoroughly cleaned, carefully balanced in topics, and systematically annotated by human. The dataset is composed by fine-grained human prior on evaluating synthesized images in criteria of 11 skills and 2 aspects. • We propose EVALALIGN, a text-to-image evaluation method which accurately aligns evaluation models with fine-grained human prior using the annotated dataset. EVALALIGN exclusively supports an accurate, comprehensive, fine-grained and interpretable text-to-image evaluations. Besides EVALALIGN is cost-effective in terms of annotation and training and computationally efficient. • With EVALALIGN, we conduct evaluations over 24 text-to-image models and compare EVALALIGN with existing evaluation methods. Quantitative and qualitative experiments demonstrate that EVALALIGN outperforms other methods in evaluating model performance.

# 2 RELATED WORK

# 2.1 BENCHMARKS OF TEXT-TO-IMAGE GENERATION

Despite the incredible progress achieved by text-to-image generation Zhang et al. (2023a); Tan et al. (2024a), evaluations and benchmarks in this area are far from flawless and contain critical limitations. For example, the most commonly used metrics, IS (Salimans et al., 2016), FID (Heusel et al., 2017), and CLIPScore (Hessel et al., 2021) are broadly recognized as inaccurate for their inconsistency with human perception. To address, HPS series (Wu et al., 2023b;a), PickScore (Kirstain et al., 2024), and ImageReward (Xu et al., 2024) introduced human preference prior on image assessing to the benchmark, thereby allowing better correlation with image quality. However, with varying source and size of training data, these methods merely score the evaluated images in a coarse and general way, which cannot serve as an indication for model evolution. Meanwhile, HEIM (Lee et al., 2024) combined automatic and human evaluation and holistically evaluated text-to-image generation in 12 aspects, such as alignment, toxicity, and so on. As a consequence, HEIM relies heavily on human labour, limiting its application within budget-limited research groups severely. Otani et al. (2023) standardized the protocol and settings of human evaluation, ensuring its verifiable and reproducible. Considering the issues of existing benchmarks, we propose EVALALIGN to offer a cost-efficient, comprehensive and fine-grained text-to-image model evaluation. Through our observations, we found that image faithfulness and text-image alignment are two key factors for comprehensive evaluation. Image faithfulness requires the model to generate visual elements that are consistently faithful to the real-world. For example, visual elements such as distorted body. Meanwhile, text-image alignment measures how the generated images are aligned with their corresponding prompts.

Table 1: Comparison of different evaluation metrics and frameworks for text-to-image generation. EVALALIGN focuses on two key evaluation aspects, i.e., image faithfulness and text-image alignment, and supports human-aligned, fine-grained, and automatic evaluations. P: Prompt. I: Image. A: Annotation.   

<html><body><table><tr><td rowspan="2">Method</td><td rowspan="2">Venue</td><td colspan="3">Benchmark Feature</td><td colspan="3">Dataset Size</td><td colspan="3">Evaluation Aspect</td></tr><tr><td>Human-aligned Fine-grained Automatic</td><td></td><td></td><td>P</td><td>I</td><td>A</td><td>Faithfulness</td><td></td><td>Alignment</td></tr><tr><td>Inception Score (Salimans et al., 2016)</td><td>NeurIPS 2016</td><td>×</td><td>X</td><td>√</td><td>1</td><td>1.3M</td><td>1</td><td></td><td></td><td>×</td></tr><tr><td>FID (Heusel et al., 2017)</td><td>NeurIPS 2017</td><td>×</td><td></td><td>√</td><td>一</td><td>1.3M</td><td>1</td><td></td><td>//</td><td>×</td></tr><tr><td>CLIP-score (Hessel et al., 2021)</td><td>EMNLP2021</td><td>×</td><td></td><td>√</td><td>400M</td><td>400M</td><td></td><td></td><td>X</td><td>√</td></tr><tr><td>HPS (Wu et al., 2023b)</td><td>ICCV 2023</td><td>√</td><td>×</td><td>√</td><td>25K</td><td>98K</td><td>25K</td><td></td><td>1</td><td></td></tr><tr><td>TIFA (Hu et al., 2023)</td><td>ICCV2023</td><td>√</td><td>√</td><td>√</td><td>4K</td><td>二</td><td>25K</td><td></td><td>×</td><td>-/</td></tr><tr><td>TVRHE (Otani et al., 2023)</td><td>CVPR2023</td><td>√</td><td>X</td><td>×</td><td></td><td>二</td><td></td><td></td><td>√</td><td>×</td></tr><tr><td>ImageReward (Xu etal.,2024)</td><td>NeurIPS 2023</td><td>√</td><td>×</td><td>√</td><td>8.8K</td><td>68K</td><td>137K</td><td></td><td>1</td><td>1</td></tr><tr><td>PickScore (Kirstain etal., 2024)</td><td>NeurIPS 2023</td><td>√</td><td>×</td><td></td><td>35K</td><td>1M</td><td>500K</td><td></td><td>1</td><td>1</td></tr><tr><td>HPS v2 (Wu et al., 2023a)</td><td>arXiv 2023</td><td>√</td><td>X</td><td></td><td>107K</td><td>430K</td><td>645K</td><td></td><td>二</td><td></td></tr><tr><td>HEIM (Lee et al.,2024)</td><td>NeurIPS 2023</td><td>√</td><td>√</td><td>×</td><td>二</td><td>1</td><td></td><td></td><td>√</td><td>√</td></tr><tr><td>Gecko (Wiles et al.,2024)</td><td>arXiv 2024</td><td>√</td><td>√</td><td>√</td><td>2K</td><td>1</td><td>108K</td><td></td><td>×</td><td>√</td></tr><tr><td>LLMScore (Lu etal., 2024)</td><td>arXiv 2024</td><td>√</td><td>√</td><td>√</td><td>1</td><td>1</td><td>1</td><td></td><td>×</td><td>√</td></tr><tr><td>EVALALIGN (ours)</td><td>1</td><td>√</td><td>√</td><td>√</td><td>3K</td><td>21K</td><td>132K|</td><td></td><td>√</td><td>√</td></tr></table></body></html>

There are also some works bear a resemble with us. For instance, TIFA (Hu et al., 2023), Gecko (Wiles et al., 2024) and LLMScore (Lu et al., 2024) also formulate the evaluation as a set of visual question answering procedure and use LLMs as evaluation models. However, while they all mainly focus on text-image alignment, our approach takes both text-image alignment and image faithfulness into consideration. Moreover, the evaluation of LLMScore requires an object detection stage, which introduces significantly extra inference latency to the evaluation pipeline.

As illustrated in Table 1, existing text-to-image evaluation methods contains various limitations, making them incapable to serve as a fine-grained, comprehensive, and human-preference aligned automatic benchmark. While our work fills in this gap economically, and can be employed to indicate evolution direction and support thorough analysis of text-to-image generation models.

# 2.2 MULTIMODAL LARGE LANGUAGE MODELS (MLLMS)

Pre-trained on massive text-only and image-text data, MLLMs have exhibited exceptional image-text joint understanding and generalization abilities, facilitating a large spectrum of downstream applications. Among the works major in MLLMs, LLaVA (Liu et al., 2024b; 2023) and MiniGPT4 (Zhu et al., 2023; Chen et al., 2023a) observed that multimodal SFT is sufficient to align MLLMs with human preferences and enable them to accurately answer fine-grained questions about visual content. Besides, Video-LLaMA (Zhang et al., 2023b) and VideoChat (Li et al., 2023) utilized MLLMs for video understanding. VILA (Lin et al., 2023) quantitatively proved that involving text-only instruction-tuning data during SFT can further ameliorate model performance on text-only and multimodal downstream tasks. LLaVA-NeXT (Liu et al., 2024a) extracted visual tokens for both the resized input image and the segmented sub-images to provide more detailed visual information for MLLMs, achieving significant performance bonus on tasks with high-resolution input images.

However, due to the data bias, existing MLLMs cannot perfectly quantify for text-to-image evaluations. Thus, we meticulously curate a SFT dataset to align MLLMs with detailed human feedback on synthesized images.

# 3 EVALALIGN DATASET CONSTRUCTION

To train, validate and test the effectiveness of our evaluation models, we build EVALALIGN dataset. Specifically, EVALALIGN dataset is a meticulously annotated collection featuring fine-grained annotations for images generated on text conditions. This dataset comprises 21k images, each accompanied by detailed instructions. The compilation process for the EVALALIGN Dataset encompasses prompt collection, image generation, and precise instruction-based annotation.

# 3.1 PROMPTS AND IMAGES COLLECTION

Prompt collection. To assess the capabilities of our model in terms of image faithfulness and textimage alignment, we collect, filter, and clean prompts from existing evaluation datasets and generated

A Data Collection $\textcircled{8}$ Data Annotation $\circledcirc$ SFT Multimodal LLM Prompt Collection Prompt Annotation Multimodal Large Language Model 自 Annotations: Mid ensource yAelwlowmsahn rtwecarirnyignag a Y "Coluonrt"": ""ywelolomwans:hiart;bgraeceknpabcakc:kap;a",ck;", ↑ ipQnrc1el:suDedonetdes.dt1hi,neStgohivemecenoirrmbejaesgcpteosncadorinentgaminipsrasolilnmtgph.tes2,?obAjllencsotbwsj(ewrc:to0s,maNreno insnehcilorutb,djbeacdtc.skapraeck) GPT- User Ge erated her head and smiled at "Spatial": "none", dQi2st:oArtrieotnh,earseyamnmy iestrsiucealsfawcitehs,haubmnaonrmfalcfeaicniatlhfe iatmuargese, usuncuhsuasl facial the camera "Action": "turned her head" ressions in the eyes, etc? Options: -1.There is no face of any person ↓ rasa naimvearlyingrtihev opiucstupreo,b0l.eTmh ethfatcies  oufntbhearpaebrlseo,n1.oTrhaenifamcael ionftthe piecrtsuroen Prompt Curation rccaeniptmabl ien, t2h.eThpeicftaucre ohfatshseopmeerssoenr orusanpirmoabl ienmtsheanpdicitsurneothas a slight Image Annotation aprniomblaelmintthaet pdiocetusrneoits  abffaesictatlhlyefisnen,sweist,h3.oTnlhyeafafceewoflfatwhes p4e.rTshoenfaorce of rFeillteevraingceptromevpatlsubataisoendtaosnk Image aQ1hu:uDemosaetnsiothanensid:m?a0g.neoco1.nyteaisn m A1n:n0o,tNaot.ions: tAh2e:5ap.neDrseswfoienri tioesrlyanNiomal in the picture is completely fine and close to reality, a human face? 0.no 1.yes A2:1,Yes. PrAomAwpowt:mo aQ3h:uDmoaens tbhoediym?a0g.enoco1.nyteaisn A3:1,Yes. D Evaluation A yweoyllmeollawonwswhsierhta rctiancrgrayrairn yelalogawrgesreheniertbncabacarkrcpykiapncagkc Faithfulness Q3: Are there any issues taurgtnruetrsunedmrenihldeberhdaecharketephartaehdhcaeakdcanad tFhaeithafnudldswn iisetnthsottsrhtheQdif2mfa:acAcgereie?,nt1sth.uhDecerehefimanaisnateygheiyas,vsYisnueugesc,shmwaoisrteh smiled at the camera. Instruction Finetuning Annotation T2I Q1o: rDloesstthe2Na.ngoPi,frvio5ve.benDafieibmfnliyganiegtYres ys?c1oN.3nD.otNaeifointniastlleulrtyeh,eY4e objects(2p.iPtrpoubll)a bplryesYenst,e3d. iNnothseurceo,rr4e.Psrpobnadbi lnygN Image T2I Alignment Questions: Image Faithfulness Questions: prompts5?.DAenfisniwtelry: 0N,oNone objects are included. Q1:Does the given image contain Q1: Are there any issues with human 1, Some objects are missing. 2, All objects are aplrletshentoebdjeicnt st(hweocomrarens,bpaocnkdipnagck) mmetrical fac included. Image Generation oprbojemctpstsa?reAinsclwuedr:ed0,. 1N,oSnoeme aexbpnroersmsiaolnfasciinaltfheateuyres,,eutncu?s1.uDalefi ￥ Yes, 2.Probably Ye objects are missing. 2, All objects 4.Probably No, 5.Definitely No Multimodal Large Language Model are included. + YC ↓ SD1.5 PIXART Annotation:2. All objects are included. Annotation:5.Definitely No T2I A1:2.All objects are included. Faithfulness A2:4.Probably No. EvalAlign Score prompts based on LLM. These prompts encompass a diverse range from real-world user prompts, prompts generated through rule-based templates with LLM, to manually crafted prompts. Specifically, the utilized prompts are soureced from HPS (Wu et al., 2023b), HRS-Bench (Bakr et al., 2023), HPSv2 (Wu et al., 2023a), TIFA (Hu et al., 2023), DSG (Cho et al., 2023a), T2I-Comp (Huang et al., 2023a), Winoground (Thrush et al., 2022), DALL-EVAL (Cho et al., 2023b), DiffusionDB (Wang et al., 2023), PartiPrompts (Yu et al., 2022), DrawBench (Saharia et al., 2022), and JourneryDB (Sun et al., 2024).

Prompt curation. To facilitate a clean and reasonable evaluation, each prompt to be annotated have to instruct text-to-image models to generate images that can reflect model performances on image faithfulness and text-image alignment. However, considering some of the collected prompts fail to achieve the purpose, we need to filter and balance the collected prompts to ensure their quantity, quality and diversity. For image faithfulness evaluation, we prioritize prompts related to human, animals, and other tangible objects, as prompts depicting sci-fi scenarios are less suitable for this type of assessment. Consequently, the prompt filter for image faithfulness initially selects prompts that describe human, animals, and other real objects. After deduplicating these prompts, we carefully select 1,500 distinct prompts with varying topic, background and style. The selected prompts encompass 10k subjects across 15 categories. For text-image alignment evaluation, we refine our selection based on descriptions of style, color, quantity, and spatial relationships in the prompts. Specifically, only prompts contain relevant descriptions and exceed 15 words in length are considered, culminating in a final set of 1,500 prompts.

Image generation. To train and evaluate the MLLM, we use a diverse set of images generated by various models using the aforementioned prompts, facilitating detailed human annotation. For each prompt, multiple images are generated across different models. The models used to generate these images vary in architectures and scales, enhancing the dataset diversity. There are 24 models used to generate these images, varying in architecture as well as scale and thus enhancing the dataset diversity. For detailed information on the generation setting of each model, please refer to the appendix.

The training and validation set comprises synthesized images from 8 out of the 24 models, whereas the test set spans all of them. Particularly, the exclusive inclusion of the 16 models in the test set is crucial for validating the MLLM’s ability to generalize beyond its training data. Through our manual inspection, in this way, we attain ample synthesized images with a balanced diversity in the performance of image faithfulness and text-image alignment.

# 3.2 DATA ANNOTATION

Prompt annotation. For text prompts focused on text-image alignment, we begin by annotating the entities and their attributes within the text, as illustrated in Figure 1. Our annotators extract the entities mentioned in the prompts and label each entity with corresponding attributes, including quantity, color, spatial relationships, and actions. During the annotation, we also ask the annotators to annotate the overall style of the image if described in the corresponding prompt and report prompts that contain toxic and NSFW content. These high-quality and detailed annotations facilitate the subsequent SFT training and evaluation of the MLLM. The prompt annotation procedure ensures that the MLLM can accurately align and respond to the nuanced details specified in the prompts, enhancing both the training process and the model’s performance in generating images that faithfully reflect the described attributes and style.

Image annotation. The images generated by text-to-image models often present challenges such as occluded human body parts, which can impede the effectiveness of SFT training and evaluation of the MLLM. To address these challenges and enhance the model’s training and evaluative capabilities, specific annotations are applied to all images depicting human and animals. These annotations include: presence of human or animal faces; visibility of hands; visibility of limbs. By implementing these annotations, we ensure that the MLLM can more effectively learn from and assess the completeness and faithfulness of the generated images. This structured approach to annotation not only aids in identifying common generation errors but also optimizes the model’s ability to generate more accurate and realistic images, thereby improving both training outcomes and the model’s overall performance in generating coherent and contextually appropriate visual content.

Instruction-finetuning data annotation. To align the MLLM with human preference prior on detailed synthesized image assessing, we can train the model on a minimal amount of fine-grained human feedback data through SFT training. As a consequence, we devise two sets of questions, each is concentrated on a specific fine-grained skill of image faithfulness and image-text alignment. Human annotators are required to answer these questions to acquire the fine-grained human preference data. To aid them to understand the meaning and principle of each question, thereby ensuring high annotation quality, we employ a thorough and comprehensive procedure of annotation preparation. First, we write a detailed annotation guideline and conduct a training for the annotators to explain the annotation guideline and answer their questions about the annotation. Then, we conduct a multi-turn trial annotation on another 50 synthesized images. After each trial, we calculate the Cohen’s kappa coefficient and interpret annotation guidelines for our annotators. In total, we conduct nine turns of trial annotation, and in the last turn of the trial, the Cohen’s kappa coefficient of our annotators reaches 0.681, indicating high inter-annotator reliability and high annotation quality.

After completing the aforementioned preparations, we delegate the images filtered during image annotation to 10 annotators and ask them to complete the annotation just as how they did in the trial annotation. Furthermore, during the whole annotation procedure, four experts in text-to-image generation conduct random sampling quality inspection on the present annotated results, causing a second and a third re-annotation on 423 and 112 inspection-failed samples. Overall, owing to the valuable work of our human annotators and our fastidious annotation procedure, we get qualitysufficient instruction-tuning data required for the SFT training of the MLLM. More details of the annotation procedure will be introduced in supplementary files.

# 3.3 DATASET STATISTICS

To summarize, we generate $2 4 \mathbf { k }$ images from $3 \mathbf { k }$ prompts based on 8 text-to-image models, which includes DeepFloyd IF (Alex Shonenkov & et al., 2023), SD15 (Rombach et al., 2022a), LCM (Luo et al., 2023), SD21 (Rombach et al., 2022a), SDXL (Podell et al., 2023), Wuerstchen (Pernias et al., 2023), Pixart (Chen et al., 2023b), and SDXL-Turbo (Stability AI, a). After data filtering, $4 . 5 \mathrm { k }$ images are selected as annotation data for task of text-image alignment. Subsequently, these images are carefully annotated to generate $1 3 . 5 \mathrm { k }$ text-image pairs, where $1 1 . 4 \mathrm { k }$ are used to the training dataset and 2.1k to the validation dataset. For the image faithfulness task, we select 12k images for annotation, yielding 36k text-image pairs, with $3 0 \mathrm { k }$ are used to the training dataset and $6 . 2 \mathrm { k }$ to the validation dataset. Additionally, we employed 24 text-to-image models to generate $2 . 4 \mathrm { k }$ images from 100 prompts. After annotation, these images are used as testing dataset. Figure 2 and Figure 3 show the distribution of objects in different categories within our prompts, demonstrating the diversity and balance of our prompts.

![](images/ff0adfb35b7daec8bb1249dbe0334828fcef1bc76aa754b4b3873b1658c11c2f.jpg)  
Figure 2: Statistics of prompts on evaluating textto-image alignment. Prompts in our text-to-image alignment benchmark covers a broad range of concepts commonly used in text-to-image generation.

![](images/59eabd373e1a3fd629202e80c22bf1684130b299fcc2b17c437173376961734c.jpg)  
Figure 3: Statistics of prompts on evaluating image faithfulness. Prompts in our image faithfulness benchmark covers a broad range of objects and categories that related to image faithfulnes.

# 4 TRAINING AND EVALUATION METHODS

# 4.1 SUPERVISED FINETUNING THE MLLM

As we mentioned above, we use MLLMs as the evaluation models and let it to answer a set of carefully-designed instructions, thereby achieving quantitative measurement of fine-grained textto-image generation skills. Due to data bias, zero-shot MLLMs perform poorly when it comes to evaluation on generated images, particularly in term of image faithfulness. To solve this problem, we apply SFT training on the detailed human annotation to align the MLLM with human preference prior. Formally, the SFT training sample can be denoted as a triplet: question (or the instruction), multimodal input and answer. During SFT training, the optimization objective is the autoregressive loss function utilized to train LLMs, but calculated only on the answer, the loss function can be formulated as follows:

$$
L ( \theta ) = \sum _ { i = 1 } ^ { N } \log p ( A _ { i } | Q , M , A _ { < i } ; \theta ) ,
$$

where $N$ is the length of the ground truth answer, $Q$ is a fine-grained question of the generated image and its available answer, $M$ is the image and textual description, while $A$ is the human annotated answer selected from the given options. Notably, we expand each option to make it more detailed and descriptive, thereby benefiting SFT performance by allowing the MLLM to better understand the meaning of each option.

# 4.2 EVALUATION AND METRICS

To evaluate synthesized images with consideration of its synthetic nature, EVALALIGN is designed to evaluate image faithfulness and text-image alignment in a fine-grained way. Notably, image faithfulness and text-image alignment are two common errors occurred in synthesized images, whereas real images inherently exhibit high levels of both image faithfulness and text-image alignment.

Image Faithfulness measures whether synthesized images are faithful to real-world commonsense. With higher image faithfulness, the visual elements of generated images more closely resemble their real-world counterparts. Unfortunately, text-to-image models often generate images with low faithfulness, such as distorted body structures and human hands. This is also a critical reason why we set image faithfulness as one of the benchmarking aspects in EVALALIGN. Additionally, evaluating image faithfulness requires considering the input prompts, as prompts may describe unreal or impossible scenarios that inherently affect the faithfulness of the generated images. For example, when prompts like "a dog walking like a human" or "a man on Mars without a spacesuit" are provided, the generated images may naturally deviate from real-world image faithfulness. Under such circumstances, the synthesized images cannot be regarded as low in image faithfulness since the generative models are merely following prompts that contain super-reality scenarios.

Text-Image Alignment evaluates whether generated images are aligned with their conditioned prompts. In the inference settings of text-to-image models, the image generation process is conditioned on textual prompts, requiring alignment between the text prompts and the synthesized images. However, through our observations, text-to-image models cannot consistently follow input prompts, often yielding images with visual elements misaligned with the input prompts. For example, models may generate images featuring an orange cat when conditioned on the text prompt "a blue cat."

During inference, the multimodal large language model (MLLM) is required to generate an appropriate response given a specific question $Q$ and multimodal input $M$ in an autoregressive manner:

$$
R _ { i } = f ( Q , M , R _ { < i } ; \theta ) ,
$$

where $R _ { i }$ is the $i$ -th generated token, $R _ { < i }$ represents the sequence of tokens generated before step $i$ , and $\theta$ denotes the parameters of the fine-tuned MLLM. This autoregressive generation process is considered complete once the model generates an end-of-sequence (EOS) token or the generated response exceeds a preset maximum generation length. After generation, we employ rule-based filtering and regular expressions to extract the option chosen by the MLLM. Each option is assigned a unique predefined score to quantitatively measure a fine-grained skill specified by the question $Q$ :

$$
\operatorname { S c o r e } ( Q ) = g ( R ) = g ( f ( Q , M ; \theta ) ) ,
$$

where $g ( \cdot )$ represents the procedure of option extraction and score mapping.

We devise two holistic and detailed question sets, $S _ { f }$ and $S _ { a }$ , that encompass every aspect of image faithfulness and text-image alignment, respectively. Consequently, our metric, EvalAlign, can be defined by averaging the scores of the questions in the two sets:

$$
\begin{array} { l } { { \displaystyle \mathrm { E v a l A l i g n } _ { \mathrm { f } } = \frac { 1 } { | S _ { f } | } \sum _ { Q _ { i } \in S _ { f } } \mathrm { S c o r e } ( Q _ { i } ) } , } \\ { { \displaystyle \mathrm { E v a l A l i g n } _ { \mathrm { a } } = \frac { 1 } { | S _ { a } | } \sum _ { Q _ { j } \in S _ { a } } \mathrm { S c o r e } ( Q _ { j } ) } , } \end{array}
$$

where EvalAlign and EvalAlign indicate the image faithfulness score and the text-image alignment score evaluated by our method, respectively.

# 4.3 IMPLEMENTATION DETAILS

For details about the SFT training, we apply LoRA (Hu et al., 2021) finetuning on LLaVA-NeXT (Liu et al., 2024a) models to align them with the EVALALIGN dataset. Additionally, we merely adapt LoRA finetuning on the Q and K weights of the attention module, as extending the finetuning to the ViT (Dosovitskiy, 2020) and projection modules will lead to overfitting. The entire training process is conducted on 32 NVIDIA A100 GPUs for 10 hours, with a learning rate of $5 \times 1 0 ^ { - 5 }$ . As for the ablation study, we evaluate the finetuned LLaVA-NeXT 13B model on the validation dataset. In the final experiment, we apply SFT to the LLaVA-NeXT 34B model on the testing dataset to testify its generalization ability.

# 5 EXPERIMENTAL RESULTS

# 5.1 MAIN RESULTS

Evaluation on image faithfulness. We evaluate image faithfulness on the testing dataset to ensure that the finetuned MLLM aligns with human judgment and generalizes to unseen data. As detailed in Table 2, the finetuned MLLM successfully aligns with human preferences on image faithfulness, indicating its ability of image faithfulness evaluation is close to human. Specifically, the rankings of the top and bottom 10 models by both EVALALIGN and human evaluation scores are remarkably consistent. Besides, most of the images in the testing dataset, especially those from the 16 exclusive generative models, are not present during the SFT training, showcasing the robust generalization capability of our models.

Table 2: Results on image faithfulness. We evaluate the image faithfulness of images generated by 24 text-to-image models to compare five evaluation metrics against human scoring results. The experiments show that our metric’s scores align more closely with human evaluations than those of other metrics.   

<html><body><table><tr><td>Model</td><td>Human</td><td>EVALALIGN</td><td>HPS v2</td><td>CLIP-scoreImageReward PickScore</td></tr><tr><td>PixArt XL2 1024 MS (Chen et al.,2023b)</td><td>2.2848</td><td>1.6415</td><td>31.6226 0.8580</td><td>0.9696 22.1335</td></tr><tr><td>DreamlikePhotoreal v2.O (dreamlike.art, b)</td><td>2.0070 2</td><td>1.4522 4</td><td>29.2322 6 0.8286</td><td>12 0.1886 13 21.22718</td></tr><tr><td>SDXL Refiner v1.O (Stability AI, b)</td><td>1.9229 3</td><td>1.6072 2</td><td>29.8197 3 0.8566 2</td><td>0.7245 22.0492 2</td></tr><tr><td>SDXL v1.0 (Podell et al., 2023)</td><td>1.8136 4</td><td>1.4675 3</td><td>29.0620 7 0.8467</td><td>4 0.7043 3 21.8106 3</td></tr><tr><td>Wuerstchen (Pernias et al.,2023)</td><td>1.7837 5</td><td>1.4279 5</td><td>30.6622 2 0.8199</td><td>14 0.3212 11 21.3720 6</td></tr><tr><td>LCM SDXL (Luo et al., 2023)</td><td>1.69106</td><td>1.3391 7</td><td>29.3588 5 0.8335</td><td>10 0.5304 6 21.6532 4</td></tr><tr><td>Openjourney (PromptHero,a)</td><td>1.6667</td><td>1.1750 10</td><td>26.3475 13 0.8196</td><td>15 0.1478 16 20.8637 10</td></tr><tr><td>Safe SD MAX (Patrick et al., 2022)</td><td>1.6491 8</td><td>1.2175 8</td><td>25.7396 17 0.7555 24</td><td>-0.0507 22 20.4594 21</td></tr><tr><td>LCM LORA SDXL (Luo et al., 2023)</td><td>1.6387 9</td><td>1.3833 6</td><td>27.3299 10 0.8364 8</td><td>0.4959 21.4824 5</td></tr><tr><td>Safe SD STRONG (Patrick et al., 2022)</td><td>1.6308 10</td><td>1.1466 11</td><td>25.5764 18 0.8165 18</td><td>-0.1022 23 20.6211 18</td></tr><tr><td>Safe SD MEDIUM (Patrick et al., 2022)</td><td>1.6275 11</td><td>1.1298 15</td><td>26.2798 14 0.8101 20</td><td>0.2042 12 20.7880 12</td></tr><tr><td>Safe SD WEAK (Patrick et al., 2022)</td><td>1.6078 12</td><td>1.1188 17</td><td>26.1180 15 0.7809 23</td><td>-0.1264 24 20.3873 24</td></tr><tr><td>SD v2.1 (Rombach et al.,2022a)</td><td>1.5524 13</td><td>1.1094 18</td><td>26.5823 12 0.8377</td><td>0.4116 21.0502 9</td></tr><tr><td>SD v2.0 (Rombach et al., 2022a)</td><td>1.5277 14</td><td>1.1300 14</td><td>25.3481 21 0.8170 17</td><td>0.0872 18 20.7529 13</td></tr><tr><td>Openjourney v2 (PromptHero,b)</td><td>1.5000 15</td><td>0.9956 20</td><td>24.6984 23 0.7958 22</td><td>-0.0415 21 20.4088 22</td></tr><tr><td>Redshift diffusion (Redshift-Diffusion)</td><td>1.4733 16</td><td>1.1382 12 25.1572</td><td>0.8101 21 22</td><td>0.0218 20 20.6155 19</td></tr><tr><td>Dreamlike Diffusion v1.O (dreamlike.art,a)</td><td>1.4652 17</td><td>1.2052 9 29.6506</td><td>0.8543 3</td><td>0.6508 21.2664</td></tr><tr><td>SD v1.5 (Rombach et al., 2022a)</td><td>1.4417 18</td><td>1.1362 13 25.4972 19</td><td>0.8214 13</td><td>0.1686 14 20.7143 16 14</td></tr><tr><td>IF-I-XL v1.0 (Alex Shonenkov & et al.,2023)</td><td>1.3808 19</td><td>0.9221 22 27.4512</td><td>9 0.8449 5</td><td>0.6087 20.7474</td></tr><tr><td>SD v1.4 (Rombach et al.,2022a)</td><td>1.3592 20</td><td>0.9511 21 25.3697</td><td>20 0.8190 16</td><td>0.1050 17 20.653517</td></tr><tr><td>Vintedois Diffusion v0.1 (Vintedois-Diffusion v0.1) 1.3562 21</td><td></td><td>1.0797 19 26.5901</td><td>11 0.8341 9</td><td>0.3562 10 20.8358 11</td></tr><tr><td>IF-I-L v1.0 (Alex Shonenkov & et al., 2023)</td><td>1.2635 22</td><td>0.8814 23 27.4836</td><td>8 0.8384 6</td><td>0.4463 8 20.7170 15</td></tr><tr><td>MultiFusion (Marco et al.,2023)</td><td>1.2372 23</td><td>1.1298 16</td><td>23.8133 24 0.8151 19</td><td>0.0695 19 20.4780 20</td></tr><tr><td>IF-I-M v1.0 (Alex Shonenkov & et al.,2023)</td><td>1.0135 24</td><td>0.7928 24 25.9522</td><td>16 0.8329 11</td><td>0.1637 15 20.4035 23</td></tr></table></body></html>

Evaluation on text-image alignment. The evaluation of text-image alignment on the testing dataset is similar to that of image faithfulness. Table 2 reveals that the rankings of the 24 evaluated models by EVALALIGN are generally consistent with human annotators. We believe that the consistency on image faithfulness and text-image alignment evaluations mainly stems from our annotated highquality SFT dataset. It also proves that, with the annotated dataset and the extraordinary image-text joint understanding ability owned by MLLMs, we can easily finetune a MLLM to conduct the evaluation with low cost but close-to-human performance.

# 5.2 ABLATIONS AND ANALYSES OF EVALALIGN

Results on different prompt categories. Since MLLMs are not specifically trained to perform evaluations, they are naturally ill-suited for this task, hindering their task performances. Therefore, we need to annotate SFT data for this task and finetune the MLLMs accordingly. To verify the necessity, We conduct experiments comparing the LLava-Next 13B model with and without SFT. As shown in Table 4 and Table 5, the results demonstrate that SFT training considerably improves performance across all prompt categories in both image faithfulness and text-to-image alignment, closely aligning the MLLM’s predictions with human evaluations. Note that Table 4 illustrates that the baseline method without SFT performs poorly in image faithfulness and text-image alignment evaluations, particularly in the former.

Effect of training dataset size for vision-language model training. In order to explore the effects of data size and determine the sufficient amount of training data, we train the model on image faithfulness evaluation task with images and their annotations sourced from 200, 500 and 800 prompts. As illustrated in Table 6, the evaluation performance continuously enhances as more training data is used. Notably, training with just 500 prompts nearly maximizes accuracy, with further increases to 800 data yielding only marginal improvements. This result suggests that our method requires only a small amount of annotated data to achieve good performance, highlighting its cost-effectiveness. Generally, since more data leads to better performance, we use all of the available data to finetune our models and release this data to the research community to bootstrap further study.

Table 3: Results on text-to-image alignment. We evaluated the text-image alignment of images generated by 24 text-to-image models to compare how five evaluation metrics align with human scoring results. The experiments reveal that, in terms of text-image alignment metrics, our metric scores are highly consistent with human scores, demonstrating a much closer alignment than other evaluation metrics.   

<html><body><table><tr><td>Model</td><td>Human</td><td>EVALALIGN</td><td>HPS v2</td><td>CLIP-scoreImageReward PickScore</td><td></td><td></td></tr><tr><td>IF-I-XL v1.0 (Alex Shonenkov & et al.,2023)</td><td>5.4500</td><td>5.5300</td><td>32.5477100.8579</td><td>2</td><td>0.4391 3</td><td>21.1998 10</td></tr><tr><td>IF-I-L v1.O (Alex Shonenkov & et al., 2023)</td><td>5.2300 2</td><td>5.4500 2</td><td>32.7140 9</td><td>0.8538 4</td><td>0.3820 6</td><td>21.1284 12</td></tr><tr><td>SDXL Refiner v1.O (Stability AI, b)</td><td>5.2100 3</td><td>5.4000 3</td><td>35.6465</td><td>3 0.8528 5</td><td>0.4738 2</td><td>22.3532 2</td></tr><tr><td>LCM SDXL (Luo et al., 2023)</td><td>5.1800 4</td><td>5.33005</td><td>33.8011 6</td><td>0.85126</td><td>0.3833 5</td><td>21.96204</td></tr><tr><td>PixArt XL21024MS (Chen etal.,2023b)</td><td>5.1100 5</td><td>5.3100 6</td><td>37.0493</td><td>0.8634</td><td>0.6542</td><td>22.3926</td></tr><tr><td>IF-I-M v1.0 (Alex Shonenkov & et al., 2023)</td><td>5.0800 6</td><td>5.2200 8</td><td>31.0951</td><td>14 0.8434 8</td><td>0.0499 10</td><td>20.8270 20</td></tr><tr><td>LCM LORA SDXL (Luo et al., 2023)</td><td>5.0600</td><td>5.2700 7</td><td>32.7752</td><td>8 0.834910</td><td>0.1618 9</td><td>21.7627 6</td></tr><tr><td>SDXL v1.0 (Podell et al., 2023)</td><td>5.0300 8</td><td>5.3500 4</td><td>35.1593</td><td>4 0.8540 3</td><td>0.4322 4</td><td>22.1291 3</td></tr><tr><td>Wuerstchen (Pernias et al., 2023)</td><td>4.8700 9</td><td>5.1700 9</td><td>36.4632</td><td>2 0.8381 9</td><td>0.2513</td><td>21.7779</td></tr><tr><td>Openjourney (PromptHero,a)</td><td>4.8300 10</td><td>4.9200 15</td><td>31.1495</td><td>12 0.8173</td><td>16 -0.0867 14</td><td>21.1163 13</td></tr><tr><td>SD v2.1 (Rombach et al.,2022a)</td><td>4.8000 11</td><td>5.0700 11</td><td>31.1017</td><td>13 0.8278</td><td>14 -0.0453 12</td><td>21.2093 9</td></tr><tr><td>MultiFusion (Marco et al., 2023)</td><td>4.6800 12</td><td>4.8000 18</td><td>28.7957 24</td><td>0.8264</td><td>15 -0.1337 15</td><td>20.9625 17</td></tr><tr><td>Dreamlike Diffusion v1.O (dreamlike.art, a)</td><td>4.6600 13</td><td>5.1500 10</td><td>34.8196</td><td>5 0.8493</td><td>0.2295 8</td><td>21.5550</td></tr><tr><td>SD v2.0 (Rombach et al., 2022a)</td><td>4.6400 14</td><td>5.0100</td><td>12 30.6153</td><td>17 0.8298</td><td>13 -0.1424 16</td><td>21.1905 11</td></tr><tr><td>Vintedois Diffusion v0.1 (Vintedois-Diffusion v0.1) 4.6200</td><td>15</td><td>4.9500</td><td>14 31.9503</td><td>11 0.8319</td><td>12 -0.0222 11</td><td>21.1141 14</td></tr><tr><td>Safe SD STRONG (Patrick et al., 2022)</td><td>4.6000 16</td><td>4.8300</td><td>17 30.6615</td><td>16 0.7751 23</td><td>-0.5028 22</td><td>20.7491 21</td></tr><tr><td>Dreamlike Photoreal v2.O (dreamlike.art, b)</td><td>4.5600 17</td><td>4.9800</td><td>13 33.7712</td><td>0.8344</td><td>11 -0.0859 13</td><td>21.4832 8</td></tr><tr><td>Safe SD WEAK (Patrick et al., 2022)</td><td>4.5300 18</td><td>4.7100 20</td><td>30.5644</td><td>18 0.8140 18</td><td>-0.2728 18</td><td>20.9899 16</td></tr><tr><td>SD v1.4 (Rombach et al., 2022a)</td><td>4.5200 19</td><td>4.7600</td><td>29.9149 19</td><td>20 0.8048 20</td><td>-0.3438 19</td><td>20.8462 19</td></tr><tr><td>SD v1.5 (Rombach et al., 2022a)</td><td>4.4500 20</td><td>4.9000 16</td><td>30.1673</td><td>19 0.8142</td><td>17 -0.2213 17</td><td>20.8640 18</td></tr><tr><td>Safe SD MEDIUM (Patrick et al., 2022)</td><td>4.4000 21</td><td>4.5600 24</td><td>30.782015</td><td>0.7974 21</td><td>-0.3591 20</td><td>21.0257 15</td></tr><tr><td>Redshift diffusion (Redshift-Diffusion)</td><td>4.3500 22</td><td>4.6700 21</td><td>29.2865</td><td>22 0.8066</td><td>19 -0.4172 21</td><td>20.6327 23</td></tr><tr><td>Safe SD MAX (Patrick et al., 2022)</td><td>4.3100 23</td><td>4.5900 23</td><td>29.8126</td><td>21 0.7601 24</td><td>-0.6095 24</td><td>20.7046 22</td></tr><tr><td>Openjourney v2 (PromptHero, b)</td><td>4.1500 24</td><td>4.6500 22</td><td>29.2389</td><td>23 0.7851 22</td><td>-0.6051 23</td><td>20.5973 24</td></tr></table></body></html>

Table 4: Results of different prompt categories for evaluating image faithfulness. Baseline is the vanilla LLaVA-NeXT model without find-tuning with human-aligned data.   

<html><body><table><tr><td>Method</td><td>|Body</td><td>Hand</td><td>Face</td><td>Object</td><td>Common</td></tr><tr><td>Human</td><td>1.6701</td><td>1.0278</td><td>1.4107</td><td>2.2968</td><td>1.0637</td></tr><tr><td>Baseline</td><td>3.9950</td><td>3.9932</td><td>3.9867</td><td>2.6734</td><td>3.3476</td></tr><tr><td>EVALALIGN</td><td>1.7305</td><td>0.9490</td><td>1.4393</td><td>2.3565</td><td>1.0903</td></tr></table></body></html>

Table 5: Results of different prompt categories for evaluating text-to-image alignment. Baseline is the vanilla LLaVA-NeXT model without find-tuning with human-aligned data.   

<html><body><table><tr><td>Method</td><td>Object Count Color Style Spatial Action</td><td></td><td></td><td></td><td></td><td></td></tr><tr><td>Human</td><td></td><td></td><td></td><td></td><td>1.6947 1.2032 1.8551 1.9796 1.5608 1.8015</td><td></td></tr><tr><td>Baseline</td><td></td><td></td><td></td><td></td><td>1.5602 1.0742 1.9275 1.18371.4118 1.1838</td><td></td></tr><tr><td>EVALALIGN|1.6807 1.2516 1.8696 1.9592 1.5882 1.8382</td><td></td><td></td><td></td><td></td><td></td><td></td></tr></table></body></html>

Effect of model size. Since transformers are known for their scalability (Radford et al., 2018; Dehghani et al., 2023), we investigate the effect of the model size on the performance of image faithfulness evaluation. As illustrated in Table 7, the benefits of scaling up the utilized MLLMs are remarkably significant, where increasing the model size from 7B to 34B results in substantial improvements in evaluation performance. For this consequence, for the final version of the EVALALIGN evaluation model, we choose LLaVA-NeXT 34B, the largest model in LLaVA-NExT series, and finetune it on our meticulously curated SFT data. Since some users of EVALALIGN cannot afford MLLM inference with 34B parameters, we will make the 13B and 34B models publicly available.

# 5.3 COMPARISON WITH EXISTING EVALUATION METHODS

SFT with human-aligned data outperforms vanilla MLLMs. To validate the effectiveness of the MLLM after SFT, we use vanilla LLaVA-NeXT 13B as the baseline model for comparison. As shown in Table 4 and Table 5, the results of vanilla model suggest some correlations with human-annotated data. However, the alignment of the vanilla MLLM is relatively low due to the absence of images generated by model (such as distorted bodies and hands images) and issues related to evaluation in the MLLM’s pre-training dataset. After applying SFT on the LLaVA-Next 13B model using human annotated data, the model’s predictions on various fine-grained evaluation metrics are almost align to the human-annotated data and significantly surpass the evaluation results of all MLLM models that are not finetuned. This experimental results confirms that our SFT training enables the MLLM to be successfully applied to the task of evaluating text-to-image models.

Table 6: Ablation study on the size of training data. Results are reported on image faithfulness under different training data scale. We observe that a small number of annotated training data is sufficient for optimal results.   

<html><body><table><tr><td>Method</td><td>Data Size</td><td>SDXL</td><td>Pixart</td><td>Wuerstchen</td><td>SDXL-Turbo</td><td>IF</td><td>SD v1.5</td><td>SD v2.1</td><td>LCM</td></tr><tr><td>Human</td><td>1</td><td>2.1044</td><td>1.8606</td><td>1.7839</td><td>1.3854</td><td>1.3822</td><td>1.3818</td><td>1.1766</td><td>1.0066</td></tr><tr><td rowspan="3">EVALALIGN</td><td>200</td><td>1.7443</td><td>1.8898</td><td>1.9278</td><td>1.1261</td><td>1.2977</td><td>1.5254</td><td>1.4309</td><td>1.1204</td></tr><tr><td>500</td><td>1.8890</td><td>1.9161</td><td>1.8586</td><td>1.2141</td><td>1.3109</td><td>1.3926</td><td>1.3815</td><td>0.9485</td></tr><tr><td>800</td><td>2.0443</td><td>1.9199</td><td>1.8012</td><td>1.3353</td><td>1.296</td><td>1.4702</td><td>1.3221</td><td>1.0305</td></tr></table></body></html>

Table 7: Ablation study on the size vision-language model. Results are reported on image faithfulness under different model scales of LLaVA-NeXT. We observe that model size is critical for reliable evaluation.   

<html><body><table><tr><td>Method</td><td>Model Size</td><td>SDXL</td><td>Pixart</td><td>Wuerstchen</td><td>SDXL-Turbo</td><td>IF</td><td>SD v1.5</td><td>SD v2.1</td><td>LCM</td></tr><tr><td>Human</td><td>1</td><td>2.1044</td><td>1.8606</td><td>1.7839</td><td>1.3854</td><td>1.3822</td><td>1.3818</td><td>1.1766</td><td>1.0066</td></tr><tr><td rowspan="3">EVALALIGN</td><td>7B</td><td>1.9959</td><td>1.8615</td><td>1.8228</td><td>1.1708</td><td>1.2704</td><td>1.4031</td><td>1.3063</td><td>1.0145</td></tr><tr><td>13B</td><td>2.0443</td><td>1.9199</td><td>1.8012</td><td>1.3353</td><td>1.2960</td><td>1.4702</td><td>1.3221</td><td>1.0305</td></tr><tr><td>34B</td><td>2.1131</td><td>1.8621</td><td>1.8083</td><td>1.3906</td><td>1.3076</td><td>1.3921</td><td>1.2037</td><td>1.0143</td></tr></table></body></html>

Comparison with other methods. To verify the human preference alignment of our model, especially when compared with other baseline methods, we calculate Kendall rank (KENDALL, 1938) and Pearson (Freedman et al., 2007) correlation coefficient on images generated by 24 text-to-image models and summarize the results in Table 8.

As can be concluded, compared with baseline methods, EVALALIGN achieves significant higher alignment with fine-grained human preference on image faithfulness and image-text consistency, showcasing robust generalization ability. Although HPS v2 roughly aligns with human preference in some extent, the relative small model capacity and coarse ranking training limits its generalization to the fine-grained

Table 8: Comparison with existing methods.   

<html><body><table><tr><td rowspan="2">Method</td><td>Faithfulness</td><td>Alignment</td></tr><tr><td>Kendall↑ Pearson↑ Kendall↑ Pearson↑</td><td></td></tr><tr><td>CLIP-score</td><td>0.1304 0.1765</td><td>0.6956 0.8800</td></tr><tr><td>HPSv2</td><td>0.4203 0.5626</td><td>0.5217 0.7113</td></tr><tr><td>EVALALIGN</td><td>0.7464 0.8730</td><td>0.8043 0.9356</td></tr></table></body></html>

annotated data. Besides, since CLIP-s only cares the CLIP similarity of the generated image and its corresponding prompt, it behaves poorly in image faithfulness evaluation. The per-question alignment and the leaderboard of EVALALIGN will be introduced in the supplementary materials.

# 6 CONCLUSION AND DISCUSSION

In this work, we design an economic evaluation method that offers high accuracy, strong generalization capabilities, and provides fine-grained, interpretable metrics. We develop a comprehensive data annotation and cleaning process tailored for evaluation tasks, and establish the EVALALIGN benchmark for training and evaluating models on supervised fine-tuning tasks for MLLMs. Experimental results across 24 text-to-image models demonstrate that our evaluation metrics surpass the accuracy of all the state-of-art evaluation method. Additionally, we conduct a detailed empirical study on how MLLMs can be applied to model evaluation tasks. There are still many opportunities for further advancements and expansions based on our EVALALIGN. We hope that our work can inspire and facilitate future research in this field.

# 7 REPRODUCIBILITY STATEMENT

The full version of the source code, dataset, as well as the final version of the finetuned MLLMs (one finetuned on LLaVA-NeXT 13B and the other one finetuned on LLaVA-NeXT 34B) will be released to the public. The data construction procuedure, including data collection and curation, data cleaning and annotation, is thoroughly described in Section 3. For details related to the human annotation and the measures that used to ensure its quality, we comprehensively introduce them in Appendix B. As for every experiment introduced in this paper, we provide a general introduction in Section 5 and exhibit implementation details related to reproduce our experiments. Specifically, the latter includes the hyper-parameters of each evaluated models, the employed instruction, as well as more supplementary experiments, which are described in Appendix C, Appendix D and Appendix E.

# 8 ETHICS STATEMENT

We are committed to conducting this research with the highest ethical standards. Our goal is to contribute positively to the fields of evaluation benchmarks on artificial intelligence generated content, emphasizing transparency and reproducibility in our design. Similar with other MLLMs, EVALALIGN may potentially generate responses contain offensive, inappropriate, or harmful content. Since the base MLLMs of EVALALIGN are pretrained on large datasets scraped from the web that might contain private information and harmful content, they may inadvertently generate or expose sensitive information, raising ethical and privacy concerns. MLLMs are also susceptible to adversarial attacks, where inputs are intentionally crafted to deceive the model. This vulnerability can be exploited to manipulate model outputs, posing security and ethic risks. To alleviate these safety limitation and our fulfill our social responsibility as artificial intelligence researchers, we create dedicated evaluation sets for bias detection and mitigation, and conducted adversarial testing through hours of redteaming. Besides, EVALALIGN is designed for fine-grained, human-aligned automatic text-toimage evaluations, which can serve as a stepping stones toward revealing the inner generation nature of text-to-image generative models, thereby lowering the ethical hazard of these models. We believe that with appropriate use, it could provide users with interesting experiences for detailed synthesized image evaluation, and inspires more appealing research works about text-to-image generation.

# REFERENCES

Misha Konstantinov Alex Shonenkov and et al. Deepfloyd if. https://github.com/deep-floyd/IF, 2023.   
Eslam Mohamed Bakr, Pengzhan Sun, Xiaogian Shen, Faizan Farooq Khan, Li Erran Li, and Mohamed Elhoseiny. Hrs-bench: Holistic, reliable and scalable benchmark for text-to-image models. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 20041–20053, 2023.   
James Betker, Gabriel Goh, Li Jing, Tim Brooks, Jianfeng Wang, Linjie Li, Long Ouyang, Juntang Zhuang, Joyce Lee, Yufei Guo, et al. Improving image generation with better captions. https://openai.com/dall-e-3, 2023.   
Andreas Blattmann, Tim Dockhorn, Sumith Kulal, Daniel Mendelevitch, Maciej Kilian, Dominik Lorenz, Yam Levi, Zion English, Vikram Voleti, Adam Letts, et al. Stable video diffusion: Scaling latent video diffusion models to large datasets. arXiv preprint arXiv:2311.15127, 2023.   
Jun Chen, Deyao Zhu, Xiaoqian Shen, Xiang Li, Zechun Liu, Pengchuan Zhang, Raghuraman Krishnamoorthi, Vikas Chandra, Yunyang Xiong, and Mohamed Elhoseiny. Minigpt-v2: Large language model as a unified interface for vision-language multi-task learning. arXiv preprint arXiv:2310.09478, 2023a.   
Junsong Chen, Jincheng Yu, Chongjian Ge, Lewei Yao, Enze Xie, Yue Wu, Zhongdao Wang, James Kwok, Ping Luo, Huchuan Lu, et al. Pixart-alpha: Fast training of diffusion transformer for photorealistic text-to-image synthesis. arXiv preprint arXiv:2310.00426, 2023b.   
Jaemin Cho, Yushi Hu, Jason Michael Baldridge, Roopal Garg, Peter Anderson, Ranjay Krishna, Mohit Bansal, Jordi Pont-Tuset, and $\mathtt { S u }$ Wang. Davidsonian scene graph: Improving reliability in fine-grained evaluation for text-image generation. In International conference on learning representations, 2023a.   
Jaemin Cho, Abhay Zala, and Mohit Bansal. Dall-eval: Probing the reasoning skills and social biases of text-to-image generation models. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 3043–3054, 2023b.   
Mostafa Dehghani, Josip Djolonga, Basil Mustafa, Piotr Padlewski, Jonathan Heek, Justin Gilmer, Andreas Peter Steiner, Mathilde Caron, Robert Geirhos, Ibrahim Alabdulmohsin, et al. Scaling vision transformers to 22 billion parameters. In International conference on machine learning, pp. 7480–7512. PMLR, 2023.   
Alexey Dosovitskiy. An image is worth 16x16 words: Transformers for image recognition at scale. arXiv preprint arXiv:2010.11929, 2020.   
dreamlike.art. Dreamlike-diffusion v1.0. https://huggingface.co/dreamlike-art/ dreamlike-diffusion-1.0, 2022a.   
dreamlike.art. Dreamlike-photoreal. https://huggingface.co/dreamlike-art/ dreamlike-photoreal-2.0, 2023b.   
David Freedman, Robert Pisani, and Roger Purves. Statistics (international student edition). Pisani, R. Purves, 4th edn. WW Norton & Company, New York, 2007.   
Jack Hessel, Ari Holtzman, Maxwell Forbes, Ronan Le Bras, and Yejin Choi. CLIPScore: A reference-free evaluation metric for image captioning. In Proceedings of the conference on empirical methods in natural language processing, pp. 7514–7528, November 2021. doi: 10.18653/v1/2021.emnlp-main.595. URL https://aclanthology.org/2021.emnlp-main.595.   
Martin Heusel, Hubert Ramsauer, Thomas Unterthiner, Bernhard Nessler, and Sepp Hochreiter. Gans trained by a two time-scale update rule converge to a local nash equilibrium. Advances in neural information processing systems, 30, 2017.   
Edward J Hu, Yelong Shen, Phillip Wallis, Zeyuan Allen-Zhu, Yuanzhi Li, Shean Wang, Lu Wang, and Weizhu Chen. Lora: Low-rank adaptation of large language models. arXiv preprint arXiv:2106.09685, 2021.   
Yushi Hu, Benlin Liu, Jungo Kasai, Yizhong Wang, Mari Ostendorf, Ranjay Krishna, and Noah A Smith. Tifa: Accurate and interpretable text-to-image faithfulness evaluation with question answering. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 20406–20417, 2023.   
Kaiyi Huang, Kaiyue Sun, Enze Xie, Zhenguo Li, and Xihui Liu. T2i-compbench: A comprehensive benchmark for open-world compositional text-to-image generation. Advances in neural information processing systems, 36:78723–78747, 2023a.   
Lianghua Huang, Di Chen, Yu Liu, Yujun Shen, Deli Zhao, and Jingren Zhou. Composer: Creative and controllable image synthesis with composable conditions. arXiv preprint arXiv:2302.09778, 2023b.   
M. G. KENDALL. A new measure of rank correlation. Biometrika, 30(1-2):81–93, 1938. doi: 10.1093/biomet/ 30.1-2.81. URL https://doi.org/10.1093/biomet/30.1-2.81.   
Yuval Kirstain, Adam Polyak, Uriel Singer, Shahbuland Matiana, Joe Penna, and Omer Levy. Pick-a-pic: An open dataset of user preferences for text-to-image generation. Advances in neural information processing systems, 36, 2024.   
Tony Lee, Michihiro Yasunaga, Chenlin Meng, Yifan Mai, Joon Sung Park, Agrim Gupta, Yunzhi Zhang, Deepak Narayanan, Hannah Teufel, Marco Bellagente, et al. Holistic evaluation of text-to-image models. Advances in neural information processing systems, 36, 2024.   
KunChang Li, Yinan He, Yi Wang, Yizhuo Li, Wenhai Wang, Ping Luo, Yali Wang, Limin Wang, and Yu Qiao. Videochat: Chat-centric video understanding. arXiv preprint arXiv:2305.06355, 2023.   
Ji Lin, Hongxu Yin, Wei Ping, Yao Lu, Pavlo Molchanov, Andrew Tao, Huizi Mao, Jan Kautz, Mohammad Shoeybi, and Song Han. Vila: On pre-training for visual language models. arXiv preprint arXiv:2312.07533, 2023.   
Haotian Liu, Chunyuan Li, Yuheng Li, and Yong Jae Lee. Improved baselines with visual instruction tuning. arXiv preprint arXiv:2310.03744, 2023.   
Haotian Liu, Chunyuan Li, Yuheng Li, Bo Li, Yuanhan Zhang, Sheng Shen, and Yong Jae Lee. Llava-next: improved reasoning, ocr, and world knowledge, January 2024a. URL https://llava-vl.github. io/blog/2024-01-30-llava-next/.   
Haotian Liu, Chunyuan Li, Qingyang Wu, and Yong Jae Lee. Visual instruction tuning. Advances in neural information processing systems, 36, 2024b.   
Yujie Lu, Xianjun Yang, Xiujun Li, Xin Eric Wang, and William Yang Wang. Llmscore: Unveiling the power of large language models in text-to-image synthesis evaluation. Advances in neural information processing systems, 36, 2024.   
Simian Luo, Yiqin Tan, Longbo Huang, Jian Li, and Hang Zhao. Latent consistency models: Synthesizing high-resolution images with few-step inference. arXiv preprint arXiv:2310.04378, 2023.   
Bellagente Marco, Brack Manuel, Teufel Hannah, Friedrich Felix, and et al. Multifusion: fusing pre-trained models for multi-lingual, multi-modal image generation. arXiv preprint arXiv:2305.15296, 2023.   
Mayu Otani, Riku Togashi, Yu Sawai, Ryosuke Ishigami, Yuta Nakashima, Esa Rahtu, Janne Heikkilä, and Shin’ichi Satoh. Toward verifiable and reproducible human evaluation for text-to-image generation. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 14277–14286, 2023.   
Schramowski Patrick, Brack Manuel, and et al. Safe latent diffusion: Mitigating inappropriate degeneration in diffusion models. arXiv preprint arXiv:2211.05105, 2022.   
Pablo Pernias, Dominic Rampas, and Marc Aubreville. Wuerstchen: Efficient pretraining of text-to-image models. arXiv preprint arXiv:2306.00637, 2023.   
Dustin Podell, Zion English, Kyle Lacey, Andreas Blattmann, Tim Dockhorn, Jonas Müller, Joe Penna, and Robin Rombach. Sdxl: Improving latent diffusion models for high-resolution image synthesis. arXiv preprint arXiv:2307.01952, 2023.   
PromptHero. Openjourney. https://huggingface.co/prompthero/openjourney, 2022a.   
PromptHero. Openjourneyv2. https://huggingface.co/ilkerc/openjourney-v2, 2023b.   
Alec Radford, Karthik Narasimhan, Tim Salimans, Ilya Sutskever, et al. Improving language understanding by generative pre-training. OpenAI, 2018.   
Aditya Ramesh, Prafulla Dhariwal, Alex Nichol, Casey Chu, and Mark Chen. Hierarchical text-conditional image generation with clip latents. arXiv preprint arXiv:2204.06125, 2022.   
Redshift-Diffusion. redshift-diffusion. https://huggingface.co/nitrosocke/ redshift-diffusion, 2022.   
Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 10684–10695, 2022a.   
Robin Rombach, Andreas Blattmann, Dominik Lorenz, Patrick Esser, and Björn Ommer. High-resolution image synthesis with latent diffusion models. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 10684–10695, 2022b.   
Chitwan Saharia, William Chan, Saurabh Saxena, Lala Li, Jay Whang, Emily L Denton, Kamyar Ghasemipour, Raphael Gontijo Lopes, Burcu Karagol Ayan, Tim Salimans, et al. Photorealistic text-to-image diffusion models with deep language understanding. Advances in neural information processing systems, 35:36479– 36494, 2022.   
Tim Salimans, Ian Goodfellow, Wojciech Zaremba, Vicki Cheung, Alec Radford, and Xi Chen. Improved techniques for training gans. Advances in neural information processing systems, 29, 2016.   
Christoph Schuhmann, Romain Beaumont, Richard Vencu, Cade Gordon, Ross Wightman, Mehdi Cherti, Theo Coombes, Aarush Katta, Clayton Mullis, Mitchell Wortsman, et al. Laion-5b: An open large-scale dataset for training next generation image-text models. Advances in neural information processing systems, 35: 25278–25294, 2022.   
Yang Song, Jascha Sohl-Dickstein, Diederik P Kingma, Abhishek Kumar, Stefano Ermon, and Ben Poole. Score-based generative modeling through stochastic differential equations. In International conference on learning representations, 2021.   
Stability AI. Sdxl-turbo. https://stability.ai/research/ adversarial-diffusion-distillation, 2023a.   
Stability AI. Sdxl-refiner. https://huggingface.co/stabilityai/ stable-diffusion-xl-refiner-1.0, 2023b.   
Keqiang Sun, Junting Pan, Yuying Ge, Hao Li, Haodong Duan, Xiaoshi Wu, Renrui Zhang, Aojun Zhou, Zipeng Qin, Yi Wang, et al. Journeydb: A benchmark for generative image understanding. Advances in neural information processing systems, 36, 2024.   
Zhiyu Tan, Mengping Yang, Luozheng Qin, Hao Yang, Ye Qian, Qiang Zhou, Cheng Zhang, and Hao Li. An empirical study and analysis of text-to-image generation using large language model-powered textual representation. arXiv preprint arXiv:2405.12914, 2024a.   
Zhiyu Tan, Xiaomeng Yang, Luozheng Qin, and Hao Li. Vidgen-1m: A large-scale dataset for text-to-video generation. arXiv preprint arXiv:2408.02629, 2024b.   
Tristan Thrush, Ryan Jiang, Max Bartolo, Amanpreet Singh, Adina Williams, Douwe Kiela, and Candace Ross. Winoground: Probing vision and language models for visio-linguistic compositionality. In Proceedings of the IEEE/CVF conference on computer vision and pattern recognition, pp. 5238–5248, 2022.   
Vintedois-Diffusion v0.1. vintedois-diffusion v0.1. https://huggingface.co/22h/ vintedois-diffusion-v0-1, 2023.   
Junyan Wang, Zhenhong Sun, Zhiyu Tan, Xuanbai Chen, Weihua Chen, Hao Li, Cheng Zhang, and Yang Song. Towards effective usage of human-centric priors in diffusion models for text-based human image generation. In Proceedings of the IEEE/CVF Conference on Computer Vision and Pattern Recognition, pp. 8446–8455, 2024.   
Zijie J Wang, Evan Montoya, David Munechika, Haoyang Yang, Benjamin Hoover, and Duen Horng Chau. Diffusiondb: A large-scale prompt gallery dataset for text-to-image generative models. In Proceedings of the annual meeting of the association for computational linguistics, pp. 893–911, 2023.   
Olivia Wiles, Chuhan Zhang, Isabela Albuquerque, Ivana Kaji´c, Su Wang, Emanuele Bugliarello, Yasumasa Onoe, Chris Knutsen, Cyrus Rashtchian, Jordi Pont-Tuset, et al. Revisiting text-to-image evaluation with gecko: on metrics, prompts, and human ratings. arXiv preprint arXiv:2404.16820, 2024.   
Xiaoshi Wu, Yiming Hao, Keqiang Sun, Yixiong Chen, Feng Zhu, Rui Zhao, and Hongsheng Li. Human preference score v2: A solid benchmark for evaluating human preferences of text-to-image synthesis. arXiv preprint arXiv:2306.09341, 2023a.   
Xiaoshi Wu, Keqiang Sun, Feng Zhu, Rui Zhao, and Hongsheng Li. Human preference score: Better aligning text-to-image models with human preference. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 2096–2105, 2023b.   
Jiazheng Xu, Xiao Liu, Yuchen Wu, Yuxuan Tong, Qinkai Li, Ming Ding, Jie Tang, and Yuxiao Dong. Imagereward: Learning and evaluating human preferences for text-to-image generation. Advances in neural information processing systems, 36, 2024.   
Jiahui Yu, Yuanzhong Xu, Jing Yu Koh, Thang Luong, Gunjan Baid, Zirui Wang, Vijay Vasudevan, Alexander Ku, Yinfei Yang, Burcu Karagol Ayan, et al. Scaling autoregressive models for content-rich text-to-image generation. Transactions on machine learning research, 2022.   
Cheng Zhang, Xuanbai Chen, Siqi Chai, Chen Henry Wu, Dmitry Lagun, Thabo Beeler, and Fernando De la Torre. Iti-gen: Inclusive text-to-image generation. In Proceedings of the IEEE/CVF International Conference on Computer Vision, pp. 3969–3980, 2023a.   
Hang Zhang, Xin Li, and Lidong Bing. Video-llama: An instruction-tuned audio-visual language model for video understanding. arXiv preprint arXiv:2306.02858, 2023b.   
Lvmin Zhang, Anyi Rao, and Maneesh Agrawala. Adding conditional control to text-to-image diffusion models. In Proceedings of the IEEE/CVF international conference on computer vision, pp. 3836–3847, 2023c.   
Shiwei Zhang, Jiayu Wang, Yingya Zhang, Kang Zhao, Hangjie Yuan, Zhiwu Qin, Xiang Wang, Deli Zhao, and Jingren Zhou. I2vgen-xl: High-quality image-to-video synthesis via cascaded diffusion models. arXiv preprint arXiv:2311.04145, 2023d.   
Deyao Zhu, Jun Chen, Xiaoqian Shen, Xiang Li, and Mohamed Elhoseiny. Minigpt-4: Enhancing visionlanguage understanding with advanced large language models. arXiv preprint arXiv:2304.10592, 2023.

# A LIMITATIONS

Multimodal LLMs. Since EVALALIGN evaluation models are fine-tuned MLLMs, they also suffer from multimodal hallucination, where models may generate content that seems plausible but actually incorrect or fabricated, and cannot be inferred from the input images and texts. Moreover, due to the possible harmful content in the pretraining data of the utilized base MLLMs, the model may inherit these biases and generate inappropriate response. Although we carefully curate the SFT training data of the EVALALIGN evaluation models, the problems of hallucination and biased pre-training is alleviated but not fully addressed. Other than the these issues, EVALALIGN evaluation models also suffer from opacity and interpretability, context limitation, as well as sensitivity to input formatting, like most multimodal LLMs.

Human Annotations. Human annotation is naturally subjective and influenced by individual perspectives, biases, and preferences. During the annotation, annotators can make mistakes, leading to incorrect or noisy labels. Regarding these challenges, we conduct 9 rounds of trial annotation and 2 rounds of random sampling quality inspection to ensure the inter-annotator consistency and overall annotation quality. We also design easy-to-understand annotation guidelines, instructions and platform to lower the annotation difficulty and benefit the annotation accuracy. Despite all these efforts, conducting human annotation with different annotators, user interface and annotation guidelines may lead to different result, making our annotation somewhat limited. Furthermore, human annotation can be time-consuming and resource-intensive, limiting the scale at which we can afford.

# B ANNOTATION DETAILS

Before performing the final human annotation, we made a series of efforts to guarantee its quantity, quality and efficiency. To begin with, we select appropriate candidates to perform the annotation and hold a training meeting for them. Then, we design a user-friendly user interface and a comprehensive annotation procedure. We write a detailed annotation guidelines to explain every aspect and precaution of the annotation. As mentioned above, we conduct 9 rounds of trial annotation on another 50 synthesized images and 2 turns of random sampling quality inspection to further ensure inter-annotator consistency and annotation accuracy.

Annotator selection. The accuracy and reliability of the annotated data depend heavily on the capabilities of the human annotators involved in the annotation process. As a consequence, at the beginning of the annotation, We first conduct annotator selection to build an appropriate and unbiased annotation team, and train this annotation team with our meticulously prepared annotation guidelines. For annotator selection, we let the candidates to accomplish a test concentrating on 10 factors, domain expertise, resistance to visually disturbing content, attention to detail, communication skills, reliability, cultural and linguistic competence, technical skills, ethical considerations, aesthetic cognition, and motivation. Notably, since the evaluated models may generate images with uncomfortable and inappropriate visual content, the candidates are notified with this inconvenience before the test. Only those agreed with this inconvenience are eligible to participate in the test, and they are welcome to withdraw at any time if they choose to do so. Based on the test results and candidate backgrounds, We try our best to ensure that the selected annotators are well-balanced in background and have a generally competitive abilities of the 10 mentioned factors. To summarize, our annotation team includes 10 annotators carefully selected from 29 candidates, 5 males and 5 females, all have a bachelor’s degree. We interview the annotators and ensure they are adequate for the annotation.

Annotation training and guidelines. After the selection, we conduct a training meeting over our comprehensive user guidelines to make the annotation team aware of our purpose and standard. During the training meeting, we explain the purpose, precaution, standard, workload and wage of the annotation. Besides, we have formally informed the annotators that the human annotation is solely for research purposes, and the data they have annotated may potentially be released to the public in the future. We, and the annotators reached consensus on the standard, workload, wage and intended usage of the annotated data. The rules for recognising image faithfulness and text-image are universal, and thus each individual’s standards should not differ significantly. As a consequence, we annotate a few samples using our meticulously developed annotation platform for the annotators to ensure inter-annotator consistency. The overall snapshot of the developed annotation paltform is exhibited in fig. 4. With this training, we also equip the annotators with necessary knowledge for unbiased detailed human evaluation on image faithfulness and text-image alignment. Specifically, the employed annotation guidelines involve the instructions for using the annotation platform and detailed guidelines about the annotation procedure, and we demonstrate them in Table 9.

Trial Annotation Even with the above preparation, there is no quantitative evidence to verify the quality, the efficiency, and the inter-annotator consistency of the human annotation. Additionally, the standard for assessing image faithfulness and text-image are universal, which further emphasize the significant role of high inter-annotator consistency. Considering that, we conduct a multi-turn trial annotation on another 50 synthesized images. After each trial, we calculate the Cohen’s kappa coefficient and conduct a meeting for our annotators

# No.4512 Prompt: A woman wearing a red dress is gently cradling a small, white puppy in her arms.

![](images/698b4af00f4d6b5ab4302bad449d0da4682b31ce941ac3ab148b21783f50df9f.jpg)  
Figure 4: Demonstration of our user interface. Each time, our specially designed user interface will provide one sample to the annotators. We incorporated four distinct icons to signify various functionalities of the user interface.

to explain annotation standards, rules and guidelines, thereby ensuring high inter-annotator reliability. In total, we conduct nine turns of trial annotation, and in the last turn of the trial, the Cohen’s kappa coefficient of our annotators reaches 0.681, indicating high inter-annotator reliability.

Random Sampling Quality Inspection Upon reaching the milestone percentages of $2 5 \%$ , $50 \%$ , $7 5 \%$ , and $100 \%$ in the annotation progress, we conducted a series of random sampling quality inspections on the present annotation results at each milestone, totally four turns of random sampling quality inspection. The random sampling quality inspection by four experts in text-to-image generation selected from our group on 1,000 randomly sampled annotated images. For the first two turn of quality inspection, there are totally 423 and 112 annotated samples that failed the inspection. The failed samples are re-annotated and re-inspected. As for the last two turns of quality inspection, they both revealed zero failed samples due to the thoughtful and rigorous annotation preparation.

# C ADDITIONAL DETAILS OF THE EVALUATED MODELS

In this section, we introduce the details of the evaluated text-to-image generative models in this work.

• Stable Diffusion {v1.4, v1.5, v2 base, v2.0, v2.1}. Stable Diffusion (SD) is a series of 1B text-to-image generative models based on latent diffusion model (Rombach et al., 2022b) and is trained on LAION5B (Schuhmann et al., 2022). Specifically, the SD series includes SD v1.1, SD v1.2, SD v1.4, SD v1.5, SD v2 base, SD v2.0, and SD v2.1 respectively. Among them, we choose the most commonly-employed SD v1.4, SD v1.5, SD v2.0 and SD v2.1 for EVALALIGN evaluation. SD v1.1 was trained at a resolution of $2 5 6 x 2 5 6$ on laion2B-en for 237k steps, followed by training at a resolution of $5 1 2 \mathrm { x } 5 1 2$ on laion-high-resolution ((170M examples from LAION-5B with resolution $> =$ $1 0 2 4 \mathrm { x } 1 0 2 4 )$ for the subsequent 194k steps. While, SD v1.2 was initialized from v1.1 and further finetuned for $5 1 5 \mathrm { k }$ steps at resolution $5 1 2 \mathrm { x } 5 1 2$ on laion-aesthetics $\mathbf { v } 2 \ 5 +$ (a subset of laion2B-en, filtered to images with an original size $> = 5 1 2 \mathrm { x } 5 1 2$ , estimated aesthetics score $> 5 . 0$ , and an estimated watermark probability $< 0 . 5 \rangle$ . SD v1.4 is initialized from v1.2 and subsequently finetuned for $2 2 5 \mathrm { k }$ steps at resolution $5 1 2 \mathrm { x } 5 1 2$ on laion-aesthetics $\mathbf { v } 2 \ 5 +$ . This version incorporates a $10 \%$ dropping of the text-conditioning to improve classifier-free guidance sampling. Similar to SD v1.4, SD v1.5 is resumed from SD v1.2 and trained $5 9 5 \mathrm { k \Omega }$ steps at resolution $5 1 2 \mathrm { x } 5 1 2$ on laion-aesthetics $\mathbf { v } 2 5 +$ , with $10 \%$ dropping of the text-conditioning. SD v2 base is trained from scratch for 550k steps at resolution $2 5 6 \mathrm { x } 2 5 6$ on a subset of LAION-5B filtered for explicit pornographic material, using the LAION-NSFW classifier with punsafe $= 0 . 1$ and an aesthetic score $> = 4 . 5$ . Then it is further trained for 850k steps at resolution $5 1 2 \mathrm { x } 5 1 2$ on the same dataset on images with resolution $> = 5 1 2 \mathrm { x } 5 1 2$ . SD v2.0 is resumed from stable-diffusion v2 base and trained for 150k steps using a v-objective on the same dataset. After that, it is further finetuned for another $1 4 0 \mathrm { k }$ steps on $7 6 8 x 7 6 8$ images. SD v2.1 is finetuned from $\operatorname { S D } \mathrm { v } 2 . 0$ with an additional $5 5 \mathrm { k }$ steps on the same dataset (with punsafe $_ { : = 0 . 1 }$ ), and then finetuned for another $1 5 5 \mathrm { k \Omega }$ extra steps with punsafe $_ { = 0 . 9 8 }$ .

• Stable Diffusion XL {v1.0, Refiner v1.0}. Stable Diffusion XL (SDXL) is a powerful text-to-image generation model that iterates on the previous Stable Diffusion models in three key ways: (1) its UNet is 3x larger and SDXL combines a second text encoder (OpenCLIP ViT-bigG/14) with the original text encoder to significantly increase the number of parameters; (2) it introduces size and crop-conditioning to preserve training data from being discarded and gain more control over how a generated image should be cropped; (3) it introduces a two-stage model process; the base model (can also be run as a standalone model) generates an image as an input to the refiner model which adds additional high-quality details.

• Pixart-Alpha. Pixart-Alpha is a model that can be used to generate and modify images based on text prompts. It is a Transformer Latent Diffusion Model that uses one fixed, pretrained text encoders (T5)) and one latent feature encoder (VAE).

• Latent Consistency Model Stable Diffusion XL Latent Consistency Model Stable Diffusion XL (LCM SDXL) Luo et al. (2023) enables SDXL for swift inference with minimal steps. Viewing the guided reverse diffusion process as solving an augmented probability flow ODE (PF-ODE), LCMs are designed to directly predict the solution of such ODE in latent space, mitigating the need for numerous iterations and allowing rapid, high-fidelity sampling.

• Dreamlike Diffusion 1.0. Dreamlike Diffusion 1.0 (dreamlike.art, a) is a SD v1.5 model finetuned on high-quality art images by dreamlike.art.

• Dreamlike Photoreal 2.0. Dreamlike Photoreal 2.0 (dreamlike.art, b) is a photorealistic text-to-image latent diffusion model resumed from SD v1.5 by dreamlike art. This model was finetuned on $7 6 8 x 7 6 8$ images, it works pretty good with resolution 768x768, 640x896, 896x640 and higher resolution such as $7 6 8 x 1 0 2 4$ .

• Openjourney v1, v2. Openjourney (PromptHero, a) is an open-source text-to-image generation model resumed from SD v1.5 and finetuned on Midjourney images by PromptHero. Openjourney v2 (PromptHero, b) was further finetuned using another 124000 images for 12400 steps, about 4 epochs and 32 training hours.

• Redshift Diffusion. Redshift Diffusion (Redshift-Diffusion) is a Stable Diffusion model finetuned on high-resolution 3D artworks.

• Vintedois Diffusion. Vintedois Diffusion (Vintedois-Diffusion v0.1) is a Stable Diffusion v1.5 model finetuned on a large number of high-quality images with simple prompts to generate beautiful images without a lot of prompt engineering.

• Safe Stable Diffusion {Weak, Medium, Strong, Max}. Safe Stable Diffusion (Patrick et al., 2022) is an enhanced version of the SD v1.5 model by mitigating inappropriate degeneration caused by pretraining on unfiltered web-crawled datasets. For instance SD may unexpectedly generate nudity, violence, images depicting self-harm, and otherwise offensive content. Safe Stable Diffusion is an extension of Stable Diffusion that drastically reduces this type of content. Specifically, it has an additional safety guidance mechanism that aims to suppress and remove inappropriate content (hate, harassment, violence, self-harm, sexual content, shocking images, and illegal activity) during image generation. The strength levels for inappropriate content removal are categorized as: {Weak, Medium, Strong, Max}.

• MultiFusion. MultiFusion (Marco et al., 2023) is a multimodal, multilingual diffusion model that extends the capabilities of SD v1.4 by integrating various modules to transfer capabilities to the downstream model. This combination results in novel decoder embeddings, which enable prompting of the image generation model with interleaved multimodal, multilingual inputs, despite being trained solely on monomodal data in a single language.

• DeepFloyd-IF { M, L, XL } v1.0. DeepFloyd-IF (Alex Shonenkov & et al., 2023) is a novel state-of-the-art open-source text-to-image model with a high degree of photorealism and language understanding. It is a modular composed of a frozen text encoder and three cascaded pixel diffusion modules: a base model that generates 64x64 image based on text prompt and two super-resolution models, each designed to generate images of increasing resolution: $2 5 6 x 2 5 6$ and $1 0 2 4 \mathrm { x } 1 0 \bar { 2 } 4$ , respectively. All stages of the model utilize a frozen text encoder based on the T5 transformer to extract text embeddings, which are then fed into a UNet architecture enhanced with cross-attention and attention pooling. Besides, it underscores the potential of larger UNet architectures in the first stage of cascaded diffusion models and depicts a promising future for text-to-image synthesis. The model is available in three different sizes: M, L, and XL. M has 0.4B parameters, L has 0.9B parameters, and XL has 4.3B parameters.

Table 9: User Guidelines of the Human Annotation. Considering that our annotators are native Chinese speakers while our readers may not be, each user is actually provided with a copy of Chinese version of the user guidelines. Meanwhile, we demonstrate its translated English version as follows.   

<html><body><table><tr><td>User Guidelines</td></tr><tr><td>PartIIntroduction Welcome to the annotation platform.This platform is designed to simplify the annotation process and enhance annotation efciency.Before the detailed introduction,we want to claim again that you mayfeel inconvenient as the evaluated models may generate images with uncomfortable and inappropriate visual content. Now, you are still welcomed ifyou want to withdraw your consent The annotation process is conducted onasample-by-sample</td></tr><tr><td>basis,with a question-by-question approach.Thus,you are supposed to answer all the questions raised for the present sample to accomplish its annotation. Once allthe delegated samples are accomplished, your job is finished and we are thankful for your contribution to the project. PartIIGuidelinesof theUserInterface 1.User Login: To access the annotation platform,you are required to login as auser. Please navigate to the login page,enter the username and password provided by us,and click the “Login”button. 2.Dashboard: Once you complete the login,you willbe jumped into thedashborad page.The dashboard willist the overview of the samples assigned to you to annotate. Besides, we list the status of each sample for you to freely check your annotation progress (e.g., pending, completed). 3.Annotation Interface: Click on the“Start”buton or an assigned image through the dashboard interface, you</td></tr><tr><td>will jump into the annotation interface.annotation interface is made upof three components:1)Image Display: View the image to be annotated and its conditioned prompts; 2) Question Panel: List of single-choice questions related to the image; 3) Navigation Buttons: "Next" and "Previous"butons to navigate through questions and images. 4.Answering Questions: Each time,the annotation interface willprovide you a sample for annotation, please view the image and read the associated question,select the appropriate answer from the available options,and repeat the processs for all questions related to the question.</td></tr><tr><td>5.Saving and Submitting Annotations: To save progressand submit completed anotations,you can click the “Save"button to save your progress.If you finish the assgned sample and ensure the accuracy and confidence of its,you can click the “Submit” button to submit this annotation. 6.Review and Edit Annotations: If you want to review and edit your submission,you can navigate the completed tasks section,and select the image to review.You willjump into its annotation interface with the previously submitted annotations and are allowed to do any modification. 7.Report and contact: If you find any problem about the assigned sample,such as witnessing NSFW or biased</td></tr><tr><td>content,assigned visually abnormal sample,feel free to click the “Report’ button and filla form to report this sample. If you have any question about the standard of the annotation or have suggestions for improvement, please do not hesitate to contact us through phone,we will be glad to help you. Part II General Guidelines of the Human Annotation 1.In general, you are supposed to answer all the questions raised for the present sample to accomplish its annotation.This annotation only involves single-choice question. 2.Before answering the question, please ensure that the question is applicable to this prompt.If it is not applicable,please select option O directly—this is the predefined option for this particular scenario. 3.If you are answering a question about the image faithfulness,you may find the question is applicable to multiple objects within the image. you need to answer the question regarding to every applicable object and its role in the image.A straightforward way for this is to solely score every applicable object and choose the option</td></tr></table></body></html>

# D INSTRUCTION TEMPLATES

Here, we present every instruction used for EVALALIGN evaluation on image faithfuleness and text-image alignment. The templates contain some placeholders set for filling in the corresponding attributes of the input images during the evaluation. For example, a specific “<ObjectHere>” and “<NumberHere>” can be “people, laptop, scissors.” and “plate: 1, turkey sandwich: 3, lettuce: 1.”, respectively.

For EVALALIGN evaluation on image faithfulness, we devise 5 questions concentrate on the faithfulness of the generated body structure, generated face, generated hand, generated objects, as well as generation adherence to commonsense and logic. The instruction templates for these fine-grained criteria are as follows:

[Q1]:Are there any issues with the [human/animals] body structure in the image, such as multiple arms, missing limbs or legs when not obscured, multiple heads, limb amputations, and etc? [OPTIONS]: 0.There are no human or animal body in the picture; 1.The body structure of the people or animals in the picture has a very grievous problem that is unbearable; 2.The body structure of the people or animals in the picture has some serious problems and is not acceptable; 3.The body structure of the people or animals in the picture has a slight problem that does not affect the senses; 4.The body structure of the people or animals in the picture is basically fine, with only a few flaws; 5.The body structure of the people or animals in the picture is completely fine and close to reality.

[Q2]:Are there any issues with the [human/animals] hands in the image, such as having more or less than five fingers when not obscured, broken fingers, disproportionate finger sizes, abnormal nail size proportions, and etc?

[OPTIONS]: 0.No human or animal hands are shown in the picture; 1.The hand in the picture has a very grievous problem that is unbearable; 2.The hand in the picture has some serious problems and is not acceptable; 3.The hand in the picture has a slight problem that does not affect the senses; 4.The hand in the picture is basically fine, with only a few flaws; 5.The hands in the picture are completely fine and close to reality.

[Q3]:Are there any issues with [human/animals] face in the image, such as facial distortion, asymmetrical faces, abnormal facial features, unusual expressions in the eyes, and etc? [OPTIONS]: 0.There is no face of any person or animal in the picture; 1.The face of the person or animal in the picture has a very grievous problem that is unbearable; 2.The face of the person or animal in the picture has some serious problems and is not acceptable; 3.The face of the person or animal in the picture has a slight problem that does not affect the senses; 4.The face of the person or animal in the picture is basically fine, with only a few flaws; 5.The face of the person or animal in the picture is completely fine and close to reality.

[Q4]:Are there any issues or tentative errors with objects in the image that do not correspond with the real world, such as distortion of items, and etc?

[OPTIONS]: 0.There are objects in the image that completely do not match the real world, which is very serious and intolerable; 1.There are objects in the image that do not match the real world, which is quite serious and unacceptable; 2.There are slightly unrealistic objects in the image that do not affect the senses; 3.There are basically no objects in the image that do not match the real world, only some flaws; 4.All objects in the image match the real world, no problem.

[Q5]:Does the generated image contain elements that violate common sense or logical rules, such as animal/human with inconsistent anatomy, object-context mismatch, impossible physics, scale and proportion issues, temporal and spatial inconsistencies, hybrid objects, and etc? [OPTIONS]: 0.The image contains elements that violate common sense or logical rules, which is very grievous and intolerable; 1.The presence of elements in the image that seriously violate common sense or logical rules is unacceptable; 2.The image contains elements that violate common sense or logical rules, which is slightly problematic and does not affect the senses; 3.There are basically no elements in the image that violate common sense or logical rules, only some flaws; 4.There are no elements in the image that violate common sense or logical rules, and they are close to reality.

The templates of EVALALIGN evaluation on text-image alignment are as follows. We select 6 common aspects of text-image alignment, object, number, color, style, spatial relationship and action. For images that do not involve the specified attribute, the corresponding question template is not filled in and subsequently input into EVALALIGN.

[Q1]:Does the given image contain all the objects (<ObjectHere>) presented in the corresponding prompts? [OPTIONS]: 1.None objects are included; 2.Some objects are missing; 3.All objects are included.

[Q2]:Does the given image correctly reflect the numbers (<NumberHere>) of each object presented in the corresponding prompts?   
[OPTIONS]: 1.All counting numbers are wrong; 2.Some of them are wrong; 3.All counting numbers are right. [Q3]:Does the given image correctly reflect the colors of each object (<ColorHere>) presented in the corresponding prompts?   
[OPTIONS]: 1.All colors are wrong; 2.Some of them are wrong; 3.All corresponding colors numbers are right.

[Q4]:Does the given image correctly reflect the style (<StyleHere>) described in the corresponding prompts? [OPTIONS]: 1.All styles are wrong; 2.Some of them are wrong; 3.All styles are right.

[Q5]:Does the given image correctly reflect the spatial relationship (<SpatialHere>) of each object described in the corresponding prompts?   
[OPTIONS]: 1.All spatial relationships are wrong; 2.Some of them are wrong; 3.All spatial relationships are right.

[Q6]:Does the given image correctly reflect the action of each object (<ActionHere>) described in the corresponding prompts? [OPTIONS]: 1.All actions are wrong; 2.Some of them are wrong; 3.All actions are right.

# E ADDITIONAL QUANTITATIVE ANALYSIS

# E.1 GENERALIZATION EXPERIMENTS

To verify the generalization capability of our evaluation model, We compared MLLM’s SFT using different training datasets: one with images generated by all 8 text-to-image models and another with images generated by only 4 of these models, while the final evaluation was conducted on images generated by the other 4 models. As shown in Table 10 and Table 11, We observed that MLLMs trained on images from a subset of text-to-image models can effectively generalize to images generated by unseen text-to-image models.

Table 10: Ablation study on the number of different text-to-image models used to generate the training data for evaluating image faithfulness. We observe that EVALALIGN exhibits strong generalization capability.   

<html><body><table><tr><td>Method</td><td>T2I models</td><td>body</td><td>hand</td><td>face</td><td>object</td><td>common</td><td>MAE</td></tr><tr><td>Human</td><td>一 -</td><td>1.4988</td><td>0.8638</td><td>1.1648</td><td>2.2096</td><td>0.8710</td><td>0</td></tr><tr><td rowspan="2">EVALALIGN</td><td>8</td><td>1.6058</td><td>0.7901</td><td>1.1974</td><td>2.2783</td><td>0.8871</td><td>0.0596</td></tr><tr><td>4</td><td>1.6522</td><td>0.9588</td><td>1.2355</td><td>2.3032</td><td>0.9516</td><td>0.0987</td></tr></table></body></html>

Table 11: Ablation study on the number of different text-to-image models used to generate the training data for evaluating text-to-image alignment. We observe that EVALALIGN exhibits strong generalization capability.   

<html><body><table><tr><td>Method</td><td>T2I models</td><td>Object</td><td>Count</td><td>Color</td><td>Style</td><td>Spatial</td><td>Action</td><td>MAE</td></tr><tr><td>Human</td><td></td><td>1.7373</td><td>1.3131</td><td>2.0000</td><td>1.9333</td><td>1.5952</td><td>1.8837</td><td>0</td></tr><tr><td rowspan="2">EVALALIGN</td><td>8</td><td>1.7203</td><td>1.3232</td><td>1.9565</td><td>1.9333</td><td>1.6547</td><td>1.8605</td><td>0.0256</td></tr><tr><td>4</td><td>1.7832</td><td>1.3526</td><td>1.9637</td><td>1.9876</td><td>1.6891</td><td>1.8954</td><td>0.0469</td></tr></table></body></html>

# E.2 INSTRUCTION ENHANCEMENT EXPERIMENTS

Providing more contextual information for instructions enhances the performance of MLLMs. To further improve MLLM evaluation performance, we enhanced the prompts for both SFT and inference stages. As shown in Table 12, our experiments demonstrate that the enhanced prompts significantly increase evaluation accuracy. Specifically, the evaluation using enhanced instructions reduced the MAE metric by half, from 0.120 to 0.006, compared to the original instructions. Additionally, this approach consistently improved evaluation performance across different text-to-image models.

# E.3 MULIT-SCALING RESOLUTIONS EXPERIMENTS

In the design of LLaVA-Next, using multi-scale resolution images as input helps address the issue of detail information loss, which significantly impacts the evaluation of image faithfulness, such as assessing deformations in hands and faces. We conducted a multi-scale image training comparison experiment to validate this approach. The baseline was the 13B LLaVA model with $3 3 6 \times 3 3 6$ resolution input, while the comparison model used images at three resolutions $3 3 6 \times 3 3 6$ , $6 7 2 \times 6 7 2$ , $1 0 0 8 \times 1 0 0 8 )$ as input. As shown in Table 13, training with multi-scale inputs significantly enhanced the model’s understanding of image and achieved better evaluation performance.

# F QUALITATIVE ANALYSIS

As shown in Figure 5, we present a comparison of different evaluation metrics on images generated by four models, including human annotated scores, EVALALIGN, ImageReward (Xu et al., 2024), HPSv2 (Wu et al., 2023a), and PickScore (Kirstain et al., 2024). The digits in the figure represent the ranking for each evaluation metric, with darker colors indicating higher rankings. From the figure, it is evident that our proposed EvalAlign metric closely matches the human rankings across two evaluation dimensions, demonstrating excellent consistency. Specifically, the numbers in the figure represent EVALALIGN scores for the corresponding evaluation aspect, with darker colors indicating higher scores and better generation performance. Note that if the text prompt does not specify a particular style, the style consistency score defaults to 0. From these results, it is evident that the same text-to-image model exhibits significant performance variation across different evaluation aspects.

Table 12: Ablation study on the enhancement of instructions. Results are reported on image faithfulness under different instructions. We observe that enhanced instructions can significantly improves the evaluation metrics. MAE: mean absolute error.   

<html><body><table><tr><td>Method</td><td></td><td>Instruction| SDXL Pixart Wuerstchen SDXL-Turbo</td><td></td><td></td><td></td><td>IF</td><td>SD v1.5 SD v2.1 LCM|MAE</td><td></td><td></td><td></td></tr><tr><td>Human</td><td>一 1</td><td>2.1044 1.8606</td><td></td><td>1.7839</td><td>1.3854</td><td>1.3822</td><td>1.3818</td><td></td><td>1.1766 1.0066</td><td>0</td></tr><tr><td></td><td>×</td><td>1.95651.9286</td><td></td><td>1.8565</td><td>1.1818</td><td>1.3419 1.4801</td><td></td><td>1.4078 1.10510.1201</td><td></td><td></td></tr><tr><td>EVALALIGN</td><td>√</td><td>2.0443 1.9199</td><td></td><td>1.8012</td><td>1.3353</td><td></td><td>1.2960 1.4702</td><td>1.3221 1.0305|0.0064</td><td></td><td></td></tr></table></body></html>

Table 13: Ablation study on multi-scale input. Results are reported on image faithfulness under different input strategy. We observe that input with multi-scale resolution images can improves the evaluation metrics. MAE: mean absolute error.   
Image Faithfulness: A man is drinking water from a glass while another man is observing a dog nearby.   
Image Faithfulness: A farmer wearing a hat feeds a cow with a bell grazing in a field.   

<html><body><table><tr><td>Method</td><td colspan="5">|Multi Scale| SDXL Pixart Wuerstchen SDXL-Turbo</td><td>IF</td><td colspan="3">SD v1.5 SD v2.1</td><td>LCM|MAE</td></tr><tr><td>Human</td><td>1</td><td>|2.1044 1.8606</td><td></td><td>1.7839</td><td>1.3854</td><td>1.3822</td><td>1.3818</td><td>1.1766</td><td>1.0066</td><td>0</td></tr><tr><td></td><td></td><td>1.8105 1.9238</td><td>1.9325</td><td></td><td>1.2078</td><td>1.2247</td><td>1.4540</td><td>1.3012</td><td>1.0554|0.1358</td><td></td></tr><tr><td>EVALALIGN</td><td>√</td><td>2.04431.9199</td><td></td><td>1.8012</td><td>1.3353</td><td>1.296</td><td>1.4702</td><td>1.3221</td><td>1.03050.0064</td><td></td></tr></table></body></html>

![](images/9a1b59b74890f198c99ec6c8d8664480f8d024254d69603130856b682952da21.jpg)  
Figure 5: Qualitative results of EVALALIGN dataset and benchmark. As can be concluded, EVALALIGN is consistently aligned with fine-grained human preference in terms of image faithfulness and text-image alignment, while other methods fail to do so.