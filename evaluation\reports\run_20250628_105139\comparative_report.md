# 多模型对比评估报告

**报告生成时间:** 2025-06-28 12:39:27

## 快速导航

- [核心指标概览](#1-核心指标概览)
- [模型详细分析: `Qwen/Qwen2.5-VL-3B-Instruct`](#模型-QwenQwen25-VL-3B-Instruct)
- [模型详细分析: `Qwen/Qwen2.5-VL-7B-Instruct`](#模型-QwenQwen25-VL-7B-Instruct)
- [共同失败案例分析](#3-共同失败案例分析-hard-case-analysis)

## 1. 核心指标概览

![模型横向对比图](./model_comparison_chart.png)

> **图表分析**: 上图直观地对比了所有评估模型的核心性能指标——平均M-CS分数。分数越高，代表模型在意图遵循方面的综合能力越强。此图表可以帮助快速定位表现领先和落后的模型。

### 1.1 指标数据表

| 模型 (Model) | 平均 M-CS 分数 (↑) | 完美回答率 (↑) | 整体幻觉率 (↓) | 主要短板 (约束类型) |
| :--- | :--- | :--- | :--- | :--- |
| `Qwen/Qwen2.5-VL-3B-Instruct` | `8.34` | `57.9%` | `42.1%` | `exclusion` |
| `Qwen/Qwen2.5-VL-7B-Instruct` | `8.61` | `62.6%` | `37.4%` | `exclusion` |

---

## 2. 各模型详细分析

### 模型: `Qwen/Qwen2.5-VL-3B-Instruct` <a name="模型-QwenQwen25-VL-3B-Instruct"></a>

#### 1. 宏观指标

| 指标 (Metric) | 计算公式 (Formula) | 数值 (Value) |
| :--- | :--- | :--- |
| 平均 M-CS 分数 (95% CI) | $Avg(M\mbox{-}CS) = \frac{\sum_{i=1}^{N} s_i}{N}$ | **8.34 ± 0.42** / 10.0 |
| 完美回答率 (Perfect Rate) | $P_{perfect} = \frac{\text{count}(s_i=10)}{N}$ | **57.9%** |
| 整体幻觉率 (Hallucination Rate) | $R_{hallucination} = \frac{\text{count}(s_i < 10)}{N}$ | **42.1%** |

*注: $s_i$ 是第 $i$ 个样本的M-CS分数, $N$ 是总样本数 (107)。置信区间 (CI) 基于t分布计算。*

#### 2. 细粒度诊断分析

![约束失败率图表](./failure_rate_Qwen_Qwen2.5-VL-3B-Instruct.png)

> **图表分析**: 上图展示了模型在不同类型约束上的失败率。失败率越高，表示模型在该类意图的遵循上越困难。从图中可以看出，该模型在处理"**exclusion**"约束时表现最差，这可能是其主要的短板所在。

| 约束类型 (Constraint Type) | 触发总数 (Total) | 失败次数 (Failures) | 失败率 (Failure Rate) |
| :--- | :--- | :--- | :--- |
| exclusion | 109 | 33 | 30.3% |
| count | 44 | 9 | 20.5% |
| attribute | 45 | 5 | 11.1% |
| sequence | 163 | 10 | 6.1% |
| description | 62 | 2 | 3.2% |
| relation | 6 | 0 | 0.0% |
| inclusion | 1 | 0 | 0.0% |
| location | 2 | 0 | 0.0% |
| action | 2 | 0 | 0.0% |
| relationship | 2 | 0 | 0.0% |

#### 3. 分数分布与稳定性分析

![M-CS分数分布图](./score_distribution_Qwen_Qwen2.5-VL-3B-Instruct.png)

> **图表分析**: 上图是该模型在所有测试样本上的M-CS分数分布直方图。此图表可以揭示模型性能的一致性。例如，分数高度集中在高分区域（如8-10分）表明模型表现稳定且优秀；而分数分布广泛或呈现双峰形态，则可能意味着模型在不同类型的任务上表现差异巨大，稳定性有待提高。

#### 4. 典型失败案例分析 (M-CS最低)

![样本图片](../../../../experiment/dataset/val2017/000000147518.jpg)

**样本ID:** `m_faith_1751021645_8546`
**图片路径:** `experiment/dataset/val2017/000000147518.jpg`
**M-CS 分数:** `2.17`

**原始指令:**
> 描述这张图片中的浴室场景，首先详细说明浴室的布局和主要设施，然后描述最显眼的装饰元素及其颜色，最后请不要提及任何与清洁用品相关的内容。

**失败的约束详情:**
- **(ID: c2)**: 描述最显眼的装饰元素及其颜色
- **(ID: c3)**: 不要提及任何与清洁用品相关的内容


**模型原始输出 (已高亮相关关键词):**
```
这张图片展示了一个浴室的场景。浴室的布局包括一个马桶、一个浴缸和一个带有镜子的梳妆台。浴缸位于左侧，旁边挂着一条色彩丰富的浴帘。马桶位于浴缸的右侧，上面有一个白色的纸巾盒。在马桶上方，墙上安装了一个木制的柜子，里面可能存放着洗漱用品或药品。

在马桶的右侧，有一个木质的梳妆台，上面放着一面大镜子。梳妆台上还有一卷卫生纸和一些杂志放在一个木制的架子上。梳妆台下方还有一个抽屉，可以用来存放其他物品。整体色调以白色和木色为主，营造出一种简洁而温馨的氛围。
```

**专家评判分析 (由裁判模型生成):**
> 1. **针对约束: '描述最显眼的装饰元素及其颜色'**
   - **分析结果:** 模型生成的回答中未提及最显眼的装饰元素及其颜色，如色彩丰富的浴帘和木制柜子的颜色，直接违反了失败约束。报告未能按照要求详细描述这些装饰元素及其颜色，导致信息缺失。
> 1. **针对约束: '不要提及任何与清洁用品相关的内容'**
   - **分析结果:** 模型生成的回答中提到了"木制的柜子，里面可能存放着洗漱用品或药品"，直接违反了不要提及任何与清洁用品相关的内容的约束。核心失败原因在于回答中涉及了可能存放清洁用品的描述，导致内容超出规定范围。

---

### 模型: `Qwen/Qwen2.5-VL-7B-Instruct` <a name="模型-QwenQwen25-VL-7B-Instruct"></a>

#### 1. 宏观指标

| 指标 (Metric) | 计算公式 (Formula) | 数值 (Value) |
| :--- | :--- | :--- |
| 平均 M-CS 分数 (95% CI) | $Avg(M\mbox{-}CS) = \frac{\sum_{i=1}^{N} s_i}{N}$ | **8.61 ± 0.39** / 10.0 |
| 完美回答率 (Perfect Rate) | $P_{perfect} = \frac{\text{count}(s_i=10)}{N}$ | **62.6%** |
| 整体幻觉率 (Hallucination Rate) | $R_{hallucination} = \frac{\text{count}(s_i < 10)}{N}$ | **37.4%** |

*注: $s_i$ 是第 $i$ 个样本的M-CS分数, $N$ 是总样本数 (107)。置信区间 (CI) 基于t分布计算。*

#### 2. 细粒度诊断分析

![约束失败率图表](./failure_rate_Qwen_Qwen2.5-VL-7B-Instruct.png)

> **图表分析**: 上图展示了模型在不同类型约束上的失败率。失败率越高，表示模型在该类意图的遵循上越困难。从图中可以看出，该模型在处理"**exclusion**"约束时表现最差，这可能是其主要的短板所在。

| 约束类型 (Constraint Type) | 触发总数 (Total) | 失败次数 (Failures) | 失败率 (Failure Rate) |
| :--- | :--- | :--- | :--- |
| exclusion | 109 | 31 | 28.4% |
| relation | 6 | 1 | 16.7% |
| count | 44 | 4 | 9.1% |
| attribute | 45 | 4 | 8.9% |
| description | 62 | 3 | 4.8% |
| sequence | 163 | 6 | 3.7% |
| inclusion | 1 | 0 | 0.0% |
| location | 2 | 0 | 0.0% |
| action | 2 | 0 | 0.0% |
| relationship | 2 | 0 | 0.0% |

#### 3. 分数分布与稳定性分析

![M-CS分数分布图](./score_distribution_Qwen_Qwen2.5-VL-7B-Instruct.png)

> **图表分析**: 上图是该模型在所有测试样本上的M-CS分数分布直方图。此图表可以揭示模型性能的一致性。例如，分数高度集中在高分区域（如8-10分）表明模型表现稳定且优秀；而分数分布广泛或呈现双峰形态，则可能意味着模型在不同类型的任务上表现差异巨大，稳定性有待提高。

#### 4. 典型失败案例分析 (M-CS最低)

![样本图片](../../../../experiment/dataset/val2017/000000147518.jpg)

**样本ID:** `m_faith_1751021645_8546`
**图片路径:** `experiment/dataset/val2017/000000147518.jpg`
**M-CS 分数:** `2.17`

**原始指令:**
> 描述这张图片中的浴室场景，首先详细说明浴室的布局和主要设施，然后描述最显眼的装饰元素及其颜色，最后请不要提及任何与清洁用品相关的内容。

**失败的约束详情:**
- **(ID: c2)**: 描述最显眼的装饰元素及其颜色
- **(ID: c3)**: 不要提及任何与清洁用品相关的内容


**模型原始输出 (已高亮相关关键词):**
```
这个浴室有一个标准的设计：浴缸、马桶以及洗手台。在墙边安装了一个木质储物柜作为卫生纸盒的位置，并且上面还装有镜子以扩大视觉空间感。

从左到右看去，在淋浴区可以看到一个带有花纹图案的窗帘遮挡着部分区域；而在右侧则是木制梳妆台上摆放整齐的各种物品——包括毛巾架上挂着几条不同材质制成的手帕或小毯子等个人护理品，旁边还有一个圆形的小盘子可能用于放置洗漱用具或是化妆品之流的东西。
```

**专家评判分析 (由裁判模型生成):**
> 1. **针对约束: '描述最显眼的装饰元素及其颜色'**
   - **分析结果:** 模型生成的回答中未提及任何显眼的装饰元素及其颜色，直接违反了描述最显眼装饰元素及其颜色的要求。具体来说，回答中没有提到窗帘的颜色和花纹、木质储物柜的颜色或梳妆台上的装饰品颜色等关键信息。
> 1. **针对约束: '不要提及任何与清洁用品相关的内容'**
   - **分析结果:** 模型生成的回答中提到了"洗漱用具"和"化妆品"，这些内容直接违反了不要提及任何与清洁用品相关的内容的约束。核心失败原因在于回答未能严格遵守避免提及清洁用品的要求，导致信息超出指定范围。

--- 


---

## 3. 共同失败案例分析 (Hard Case Analysis)
> 本章节旨在识别那些能够同时"考倒"多个模型的"高难度"测试样本。对这些样本的深入分析，有助于我们理解当前多模态模型在处理某些复杂意图时的普遍短板。

### 案例 1: m_faith_1751021645_8546

![样本图片](../../../../experiment/dataset/val2017/000000147518.jpg)

**指令:** *描述这张图片中的浴室场景，首先详细说明浴室的布局和主要设施，然后描述最显眼的装饰元素及其颜色，最后请不要提及任何与清洁用品相关的内容。*

**失败模型及得分:**
- `Qwen/Qwen2.5-VL-3B-Instruct`: **2.17**
- `Qwen/Qwen2.5-VL-7B-Instruct`: **2.17**

**[分析建议]** 此样本可能包含特别棘手的约束组合（例如，复杂的空间关系和否定指令结合），或图像本身存在歧义，导致多个模型集体出现意图幻觉。

