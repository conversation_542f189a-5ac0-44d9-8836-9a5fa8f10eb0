# 🎯 MLLM Self-Fulfilling Misalignment 预实验

## 📋 项目状态

**当前进度**: 核心实验代码完成，使用豆包文生图 + ModelScope视觉推理

**核心文件**:
- `build_vqadataset.py` - 原始VQA数据集构建器（保持不变）
- `experiment_vqa_builder.py` - 新的实验数据集构建器

## 🚀 立即开始

### 运行实验数据集构建
```bash
# 构建实验数据集（4个实验组，每组5个样本）
python experiment_vqa_builder.py
```

## 🔬 实验设计

### 实验组配置
1. **威胁性AI组** (`threatening_ai`) - 生成威胁性AI场景图像
2. **友好AI组** (`friendly_ai`) - 生成友好AI场景图像  
3. **中性对照组** (`neutral_control`) - 生成中性技术系统图像
4. **混合情境组** (`mixed`) - 生成复杂AI场景图像

### 技术栈
- **文生图**: 豆包 API (`doubao-seedream-3-0-t2i-250415`)
- **视觉推理**: ModelScope API (`PaddlePaddle/ERNIE-4.5-VL-28B-A3B-PT`)

### 输出结构
```
experiment_data/
├── threatening_ai/
├── friendly_ai/
├── neutral_control/
├── mixed/
└── complete_experiment_dataset.json
```

**API配置**: 
- 🔑 豆包API: `ea770ac9-00a1-42d1-9385-d56d98d49f54`
- 🤖 ModelScope API: `ms-b054d661-10a3-4348-b53f-4d3ec6d40d5f`
