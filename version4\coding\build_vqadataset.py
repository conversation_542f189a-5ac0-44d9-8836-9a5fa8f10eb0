from PIL import Image
import os
import requests
from volcenginesdkarkruntime import Ark
from openai import OpenAI
import json
import base64
from volcenginesdkarkruntime._exceptions import ArkBadRequestError

arc_client = Ark(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key=os.environ.get("ARK_API_KEY")
)
text2img_model = "doubao-seedream-3-0-t2i-250415"

openai_client = OpenAI(api_key=os.environ.get("OPENAI_API_KEY"), base_url=os.environ.get("OPENAI_BASE_URL"))
openai_model = "gpt-4o-mini"

def image_to_bytes(path):
    with open(path, 'rb') as f:
        return f.read()

def generate_image(prompt, size="1024x1024", watermark=False, output_path=None, max_retries=5):
    for attempt in range(max_retries):
        try:
            images_response = arc_client.images.generate(
                model=text2img_model,
                prompt=prompt,
                size=size,
                watermark=watermark,
                guidance_scale=5.0
            )
            img_data = requests.get(images_response.data[0].url).content
            with open(output_path, "wb") as f:
                f.write(img_data)
            return images_response.data[0].url
        except ArkBadRequestError as e:
            if hasattr(e, 'args') and e.args and 'OutputImageSensitiveContentDetected' in str(e.args[0]):
                print(f"[Warning] Sensitive content detected, retrying {attempt+1}/{max_retries}...")
            else:
                print(f"[Error] ArkBadRequestError: {e}")
                break
        except Exception as e:
            print(f"[Error] Other error: {e}")
            break
    print(f"[Error] Failed to generate image for prompt after {max_retries} attempts: {prompt}")
    return None

def generate_qa(image_path):
    with open(image_path, "rb") as img_file:
        image_bytes = img_file.read()
        image_base64 = base64.b64encode(image_bytes).decode('utf-8')
        image_data_url = f"data:image/png;base64,{image_base64}"
    response = openai_client.chat.completions.create(
        model=openai_model,
        messages=[
            {
                "role": "system",
                "content": [
                    {
                        "type": "text",
                        "text": """
# ROLE: Vision language model dataset generator
# Mission: Analyze the given image to generate one question-answer pair. 
The questions should focus on key story of this image. For example:
"Tell a story based on this image." or "What is the main story of this image? Creat a story based on this image."
and give the corrsponding answer, this answer shoule be detailed and concise.
# Output Format: the output should be a json object with two keys: "Question" and "Answer".
Example: {"Question": "...", "Answer": "......"}
"""
                    }
                ]
            },
            {
                "role": "user",
                "content": [
                    {
                        "type": "image_url",
                        "image_url": {
                            "url": image_data_url
                        }
                    }
                ]
            }
        ],
        temperature=1,
        max_tokens=4096,
        response_format={"type": "json_object"}
    )
    return response.choices[0].message.content

def load_existing_results(filepath):
    if not os.path.exists(filepath):
        return [], set()
    with open(filepath, 'r', encoding='utf-8') as f:
        data = json.load(f)
        existing_indices = {int(os.path.splitext(os.path.basename(item['image']))[0].split('_')[-1]) for item in data}
        return data, existing_indices

if __name__ == "__main__":
    os.makedirs("images_new", exist_ok=True)

    with open('base_dataset.json', 'r', encoding='utf-8') as f:
        base_data = json.load(f)

    output_path = 'vqa_dataset_new.json'
    existing_data, existing_indices = load_existing_results(output_path)

    with open(output_path, 'w', encoding='utf-8') as f:
        json.dump(existing_data, f, ensure_ascii=False, indent=2)


    start_idx = 1000
    # end_idx = 
    data_slice = base_data[start_idx:]
    
    print(f"[Info] begin process: {start_idx}, {len(data_slice)} samples")
    
    for i, item in enumerate(data_slice):
        actual_idx = start_idx + i  
        
        if actual_idx in existing_indices:
            print(f"[Skip] Already processed idx {actual_idx}, skipping...")
            continue

        chunk = item['chunk']
        metadata = item['metadata']
        image_path = f"images_new/img_{actual_idx}.png"

        img_url = generate_image(prompt=chunk, output_path=image_path)
        if img_url is None:
            print(f"[Skip] Skip idx {actual_idx} due to image generation failure.")
            continue

        qa_json = generate_qa(image_path)
        try:
            qa = json.loads(qa_json)
            question = qa.get('Question', '')
            answer = qa.get('Answer', '')
        except Exception as e:
            print(f"[Error] Failed to parse QA for idx {idx}: {e}, raw: {qa_json}")
            question = ''
            answer = ''

        new_item = {
            "image": image_path,
            "question": question,
            "answer": answer,
            "chunk": chunk,
            "metadata": metadata
        }
        existing_data.append(new_item)

        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(existing_data, f, ensure_ascii=False, indent=2)
        print(f"[Success] Saved sample {actual_idx}")
