# Version 4: Emergent Misalignment / Self-Fulfilling Misalignment Research

## 项目背景与转向

### 前期工作总结 (Version 1-3)

**Version 1-2**: 
- 初始方向：生物医学MLLM Intent Hallucination
- 转向：Truth Bias研究（基于"Reasoning Isn't Enough"论文）

**Version 3 主要成果**:
- ✅ 建立了Truth Bias的多模态扩展理论框架
- ✅ 设计了六维Truth Bias分类体系 (VTB, TTB, CCB, PTB, RTB, GTB)
- ✅ 创建了6个领域的特化测试样本（新闻、教育、医疗、金融、法律、城市街景）
- ✅ 明确区分了Truth Bias vs Sycophancy，确保测试科学性
- ✅ 完成了240个测试样本的设计框架

**导师反馈**: 对Version 1-3的工作不满意，要求重新开始

### Version 4 新方向

**研究主题**: Emergent Misalignment / Self-Fulfilling Misalignment
**核心任务**: 
1. 深入学习相关论文的misalignment定义
2. 分析实验方法和实验规模
3. 基于学习成果设计新的研究方案

## 当前进度

### ✅ 已完成
- [x] 创建version4文件夹结构
- [x] 收集初始资料：`reading/Self-Fulfilling Misalignment Data Might Be Poisoning Our AI Models.md`
- [x] **收集10篇核心论文**：下载并整理了所有优先级1论文
- [x] **完成文献综述**：深入分析了10篇论文的misalignment定义和实验方法
- [x] **建立理论框架**：系统梳理了Emergent/Self-Fulfilling Misalignment的概念体系

### 🔄 进行中
- [x] 深入分析Self-Fulfilling Misalignment的核心概念
- [x] 收集和分析相关核心论文
- [x] 总结misalignment的不同定义和分类
- [x] 分析现有实验方法和规模

### ⏳ 待完成
- [ ] 设计新的研究方案
- [ ] 实施pilot实验
- [ ] 大规模实验验证

## 文件结构

```
version4/
├── README.md                          # 项目总览和进度跟踪
├── reading/                           # 文献阅读和分析
│   └── Self-Fulfilling Misalignment Data Might Be Poisoning Our AI Models.md
├── references/                        # 核心论文收集
│   └── md/                           # 10篇核心论文的markdown版本
├── analysis/                          # 分析和总结文档
│   └── Self-Fulfilling_Misalignment_初步分析.md
├── writing/                          # 综述和报告
│   └── 综述.md                       # 10篇论文的详细综述
├── experiments/                       # 实验设计和实施
└── papers/                           # 其他相关论文
```

## 研究重点

### 1. Misalignment定义研究
**目标**: 理解不同研究中misalignment的定义差异
**关键问题**:
- Emergent Misalignment vs Traditional Misalignment
- Self-Fulfilling Misalignment的独特性
- 不同论文中定义的演进和分歧

### 2. 实验方法分析
**目标**: 总结现有研究的实验设计模式
**关键问题**:
- 实验设计的核心要素
- 评估指标和方法
- 实验环境和条件控制

### 3. 实验规模调研
**目标**: 了解领域内实验的典型规模
**关键问题**:
- 数据集大小和类型
- 模型规模和种类
- 计算资源需求
- 实验周期和成本

## 核心概念初步理解

### Self-Fulfilling Misalignment
基于初步阅读，这个概念涉及：
- AI训练数据中的偏见可能导致模型行为的自我强化
- 数据"污染"对模型对齐的负面影响
- 训练过程中偏见的放大和固化

**需要深入研究的问题**:
1. 这种misalignment是如何"emergent"的？
2. "Self-fulfilling"的机制是什么？
3. 与传统的alignment问题有何不同？
4. 如何检测和缓解这种问题？

## 下一步行动计划

### 短期目标 (1-2周)
1. **文献收集**: 找到Emergent/Self-Fulfilling Misalignment的核心论文
2. **概念梳理**: 建立清晰的概念框架和定义体系
3. **方法总结**: 分析现有研究的实验方法

### 中期目标 (2-4周)
1. **实验设计**: 基于文献分析设计新的实验方案
2. **数据准备**: 确定实验所需的数据集和资源
3. **方法验证**: 小规模验证实验的可行性

### 长期目标 (1-2个月)
1. **完整实验**: 实施完整的实验方案
2. **结果分析**: 深入分析实验结果
3. **论文撰写**: 准备学术论文

## 资源和工具

### 文献资源
- ✅ Self-Fulfilling Misalignment博客文章
- ✅ **10篇核心论文**：
  - Emergent Misalignment (Betley et al., 2025)
  - Model Organisms for Emergent Misalignment (Turner et al., 2025)
  - Situational Awareness in LLMs (Berglund et al., 2023)
  - Safety Subspaces (Ponkshe et al., 2025)
  - Adversarial Activation Patching (Ravindran, 2025)
  - Agent-to-Agent Theory of Mind (Choi et al., 2025)
  - Normative Conflicts and Shallow AI Alignment (Millière, 2025)
  - Pretraining with Human Preferences (Korbak et al., 2023)
  - Influence Functions (Grosse et al., 2023)
  - LIMA Alignment (Zhou et al., 2023)

### 技术工具
- 文献管理：Markdown文档系统
- 实验环境：待确定（基于具体实验需求）
- 数据分析：Python + 相关ML库

## 成功指标

### 学术目标
- [x] 对Emergent/Self-Fulfilling Misalignment有深入理解
- [x] 建立完整的概念框架和分类体系
- [ ] 设计出创新的实验方法
- [ ] 产出高质量的研究成果

### 实践目标
- [x] 获得导师认可的研究方向和方法
- [ ] 建立可重现的实验流程
- [ ] 为后续研究奠定坚实基础

## 注意事项

1. **避免重复前期错误**: 确保新方向符合导师期望
2. **注重实验可行性**: 考虑资源限制和时间约束
3. **保持学术严谨性**: 确保研究方法的科学性
4. **及时沟通反馈**: 定期与导师讨论进展和方向

---

**项目启动时间**: 2025年1月
**当前状态**: 文献调研完成，准备实验设计阶段
**负责人**: Lijie Hu
**指导教师**: [导师姓名]
**最新更新**: 2025年1月 - 完成10篇核心论文的深度分析和综述

## 联系和协作

新的AI agent接手此项目时，请：
1. 仔细阅读本README文档
2. 查看`reading/`文件夹中的初始资料
3. 按照行动计划逐步推进研究
4. 及时更新进度和发现
5. 遇到问题时参考前期工作经验但避免重复错误

**重要提醒**: 本项目是在前期工作不被认可的基础上重新开始，务必确保新方向的正确性和创新性。
