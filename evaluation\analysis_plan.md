# M-FAITH 评估结果分析与报告方案

## 1. 核心评估指标 (Core Evaluation Metrics)

为了对一个模型进行全面、公正的评判，我们将采用以下三个核心的宏观量化指标。

### 1.1 平均多模态约束分数 (Average M-CS)

-   **定义**: 模型在所有测试样本上获得的 `M-CS` 分数的算术平均值。
-   **计算**: `总分 / 样本总数`
-   **作用**: 反映了模型在遵循各类意图约束时的**普遍表现 (General Performance)**。这是一个衡量模型整体水平的基础指标。

### 1.2 完美回答率 (Perfect Rate)

-   **定义**: 回答的 `M-CS` 分数恰好为 10 分的样本在总样本中的比例。
-   **计算**: `(M-CS=10 的样本数 / 样本总数) * 100%`
-   **作用**: 反映了模型实现**完美意图对齐 (Perfect Intent Alignment)** 的能力。这是一个非常严格的指标，衡量模型在多大程度上能做到完全不犯任何意图幻觉错误。

### 1.3 整体幻觉率 (Overall Hallucination Rate)

-   **定义**: 只要 `M-CS` 分数不为10，就计为一个"有幻觉"的样本。此指标衡量有幻觉样本的占比。
-   **计算**: `1 - Perfect Rate`
-   **作用**: 从反面来衡量模型**产生意图幻觉的倾向性**。

---

## 2. 细粒度诊断分析 (Fine-grained Diagnostic Analysis)

除了宏观指标，我们还必须进行微观诊断，以理解模型"为什么"会失败，这对于提出改进方向至关重要。

### 2.1 按约束类型分析准确率

-   **目标**: 探究模型对哪一类约束的处理能力最弱。
-   **方法**: 我们需要读取 `evaluation_results.jsonl` 中的 `judgement.details` 字段，结合原始数据中的约束 `type`（如 `inclusion`, `exclusion`, `count` 等），分别计算模型在每种类型约束上的满足率。
-   **产出**: 一个展示模型在不同约束类型上准确率的图表（类似《Beyond Facts》中的图4），清晰地暴露模型的"短板"。

### 2.2 按任务/复杂度分析性能

-   **目标**: 评估模型的鲁棒性 (Robustness)。
-   **方法**:
    -   **任务维度**: 分别计算模型在 `complex_description`, `rag_qa`, `visual_intent` 这三种不同任务上的核心指标。
    -   **复杂度维度**: 根据样本的 `constraint_count`（约束数量）对样本进行分组（如：1-2个约束，3-4个约束，5+个约束），分析模型在不同复杂度下的性能衰减情况。
-   **产出**: 不同任务和复杂度下的性能对比表格或曲线图。

### 2.3 典型失败案例定性分析 (Case Study)

-   **目标**: 直观地展示模型的失败模式。
-   **方法**: 挑选出一些得分较低（如 M-CS < 5）的典型案例，将【图片】、【指令】、【模型回答】和【裁判的详细评分】并列展示，并用自然语言分析其失败的根本原因（是"遗漏"了某个约束，还是"曲解"了某个约束）。

---

## 3. 最终报告与模型对比

当评估多个模型时，我们的最终目标是产出一份清晰的横向对比报告。报告的核心应该是一个综合性的对比表格，其结构可参考：

| Model                      | Avg. M-CS (↓) | Perfect Rate (↑) | Overall Hallucination Rate (↓) | Failed most on (Constraint Type) |
| -------------------------- | ------------- | ---------------- | ------------------------------ | -------------------------------- |
| **Qwen/Qwen2.5-VL-3B**     | 6.15          | 20%              | 80%                            | exclusion                        |
| **LLaVA-1.6-34B**          | (TBD)         | (TBD)            | (TBD)                          | (TBD)                            |
| **GPT-4o**                 | (TBD)         | (TBD)            | (TBD)                          | (TBD)                            |

通过这套"宏观量化 + 微观诊断"的组合拳，我们就能对任何一个MLLM的意图幻觉水平，给出一个既**合理**又**鲁棒**的综合性评判。 