import json
import os
import argparse

def is_sample_valid(sample_data):
    """
    检查单个样本中的所有约束是否都有效。
    一个有效的约束必须是字典，并且包含一个非空的 'content' 字段。
    """
    try:
        constraints = sample_data.get('original_sample', {}).get('instruction', {}).get('constraints', [])
        
        # 如果没有约束，或者约束列表为空，我们视其为有效样本
        if not constraints:
            return True
            
        for constraint in constraints:
            if not isinstance(constraint, dict) or not constraint.get('content'):
                return False # 发现一个无效约束，则整个样本无效
        
        return True # 所有约束都通过了检查
    except Exception as e:
        print(f"检查样本时发生意外错误: {e}")
        return False

def clean_dataset_file(input_path):
    """
    读取一个JSONL文件，剔除包含无效约束的行，并写入一个新的 cleaned 文件。
    """
    if not os.path.exists(input_path):
        print(f"错误: 输入文件未找到于 '{input_path}'")
        return

    # 定义输出文件路径
    directory, filename = os.path.split(input_path)
    name, ext = os.path.splitext(filename)
    output_path = os.path.join(directory, f"{name}_cleaned{ext}")

    lines_read = 0
    lines_written = 0

    print(f"开始清洗文件: {input_path}")

    with open(input_path, 'r', encoding='utf-8') as f_in, \
         open(output_path, 'w', encoding='utf-8') as f_out:
        
        for line in f_in:
            lines_read += 1
            try:
                data = json.loads(line)
                if is_sample_valid(data):
                    f_out.write(line)
                    lines_written += 1
                else:
                    print(f"  -> 已剔除样本 (ID: {data.get('evaluation_id', 'Unknown')}), 原因: 包含无效约束。")
            except json.JSONDecodeError:
                print(f"  -> 警告: 跳过格式错误的JSON行: {line.strip()}")

    print("\n清洗完成！")
    print(f"总共读取行数: {lines_read}")
    print(f"有效并写入行数: {lines_written}")
    print(f"已剔除行数: {lines_read - lines_written}")
    print(f"纯净的数据已保存至: {output_path}")


def main():
    parser = argparse.ArgumentParser(
        description="数据清洗工具：自动剔除JSONL数据集中包含无效约束的样本。",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        "input_file",
        type=str,
        help="需要清洗的JSONL文件路径 (例如: evaluation/output/evaluation_results.jsonl)"
    )
    args = parser.parse_args()

    clean_dataset_file(args.input_file)

if __name__ == "__main__":
    main() 