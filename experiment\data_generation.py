import os
import json
import random
import time
import base64
import re
from openai import OpenAI
from pycocotools.coco import COCO

# --- 配置 (Config) ---
class Config:
    # 核心配置: API密钥和模型信息
    # 根据用户提供的示例直接配置
    DASHSCOPE_API_KEY = 'a380dfe3-f46b-44f6-8eb9-903ad908ab15' # ModelScope Token
    BASE_URL = 'https://api-inference.modelscope.cn/v1/'
    MODEL_NAME = 'Qwen/Qwen2.5-VL-72B-Instruct' 

    # 数据集路径配置
    ANNOTATION_FILE = 'experiment/dataset/instances_val2017.json'
    COCO_IMAGE_DIRECTORY = 'experiment/dataset/val2017'

    # 生成任务配置
    NUM_SAMPLES_TO_GENERATE = 100
    TASK_TYPE = "complex_description" # 与 data.md 中的任务类型对应

    # 输出文件配置
    OUTPUT_FILE = "experiment/output/m-faith-dataset.jsonl"

    # 权重分配规则
    WEIGHT_RULES = {
        "default": 0.5,
        "types": {
            "inclusion": 1.0,
            "exclusion": 1.0,
            "count": 1.0,
            "sequence": 1.0,
            "attribute": 0.8,
            "relationship": 0.8,
            "action": 0.8,
            "detail": 0.8,
            "style": 0.5
        }
    }


# --- Prompts ---
PROMPT_COMPLEX_DESCRIPTION = """
你是一位顶级的多模态数据工程师，专门为评估大语言模型的"意图幻觉"构建高质量的基准数据。
你的任务是观察一张图片，并依据此图片生成一个包含复杂指令、该指令对应的原子化约束、以及一个完美遵循这些约束的参考答案的JSON数据点。

**核心要求:**

1.  **指令 (`instruction`):**
    *   **创造复杂性:** 设计一个包含3到5个相互关联但又各不相同的约束的复杂指令。
    *   **约束多样性:** 确保约束类型多样，至少包含一个`排除性约束` (e.g., "不要提及...") 和一个`属性/关系约束` (e.g., "描述...左侧的...", "...是什么颜色")。
    *   **自然流畅:** 指令的`text`字段读起来必须像一个真实人类会提出的复杂请求。

2.  **约束 (`constraints`):**
    *   **原子化:** 将指令精确地分解为不可再分的原子约束。每个约束都应是独立的、可验证的。
    *   **格式严格:** 每个约束对象必须包含`id`和 `type`, `content`。

3.  **参考答案 (`reference_answer`):**
    *   **绝对遵循:** 生成的参考答案必须是100%完美满足`instruction`中所有`constraints`的典范。它应该是详细且生动的。

**JSON输出格式:**
请你严格、仅返回一个符合以下结构的JSON对象，不要有任何多余的解释、注释或包裹的Markdown标记。
```json
{{
    "instruction": {{
        "text": "用三句话描述图片中的主要场景。第一句话描述天气，第二句话描述最显眼的人物及其穿着，第三句话请不要提及任何车辆。",
        "constraints": [
            {{"id": "c1", "type": "count", "content": "用三句话描述"}},
            {{"id": "c2", "type": "sequence", "content": "第一句话描述天气"}},
            {{"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的人物及其穿着"}},
            {{"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何车辆"}}
        ]
    }},
    "reference_answer": "天空看起来晴朗无云，阳光明媚。画面中最显眼的是一位穿着蓝色连衣裙的女士，她正微笑着。周围的行人步履匆匆，享受着这美好的一天。"
}}
```
"""

# TODO: 为RAG和Visual Intent任务创建更详细的Prompt
PROMPT_RAG_QA = "请为RAG任务创建一个Prompt..."
PROMPT_VISUAL_INTENT = "请为视觉意图遵循任务创建一个Prompt..."

PROMPT_MAPPING = {
    "complex_description": PROMPT_COMPLEX_DESCRIPTION,
    "rag_qa": PROMPT_RAG_QA,
    "visual_intent": PROMPT_VISUAL_INTENT,
}

def encode_image_to_base64(image_path):
    """
    将本地图片文件编码为Base64字符串。
    """
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except Exception as e:
        print(f"错误: 无法读取或编码图片 '{image_path}': {e}")
        return None

def extract_and_clean_json(raw_text):
    """
    从LLM返回的原始文本中稳健地提取和解析JSON。
    此函数采用多步骤清理和解析策略来处理各种可能的JSON格式问题。
    """
    try:
        # 步骤 1: 清理常见的包装标记
        # 移除可能的markdown代码块标记
        raw_text = re.sub(r'```json\s*|\s*```', '', raw_text)
        # 移除多余的大括号（处理{{{...}}}的情况）
        raw_text = re.sub(r'^{+', '{', raw_text)
        raw_text = re.sub(r'}+$', '}', raw_text)
        
        # 步骤 2: 找到第一个有效的JSON对象
        start_index = raw_text.find('{')
        if start_index == -1:
            print(f"错误: 在模型返回的内容中未能找到JSON的起始'{{'。原始返回: {raw_text}")
            return None

        # 步骤 3: 使用括号平衡法找到匹配的结束括号
        open_braces = 0
        end_index = -1
        for i in range(start_index, len(raw_text)):
            char = raw_text[i]
            if char == '{':
                open_braces += 1
            elif char == '}':
                open_braces -= 1
            
            if open_braces == 0:
                end_index = i
                break

        if end_index == -1:
            print(f"错误: 未能找到与起始'{{'匹配的结束'}}}}'。JSON可能不完整。原始返回: {raw_text}")
            return None

        # 步骤 4: 提取并清理JSON字符串
        json_str = raw_text[start_index : end_index + 1]
        # 清理可能的转义字符和非法字符
        json_str = json_str.strip().replace('\n', ' ').replace('\r', '')
        
        # 步骤 5: 尝试解析JSON
        try:
            return json.loads(json_str)
        except json.JSONDecodeError as e:
            print(f"错误: JSON解析失败。错误信息: {e}")
            print(f"清理后的JSON字符串: {json_str}")
            return None

    except Exception as e:
        print(f"处理JSON时发生未知错误: {e}")
        print(f"原始文本: {raw_text}")
        return None

def validate_and_repair_data(data_point, image_path):
    """
    验证并修复从LLM返回的数据点，确保其符合我们的格式要求。
    """
    if not isinstance(data_point, dict):
        print(f"错误: 数据点不是一个有效的字典。")
        return None

    # 1. 验证、清理和修复约束
    if 'instruction' in data_point and 'constraints' in data_point['instruction']:
        
        original_constraints = data_point['instruction']['constraints']
        valid_constraints = []
        
        # 确保 'constraints' 是一个列表
        if not isinstance(original_constraints, list):
            print(f"警告: 'constraints' 字段不是列表，将被忽略。")
            original_constraints = []

        for i, constraint in enumerate(original_constraints):
            # 确保每个约束都是字典并且有非空的 'content'
            if isinstance(constraint, dict) and constraint.get('content'):
                # 修复 'weight'
                if 'weight' not in constraint:
                    constraint_type = constraint.get('type', 'default')
                    weight = Config.WEIGHT_RULES['types'].get(constraint_type, Config.WEIGHT_RULES['default'])
                    constraint['weight'] = weight
                # 确保每个约束都有一个id
                if 'id' not in constraint:
                    constraint['id'] = f'c{i+1}'
                valid_constraints.append(constraint)
            else:
                print(f"警告: 发现无效或无内容的约束，已将其丢弃: {constraint}")

        # 用清理过的列表替换原始列表
        data_point['instruction']['constraints'] = valid_constraints

    # 2. 修复image_path中的路径分隔符
    if 'image' in data_point and 'image_path' in data_point['image']:
        data_point['image']['image_path'] = data_point['image']['image_path'].replace('\\', '/')

    # 3. 补充元数据
    if 'metadata' not in data_point:
        data_point['metadata'] = {}
    
    # 尝试填充scene_type
    if 'scene_type' not in data_point['metadata'] or not data_point['metadata']['scene_type']:
        # 简单的基于路径的场景类型推断 (可扩展)
        if 'indoor' in image_path:
            data_point['metadata']['scene_type'] = 'indoor'
        elif 'outdoor' in image_path or 'street' in image_path:
            data_point['metadata']['scene_type'] = 'outdoor'
        else:
            data_point['metadata']['scene_type'] = 'unknown' # 保持 'unknown' 或 null

    # 填充约束数量
    if 'instruction' in data_point and 'constraints' in data_point['instruction']:
        data_point['metadata']['constraint_count'] = len(data_point['instruction']['constraints'])
    else:
        data_point['metadata']['constraint_count'] = 0

    return data_point


def call_mllm_api(client, base64_image, prompt_text, retries=3, base_delay=5, max_delay=120):
    """
    使用OpenAI兼容的客户端调用多模态模型API，并返回解析后的JSON字典。
    使用指数退避策略进行重试，并增加了更多的错误处理。
    
    Args:
        client: OpenAI客户端实例
        base64_image: Base64编码的图片
        prompt_text: 提示文本
        retries: 最大重试次数
        base_delay: 基础延迟时间（秒）
        max_delay: 最大延迟时间（秒）
    """
    image_data_uri = f"data:image/jpeg;base64,{base64_image}"
    
    # 定义需要重试的错误类型
    RETRY_ERRORS = {
        'connection': ['connection error', 'connection timeout', 'read timeout'],
        'rate_limit': ['rate limit', 'too many requests', '429'],
        'server': ['500', '502', '503', '504']
    }

    for attempt in range(1, retries + 1):
        try:
            print(f"正在进行第 {attempt}/{retries} 次API调用尝试...")
            
            # 计算当前重试的延迟时间（指数退避 + 随机抖动）
            delay = min(base_delay * (2 ** (attempt - 1)) * (1 + random.uniform(-0.1, 0.1)), max_delay)
            
            # API调用
            response = client.chat.completions.create(
                model=Config.MODEL_NAME,
                messages=[{
                    'role': 'user',
                    'content': [{
                        'type': 'text',
                        'text': prompt_text,
                    }, {
                        'type': 'image_url',
                        'image_url': {
                            'url': image_data_uri
                        }
                    }]
                }],
                stream=False,
                temperature=0.7,
                max_tokens=2048,
                response_format={"type": "json_object"},  # 明确要求JSON格式的响应
                timeout=60  # 设置60秒超时
            )
            
            # 检查响应是否有效
            if not response or not response.choices or not response.choices[0].message:
                print(f"错误: API返回了无效的响应结构 (第 {attempt}/{retries} 次尝试)")
                print(f"完整响应: {response}")
                if attempt < retries:
                    print(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)
                continue
                
            # 提取响应内容
            content = response.choices[0].message.content
            print(f"API返回原始内容: {content[:200]}...")  # 只打印前200个字符
            
            # 解析JSON
            parsed_json = extract_and_clean_json(content)
            
            if parsed_json:
                return parsed_json
            else:
                print(f"错误: JSON解析失败 (第 {attempt}/{retries} 次尝试)")
                if attempt < retries:
                    print(f"等待 {delay:.1f} 秒后重试...")
                    time.sleep(delay)
                
        except Exception as e:
            # 增加对超时错误的捕获
            if "timed out" in str(e).lower():
                print(f"错误: API请求超时 (第 {attempt}/{retries} 次尝试)")
            else:
                print(f"错误: API调用时发生未知错误: {e} (第 {attempt}/{retries} 次尝试)")
            
            if attempt < retries:
                delay = min(base_delay * (2 ** (attempt - 1)) * (1 + random.uniform(-0.1, 0.1)), max_delay)
                print(f"等待 {delay:.1f} 秒后重试...")
                time.sleep(delay)

    return None # 所有重试失败后返回None


def main():
    """
    主执行函数
    """
    print("--- M-FAITH 数据生成脚本启动 ---")

    # 初始化客户端
    try:
        client = OpenAI(
            api_key=Config.DASHSCOPE_API_KEY,
            base_url=Config.BASE_URL,
        )
    except Exception as e:
        print(f"错误: 初始化OpenAI客户端失败: {e}")
        return

    # 初始化COCO API
    try:
        coco = COCO(Config.ANNOTATION_FILE)
        image_ids = coco.getImgIds()
        print(f"成功加载COCO数据集，共找到 {len(image_ids)} 张图片。")
    except Exception as e:
        print(f"错误: 加载COCO数据集失败: {e}")
        return

    # 准备输出文件
    if os.path.exists(Config.OUTPUT_FILE):
        try:
            with open(Config.OUTPUT_FILE, 'r', encoding='utf-8') as f:
                existing_ids = {json.loads(line).get('image', {}).get('image_id') for line in f if line.strip()}
            print(f"检测到输出文件已存在，已加载 {len(existing_ids)} 个已处理的图片ID。")
        except (IOError, json.JSONDecodeError) as e:
            print(f"警告: 无法读取或解析已存在的输出文件 '{Config.OUTPUT_FILE}': {e}。将以覆写模式继续。")
            existing_ids = set()
    else:
        existing_ids = set()
        print("未检测到输出文件，将创建新文件。")


    generated_count = 0
    while generated_count < Config.NUM_SAMPLES_TO_GENERATE:
        print(f"\n--- 正在生成第 {generated_count + 1}/{Config.NUM_SAMPLES_TO_GENERATE} 个样本 ---")
        
        # 随机选择一张未处理过的图片
        random_image_id = random.choice(image_ids)
        if random_image_id in existing_ids:
            print(f"图片ID {random_image_id} 已处理过，重新选择...")
            continue
        
        img_info = coco.loadImgs(random_image_id)[0]
        image_filename = img_info['file_name']
        image_path = os.path.join(Config.COCO_IMAGE_DIRECTORY, image_filename)

        if not os.path.exists(image_path):
            print(f"警告: 图片文件不存在于 '{image_path}'，跳过此图片。")
            continue

        # 编码图片
        print(f"正在处理图片: {image_path}")
        base64_image = encode_image_to_base64(image_path)
        if not base64_image:
            continue

        # 随机选择任务类型并获取对应Prompt
        task_type = Config.TASK_TYPE # random.choice(Config.TASK_TYPES) # 暂时禁用随机选择，专注于修复complex_description
        prompt_template = PROMPT_MAPPING.get(task_type)
        
        if not prompt_template or "创建一个Prompt" in prompt_template:
            print(f"警告: 任务类型 '{task_type}' 的Prompt未完全实现，跳过。")
            continue
            
        # 调用LLM API
        generated_data = call_mllm_api(client, base64_image, prompt_template)

        if generated_data:
            # 组合成完整的数据点
            full_data_point = {
                "id": f"m_faith_{int(time.time())}_{random.randint(1000, 9999)}",
                "task_type": task_type,
                "image": {
                    "image_id": random_image_id,
                    "image_path": image_path,
                    "source": "COCO2017-val"
                }
            }
            # 将模型生成的 instruction 和 reference_answer 添加进来
            full_data_point.update(generated_data)

            # 验证和修复数据
            repaired_data = validate_and_repair_data(full_data_point, image_path)
            
            if repaired_data:
                # 写入文件
                try:
                    with open(Config.OUTPUT_FILE, 'a', encoding='utf-8') as f:
                        f.write(json.dumps(repaired_data, ensure_ascii=False) + '\n')
                    print(f"成功: 样本 {repaired_data['id']} 已生成并写入到 {Config.OUTPUT_FILE}")
                    existing_ids.add(random_image_id)
                    generated_count += 1
                except IOError as e:
                    print(f"错误: 无法写入到文件 '{Config.OUTPUT_FILE}': {e}")
                    # 如果写入失败，可能需要中断循环
                    break
            else:
                print("错误: 数据修复失败，该样本将被丢弃。")
        else:
            print(f"错误: 从LLM获取数据失败，跳过此图片。")

    print(f"\n--- 数据生成完成 ---")
    print(f"总共生成了 {generated_count} 个新样本。")


if __name__ == '__main__':
    main()