/*
  Minimal and safe table styling for PrinceXML.
  This stylesheet ONLY targets tables to avoid conflicts with default styles.
*/

table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed; /* Prevents tables from becoming too wide */
    margin-top: 1em;
    margin-bottom: 1em;
}

th, td {
    border: 1px solid #ccc;
    padding: 5px;
    text-align: left;
    vertical-align: top;

    /* Key property to prevent content from overflowing the page */
    word-wrap: break-word;
}

/* Optional: Style for table header */
th {
    background-color: #f2f2f2;
    font-weight: bold;
} 