# M-FAITH 评估方案 vs. Beyond Facts 原文：核心改进点分析

本文档旨在清晰阐述我们的 MLLM 意图幻觉评估方案，相较于其理论来源《Beyond Facts: Evaluating Intent Hallucination in Large Language Models》论文，所做出的核心改进与创新。

## 1. 核心思想：从 "应用" 到 "扩展与深化"

如果说《Beyond Facts》原文是**"在一个新领域（意图幻觉）提出核心概念，并解决了它在纯文本世界（LLM）的评估问题"**，那么我们的工作就是**"将这个核心概念成功地迁移、扩展并应用到更复杂、更前沿的图文多模态世界（MLLM），并为之打造了一套全新的、更强大的评估工具"**。

我们的工作并非简单的复现或应用，而是一次**体系化的扩展和创新**，旨在为 MLLM 意图幻觉这个新兴研究方向建立起一套坚实的评估基础。

## 2. 改进点对比详情

为了更直观地展示差异，我们整理了以下对比表格：

| 对比维度 (Comparison Dimension) | 原文《Beyond Facts》 (Original Paper) | 我们的评估方案 (Our Evaluation Plan) | **核心改进点 (Key Improvement)** |
| :--- | :--- | :--- | :--- |
| **评估领域 (Domain)** | **纯文本 (LLM)**<br>评估语言模型对文本指令的遵循情况。 | **多模态 (MLLM)**<br>评估多模态模型对"图+文"指令的遵循情况。 | **根本性的领域扩展**：首次将意图幻觉的概念从文本域迁移到更复杂、更前沿的多模态领域。 |
| **评估基准 (Benchmark)** | `FAITHQA` 基准<br>- 事实问答<br>- 创意写作<br>- 文本RAG | `M-FAITH` 基准<br>- 复杂约束图像描述<br>- 多模态RAG问答<br>- 视觉意图遵循 | **任务的完全重塑**：我们设计的任务是原生多模态的，能有效诱导并测试 MLLM 特有的意图幻觉（如忽略图像内容、曲解图文关系）。 |
| **评估方法 (Methodology)** | `CONSTRAINT SCORE (CS)`<br>使用 **LLM 作为裁判**，判断**文本回答**是否满足**文本约束**。函数为 `S_phi(c, y)`。 | `Multimodal-CS (M-CS)`<br>使用 **MLLM 作为"多模态裁判"**，判断**文本回答**在结合**输入图像**后是否满足**文本约束**。函数为 `S_phi(c, y, I)`。 | **评估范式的创新**：我们引入了"多模态裁判"机制。裁判的判断不再是简单的文本比对，而是需要进行**图文联合理解**的复杂推理，这使得评估更加精准和深刻，也是我们方案**最核心的技术创新点**。 |
| **核心问题 (Core Question)** | "LLM 的回答 `y` 满足了指令 `q` 里的约束 `c` 吗？" | "MLLM 的回答 `y` 在**看到图片 `I` 的前提下**，满足了指令 `q` 里的约束 `c` 吗？" | **问题深度的演进**：我们的评估从"模型是否听话"进化为"模型是否**看懂了图再听话**"，更贴近 MLLM 的真实应用场景。 |

## 3. 总结

综上所述，我们的评估方案在继承《Beyond Facts》核心思想的同时，在研究领域、评测任务和评估方法上均做出了关键性的、专门针对多模态的创新，从而保证了我们研究的独特性和前沿性。
