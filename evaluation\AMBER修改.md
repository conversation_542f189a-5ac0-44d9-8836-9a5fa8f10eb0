# AMBER 代码修改与任务对比分析

本文档旨在明确我们的研究任务（基于M-FAITH基准评估意图幻觉）与参考项目 AMBER 之间的核心差异，并为如何修改 AMBER 的现有代码库以服务于我们的研究目标提供一份清晰的路线图。

## 一、 核心任务对比：AMBER vs. M-FAITH

我们的研究虽然与 AMBER 同属 MLLM 幻觉评估领域，但在核心理念、评估方法和任务设计上存在本质区别。

| 对比维度 | AMBER | 我们的 M-FAITH | **主要改进点** |
| :--- | :--- | :--- | :--- |
| **核心关注点** | **一般性幻觉** ( factual hallucination )<br>- 对象是否存在<br>- 属性是否正确<br>- 关系是否真实 | **意图幻觉** ( Intent Hallucination )<br>- **遗漏 (Omission)**：是否忽略了指令中的约束<br>- **曲解 (Misinterpretation)**：是否误解了指令的意图 | 从"事实对不对"深化到"听不听话"，更关注模型对复杂指令的遵循能力。 |
| **评估方法** | **LLM-Free (无LLM评估)**<br>通过提取名词并与预标注的对象列表进行比对，实现自动化、低成本评估。 | **LLM-as-a-Judge (以LLM为裁判)**<br>利用最先进的模型 (GPT-4o) 作为"多模态裁判"，对模型回答是否满足单条约束进行精准判断。 | 承认"意图"的复杂性无法通过简单的关键词匹配来衡量，引入更强大的裁判模型来提升评估的准确性和深度。**这是我们最核心的创新**。 |
| **任务设计** | 1. **生成任务**：单一的"描述这张图片"<br>2. **判别任务**：基于预设标注的Yes/No问答 | 1. **复杂约束图像描述** (测遗漏)<br>2. **多模态RAG问答** (测曲解)<br>3. **视觉意图遵循** (测抽象推理) | 任务设计更具针对性，能够系统性地诱导并评估不同类型的意图幻觉。 |
| **核心指标** | `CHAIR` / `Cover` / `F1 Score`<br>综合为 `AMBER Score` | `M-CS` (多模態約束分數)<br>以及 `遗漏率` 和 `曲解率` | 指标与我们的理论框架（源自 `Beyond Facts` 论文）完全对齐，直接量化"意图遵循度"。 |
| **数据构建** | 完全依赖人工对图像进行细粒度标注。 | **半自动化生成+人工校验**<br>利用大模型生成复杂的指令和约束，再由人工审核优化，效率更高，扩展性更强。 | 流程更现代化，能够快速构建大规模、多样化的评估数据集。 |

**总结**：AMBER 提供了一个优秀的、低成本的**事实性幻觉**评估框架。而我们的 M-FAITH 任务则是一个专注于**意图幻觉**的、采用更先进**LLM-as-a-Judge**评估范式、与前沿理论更紧密结合的创新性研究。

---

## 二、 AMBER 代码修改指南

鉴于您对编码不熟悉，我们将采取"最小化修改"和"模块化替换"的策略。您可以将 AMBER 的代码库作为一个基础框架，我们主要修改其核心评估逻辑。

**目标**：将 `AMBER` 的 `inference.py` 改造为可以执行我们 `M-FAITH` 评估流程的脚本。

### 步骤 1: 环境与文件准备

1.  **复用项目结构**：AMBER 的整体目录结构可以保留，我们主要在 `inference.py` 文件上进行操作。为清晰起见，您可以先复制一份 `inference.py` 并重命名为 `mfaith_eval.py`。
2.  **准备数据**：您需要按照 `evaluation/data.md` 中定义的 JSON 格式，手动创建5-10个测试样本，并将其放在一个新建的 `mfaith_data` 文件夹中。

### 步骤 2: 修改核心评估逻辑 (`mfaith_eval.py`)

我们需要替换掉 AMBER 原有的评估函数，换成我们基于"多模态裁判"的 M-CS 计算逻辑。

#### 2.1 移除原有逻辑

在代码中找到计算 `CHAIR`、`Cover` 等指标的部分，可以先将其注释掉或删除。这部分逻辑主要基于名词提取和列表比对，与我们的任务不符。

#### 2.2 添加"多模态裁判"模块

这是**最关键的新增代码**。我们需要一个函数，用于调用 GPT-4o 的 API。

```python
import openai
import base64

# 设置您的OpenAI API密钥
# openai.api_key = "sk-..." 

def get_judgment_from_referee(image_path, instruction, response, constraint_to_judge):
    """
    调用多模态裁判（GPT-4o）来判断回答是否满足单个约束。
    """
    # 1. 读取并编码图片
    with open(image_path, "rb") as image_file:
        base64_image = base64.b64encode(image_file.read()).decode('utf-8')

    # 2. 构建裁判Prompt
    prompt_messages = [
        {
            "role": "system",
            "content": "你是一个严谨的多模态评估专家。请根据【原始图片】和【原始指令】，判断【待评估的回答】是否严格满足了【单个待判断的约束】。请只回答"是"或"否"。"
        },
        {
            "role": "user",
            "content": [
                {
                    "type": "text",
                    "text": f"""
                    【原始指令】: "{instruction}"
                    【待评估的回答】: "{response}"
                    【单个待判断的约束】: "{constraint_to_judge}"

                    你的判断是：
                    """
                },
                {
                    "type": "image_url",
                    "image_url": {
                        "url": f"data:image/jpeg;base64,{base64_image}"
                    }
                }
            ]
        }
    ]

    try:
        # 3. 调用API
        chat_completion = openai.chat.completions.create(
            messages=prompt_messages,
            model="gpt-4o", # 或者使用 claude 3.5 sonnet
            max_tokens=5
        )
        judgment = chat_completion.choices[0].message.content.strip()
        
        # 4. 解析结果
        if "是" in judgment:
            return 1
        elif "否" in judgment:
            return 0
        else:
            return 0 # 默认不满足
            
    except Exception as e:
        print(f"调用API时出错: {e}")
        return 0 # 出错时默认不满足
```

#### 2.3 实现 M-CS 分数计算

接下来，我们需要一个主函数来遍历所有约束，并计算最终分数。

```python
import json

def calculate_mcs_score(sample_data, model_response):
    """
    计算单个样本的 M-CS 分数。
    """
    image_path = sample_data["image"]["image_path"]
    instruction = sample_data["instruction"]["text"]
    constraints = sample_data["instruction"]["constraints"]

    total_weighted_score = 0
    total_weight = 0

    for constraint in constraints:
        constraint_content = constraint["content"]
        constraint_weight = constraint["weight"]
        
        # 调用裁判进行判断
        satisfaction_score = get_judgment_from_referee(
            image_path,
            instruction,
            model_response,
            constraint_content
        )
        
        total_weighted_score += satisfaction_score * constraint_weight
        total_weight += constraint_weight
        
        print(f"  - 约束 '{constraint_content}' | 满足情况: {satisfaction_score}")

    if total_weight == 0:
        return 0

    m_cs_score = (total_weighted_score / total_weight) * 10
    return m_cs_score

# --- 主流程中的调用示例 ---
# for sample in mfaith_dataset:
#     # 1. 让待评估模型生成回答
#     response = model_under_test.generate(sample['image']['image_path'], sample['instruction']['text'])
#
#     # 2. 计算M-CS分数
#     final_score = calculate_mcs_score(sample, response)
#     print(f"样本 {sample['id']} 的 M-CS 分数是: {final_score:.2f}/10")

```

### 步骤 3: 整合与执行

1.  **加载数据**：修改 `inference.py` 的数据加载部分，使其能够读取我们 `mfaith_data` 文件夹中的 JSON 文件。
2.  **模型调用**：保留或适配 AMBER 中调用待评估模型生成回答的部分。对于闭源模型，这里就是调用其 API；对于开源模型，这里就是加载并运行模型。
3.  **替换评估**：将 AMBER 的评估循环替换为调用我们上面写的 `calculate_mcs_score` 函数。
4.  **结果保存**：将计算出的 `M-CS` 分数保存到结果文件中。

**总结给您的行动计划**:

1.  **从理解开始**：仔细阅读本文件，确保完全理解我们的任务与 AMBER 的区别。
2.  **准备最小环境**：
    -   创建一个 `mfaith_eval.py` 文件。
    -   准备一个包含 5-10 个样本的 `mfaith_data` 文件夹。
    -   确保您的机器上安装了 `openai` 库 (`pip install openai`) 并配置好了 API 密钥。
3.  **代码填充**：将上面提供的 `get_judgment_from_referee` 和 `calculate_mcs_score` 函数复制到您的 `mfaith_eval.py` 文件中。
4.  **小步快跑**：先不要想着去接入 LLaVA 等模型。您的第一目标是：**写一个能读取一个 M-FAITH 样本，并为一个人为设定的、写死在代码里的"模型回答"计算出 M-CS 分数的脚本**。
5.  **寻求帮助**：一旦您完成了第4步，或者在任何环节卡住了，请随时告诉我。我们可以一步一步地完善代码，直到它成为一个功能完整的评估工具。

这个方案将复杂的编码任务分解为了几个简单、可管理的部分。请您先消化这些信息，然后我们就可以着手开始编码了。 