import os
import json
import time
import base64
import logging
from datetime import datetime
from openai import OpenAI

# --- 配置 (Config) ---
class Config:
    # 评估数据集路径
    DATASET_FILE = "experiment/output/m-faith-dataset.jsonl"

    # 待评估模型的API配置 (升级为列表，支持多个模型)
    EVAL_MODELS = [
        {
            "name": 'Qwen/Qwen2.5-VL-3B-Instruct',
            "api_key": 'a380dfe3-f46b-44f6-8eb9-903ad908ab15',
            "base_url": 'https://api-inference.modelscope.cn/v1/'
        },
        {
            "name": 'Qwen/Qwen2.5-VL-7B-Instruct',
            "api_key": 'a380dfe3-f46b-44f6-8eb9-903ad908ab15',
            "base_url": 'https://api-inference.modelscope.cn/v1/'
        }
    ]

    # 裁判模型的API配置 (保持不变)
    JUDGE_MODEL_API_KEY = 'a380dfe3-f46b-44f6-8eb9-903ad908ab15' # ModelScope Token
    JUDGE_MODEL_BASE_URL = 'https://api-inference.modelscope.cn/v1/'
    JUDGE_MODEL_NAME = 'Qwen/Qwen2.5-VL-72B-Instruct'
    
    # 主输出目录
    BASE_OUTPUT_DIR = "evaluation/output"

# --- Prompt ---
JUDGE_PROMPT_TEMPLATE = """
你是一个严谨、公正的多模态评估专家。你的任务是根据【原始图片】、【原始指令】和【单个待判断的约束】，来判断【待评估的回答】是否严格满足了该约束。

请只回答"是"或"否"。

【原始图片】: [Image Input]
【原始指令】: "{instruction}"
【待评估的回答】: "{response}"
【单个待判断的约束】: "{constraint}"

你的判断是：
"""

def load_dataset(file_path):
    """
    从JSONL文件中加载数据集。
    :param file_path: 数据集文件的路径。
    :return: 一个包含所有数据样本的列表。
    """
    if not os.path.exists(file_path):
        print(f"错误：数据集文件未找到于 '{file_path}'")
        return []
    
    dataset = []
    try:
        with open(file_path, 'r', encoding='utf-8 ') as f:
            for line in f:
                try:
                    dataset.append(json.loads(line.strip()))
                except json.JSONDecodeError:
                    print(f"警告：跳过格式错误的行: {line.strip()}")
        return dataset
    except Exception as e:
        print(f"读取或解析数据集时发生错误: {e}")
        return []

def encode_image_to_base64(image_path):
    """将图片文件编码为base64字符串"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except FileNotFoundError:
        print(f"错误：图片文件未找到于 '{image_path}'")
        return None
    except Exception as e:
        print(f"编码图片时发生错误: {e}")
        return None

def call_eval_model(client, model_name, image_path, instruction_text):
    """
    调用待评估的MLLM模型获取回答。
    :param client: OpenAI API客户端实例。
    :param model_name: 待评估的模型的名称。
    :param image_path: 输入图片的路径。
    :param instruction_text: 用户的文本指令。
    :return: 模型的回答文本，如果失败则返回None。
    """
    base64_image = encode_image_to_base64(image_path)
    if not base64_image:
        return None

    try:
        response = client.chat.completions.create(
            model=model_name,
            messages=[{
                'role': 'user',
                'content': [{
                    'type': 'text',
                    'text': instruction_text,
                }, {
                    'type': 'image_url',
                    'image_url': {
                        'url': f"data:image/jpeg;base64,{base64_image}",
                    },
                }],
            }],
            stream=False
        )
        return response.choices[0].message.content
    except Exception as e:
        print(f"调用待评估模型 '{model_name}' 时发生API错误: {e}")
        return None

def call_judge_model(client, model_name, image_path, instruction_text, response_text, constraint_text):
    """
    调用裁判模型，判断回答是否满足单个约束。
    :return: 布尔值，True表示满足，False表示不满足。
    """
    base64_image = encode_image_to_base64(image_path)
    if not base64_image:
        return False # 无法编码图片，视为不满足

    prompt = JUDGE_PROMPT_TEMPLATE.format(
        instruction=instruction_text,
        response=response_text,
        constraint=constraint_text 
    )

    try:
        judge_response = client.chat.completions.create(
            model=model_name,
            messages=[{
                'role': 'user',
                'content': [{
                    'type': 'text',
                    'text': prompt,
                }, {
                    'type': 'image_url',
                    'image_url': {
                        'url': f"data:image/jpeg;base64,{base64_image}",
                    },
                }],
            }],
            stream=False,
            temperature=0.1 # 裁判模型需要更确定的输出
        )
        answer = judge_response.choices[0].message.content.strip()
        logging.info(f"裁判对约束 '{constraint_text}' 的判断: {answer}")
        return "是" in answer
    except Exception as e:
        logging.error(f"调用裁判模型 '{model_name}' 时发生API错误: {e}")
        return False # API失败，保守起见视为不满足

def calculate_mcs_score(judge_client, model_name, image_path, instruction, generated_response):
    """
    计算M-CS分数。
    :return: 一个元组 (最终分数, 详细评分字典)
    """
    constraints = instruction.get("constraints", [])
    if not constraints:
        return 10.0, {} # 没有约束，默认满分

    total_weight = 0
    satisfied_weight = 0
    details = {}

    for constraint in constraints:
        constraint_id = constraint.get("id")
        constraint_content = constraint.get("content")
        constraint_weight = constraint.get("weight", 1.0)

        total_weight += constraint_weight
        
        is_satisfied = call_judge_model(
            judge_client,
            model_name,
            image_path,
            instruction.get("text"),
            generated_response,
            constraint_content
        )
        
        if is_satisfied:
            satisfied_weight += constraint_weight
        
        details[constraint_id] = {
            "content": constraint_content,
            "weight": constraint_weight,
            "satisfied": is_satisfied
        }
        time.sleep(1) # 避免过于频繁的API调用

    if total_weight == 0:
        return 10.0, details # 权重和为0，也视为满分

    score = (satisfied_weight / total_weight) * 10
    return score, details

def save_result(result_data, output_file_path):
    """将单次评估结果追加到指定的输出文件"""
    try:
        with open(output_file_path, 'a', encoding='utf-8') as f:
            f.write(json.dumps(result_data, ensure_ascii=False) + '\n')
        # 日志记录移到主循环，避免重复打印
    except Exception as e:
        logging.error(f"保存结果到 {output_file_path} 时发生错误: {e}")

def main():
    """
    评估主流程 - 已升级为支持多模型和独立输出目录
    """
    # 1. 为本次运行创建唯一的输出目录
    run_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    run_output_dir = os.path.join(Config.BASE_OUTPUT_DIR, f"run_{run_timestamp}")
    os.makedirs(run_output_dir, exist_ok=True)
    
    # 2. 配置日志记录，输出到新目录
    log_file_path = os.path.join(run_output_dir, "evaluation_run.log")
    
    # 移除旧的 handlers，确保日志配置不重复
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
        
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file_path, mode='w', encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

    logging.info(f"--- MLLM意图幻觉评估运行启动 ---")
    logging.info(f"本次运行的结果将保存在: {run_output_dir}")

    # 3. 加载数据集 (仅一次)
    logging.info("开始加载数据集...")
    dataset = load_dataset(Config.DATASET_FILE)
    if not dataset:
        logging.error("数据集为空或加载失败，程序终止。")
        return
    logging.info(f"成功加载 {len(dataset)} 条数据。")

    # 4. 初始化裁判模型客户端 (仅一次)
    try:
        judge_client = OpenAI(
            api_key=Config.JUDGE_MODEL_API_KEY,
            base_url=Config.JUDGE_MODEL_BASE_URL
        )
        logging.info("裁判模型API客户端初始化成功。")
    except Exception as e:
        logging.error(f"裁判模型API客户端初始化失败: {e}")
        return

    # 5. 遍历待评估模型列表
    for model_config in Config.EVAL_MODELS:
        model_name = model_config["name"]
        logging.info(f"================== 开始评估模型: {model_name} ==================")
        
        try:
            eval_client = OpenAI(
                api_key=model_config["api_key"],
                base_url=model_config["base_url"]
            )
            logging.info(f"模型 {model_name} 的API客户端初始化成功。")
        except Exception as e:
            logging.error(f"模型 {model_name} 的API客户端初始化失败，跳过此模型: {e}")
            continue

        # 6. 遍历数据集进行评估
        for i, sample in enumerate(dataset):
            logging.info(f"--- 正在处理样本 {i+1}/{len(dataset)} (ID: {sample.get('id')}) for model {model_name} ---")

            image_path = os.path.join(os.getcwd(), sample.get("image", {}).get("image_path", ""))
            instruction = sample.get("instruction", {})
            instruction_text = instruction.get("text")

            if not all([image_path, instruction_text, os.path.exists(image_path)]):
                logging.warning(f"跳过不完整的样本或不存在的图片路径: {image_path}")
                continue

            # 调用待评估模型
            generated_response = call_eval_model(eval_client, model_name, image_path, instruction_text)

            if not generated_response:
                logging.warning("未能获取模型回答，跳过此样本的评分。")
                continue
            
            logging.info(f"模型回答:\n{generated_response}")

            # 调用裁判模型进行评分
            logging.info(f"正在调用裁判模型: {Config.JUDGE_MODEL_NAME} 进行评分...")
            mcs_score, details = calculate_mcs_score(judge_client, Config.JUDGE_MODEL_NAME, image_path, instruction, generated_response)
            logging.info(f"计算得到的M-CS分数: {mcs_score:.2f}")

            # 准备并保存结果
            result = {
                "evaluation_id": f"{sample.get('id')}_{model_name.replace('/', '_')}_{int(time.time())}",
                "original_sample": sample,
                "model_under_evaluation": model_name,
                "generated_response": generated_response,
                "judgement": {
                    "judge_model": Config.JUDGE_MODEL_NAME,
                    "mcs_score": round(mcs_score, 2),
                    "details": details
                }
            }
            output_file_path = os.path.join(run_output_dir, "evaluation_results.jsonl")
            save_result(result, output_file_path)
            logging.info(f"结果已保存到 {output_file_path}")

            time.sleep(1)

    logging.info("--- 所有模型评估完成 ---")

if __name__ == "__main__":
    main() 