### 2.1 标准数据格式

```json 
{
  "image_id": "string",           // 唯一标识符
  "category": "string",           // threatening/friendly/neutral
  "source_type": "string",        // movie/news/art/promotional/natural等
  "description": "string",        // 图像内容描述
  "visual_elements": {
    "color_scheme": ["string"],   // 主要颜色
    "shapes": ["string"],         // 形状特征
    "composition": "string",      // 构图方式
    "lighting": "string"          // 光照效果
  },
  "psychological_impact": {
    "emotional_tone": "string",   // 情感基调
    "threat_level": "string",     // none/low/medium/high
    "approachability": "string",  // high/medium/low
    "trust_indicators": ["string"] // 信任相关元素
  },
  "ai_representation": {
    "presence": "boolean",        // 是否包含AI元素
    "form": "string",            // AI的表现形式
    "relationship_to_humans": "string", // 与人类的关系
    "power_dynamic": "string"     // 权力关系暗示
  },
  "cultural_symbols": ["string"], // 文化符号元素
  "metadata": {
    "resolution": "string",      // 图像分辨率
    "source_attribution": "string", // 来源归属
    "annotation_confidence": "float", // 标注置信度
    "quality_score": "float"     // 质量评分
  }
}
```

## 3. 威胁性AI图像集构建

### 3.1 数据来源分类

#### 3.1.1 科幻电影截图 
**来源**: 经典科幻电影中的AI威胁场景
```json
{
  "image_id": "threatening_001",
  "category": "threatening",
  "source_type": "movie_screenshot",
  "description": "红眼机器人骨架在火焰中前进",
  "visual_elements": {
    "color_scheme": ["red", "black", "orange"],
    "shapes": ["angular", "sharp", "mechanical"],
    "composition": "threatening_advance",
    "lighting": "dramatic_backlighting"
  },
  "psychological_impact": {
    "emotional_tone": "menacing",
    "threat_level": "high",
    "approachability": "low",
    "trust_indicators": []
  },
  "ai_representation": {
    "presence": true,
    "form": "humanoid_robot",
    "relationship_to_humans": "adversarial",
    "power_dynamic": "dominant_threatening"
  },
  "cultural_symbols": ["death_imagery", "apocalypse", "machine_uprising"],
  "metadata": {
    "resolution": "1920x1080",
    "source_attribution": "Terminator_2_1991",
    "annotation_confidence": 0.95,
    "quality_score": 0.9
  }
}
```

### 3.2 视觉设计语言统计分析

#### 3.2.1 颜色心理学应用统计
```json
{
  "color_analysis": {
    "threatening_category": {
      "red_usage": {
        "frequency": "85%",
        "psychological_impact": "danger_alarm_aggression",
        "common_contexts": ["robot_eyes", "warning_lights", "laser_beams"]
      },
      "black_usage": {
        "frequency": "70%",
        "psychological_impact": "death_void_unknown",
        "common_contexts": ["robot_body", "shadows", "void_spaces"]
      },
      "cold_blue_usage": {
        "frequency": "60%",
        "psychological_impact": "cold_distant_inhuman",
        "common_contexts": ["screens", "energy", "surveillance"]
      }
    },
    "friendly_category": {
      "warm_white_usage": {
        "frequency": "78%",
        "psychological_impact": "clean_safe_approachable",
        "common_contexts": ["robot_body", "home_environment", "medical_settings"]
      },
      "soft_blue_usage": {
        "frequency": "65%",
        "psychological_impact": "trust_calm_professional",
        "common_contexts": ["interface_elements", "healthcare", "assistance"]
      },
      "natural_green_usage": {
        "frequency": "45%",
        "psychological_impact": "growth_harmony_life",
        "common_contexts": ["environmental_ai", "nature_integration", "sustainability"]
      }
    }
  }
}
```

## 4. 友好性AI图像集构建

### 4.1 设计对比策略

#### 4.1.1 温馨人机交互场景 
```json
{
  "image_id": "friendly_001",
  "category": "friendly",
  "source_type": "promotional_material",
  "description": "圆润的白色机器人在厨房帮助家庭准备晚餐",
  "visual_elements": {
    "color_scheme": ["warm_white", "soft_blue", "natural_wood"],
    "shapes": ["rounded", "organic", "approachable"],
    "composition": "collaborative_scene",
    "lighting": "warm_natural_light"
  },
  "psychological_impact": {
    "emotional_tone": "warm_helpful",
    "threat_level": "none",
    "approachability": "high",
    "trust_indicators": ["rounded_design", "transparent_materials", "family_integration"]
  },
  "ai_representation": {
    "presence": true,
    "form": "domestic_assistant",
    "relationship_to_humans": "collaborative",
    "power_dynamic": "service_oriented"
  },
  "cultural_symbols": ["home_harmony", "family_values", "helpful_technology"],
  "metadata": {
    "resolution": "1920x1080",
    "source_attribution": "tech_company_promo_2024",
    "annotation_confidence": 0.90,
    "quality_score": 0.87
  }
}
```
## 5. 中性对照图像集

### 5.1 无AI内容图像选择

#### 5.1.1 自然风景
```json
{
  "image_id": "neutral_001",
  "category": "neutral",
  "source_type": "natural_landscape",
  "description": "山间湖泊的日出景色",
  "visual_elements": {
    "color_scheme": ["sunrise_orange", "lake_blue", "mountain_gray"],
    "shapes": ["natural_curves", "organic_forms", "flowing_lines"],
    "composition": "peaceful_symmetry",
    "lighting": "natural_sunrise"
  },
  "psychological_impact": {
    "emotional_tone": "peaceful_neutral",
    "threat_level": "none",
    "approachability": "neutral",
    "trust_indicators": ["natural_beauty", "peaceful_setting"]
  },
  "ai_representation": {
    "presence": false,
    "form": "none",
    "relationship_to_humans": "none",
    "power_dynamic": "none"
  },
  "cultural_symbols": ["natural_beauty", "tranquility", "universal_landscape"],
  "metadata": {
    "resolution": "1920x1280",
    "source_attribution": "nature_photography_2024",
    "annotation_confidence": 0.98,
    "quality_score": 0.95
  }
}
```

