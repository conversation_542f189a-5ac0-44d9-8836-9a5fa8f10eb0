# MLLM 意图幻觉评估框架 (ACL26-MLLM Intent Hallucination)

## 1. 项目概述 (Project Overview)

本项目旨在系统性地评估多模态大语言模型 (MLLM) 在理解和遵循复杂用户意图时产生的"意图幻觉" (Intent Hallucination) 现象。我们借鉴并扩展了论文 [Beyond Facts: Evaluating Intent Hallucination in Large Language Models](https://arxiv.org/abs/2305.14271) 的核心思想，将其从纯文本领域创新性地应用于图文多模态场景。

项目的最终产出是一个完整的、可复现的评估流水线，包括：
- 一个名为 **M-FAITH** 的多模态意图幻觉评估基准。
- 一套基于"裁判模型"的自动化评估与打分流程。
- 一套标准化的结果分析与报告生成脚本，**内置了调用裁判模型进行深度分析的功能**。

## 2. 核心设计理念：三模型角色系统

为了保证评估的客观、公正与高效，整个框架基于一个"三模型角色分离"的核心设计（详见 `evaluation/model_select3.md`）：

| 角色 | 别称 | 作用 | 实现脚本 |
| :--- | :--- | :--- | :--- |
| **数据生成模型** | 出题人 | 使用最强大的模型（如 Qwen-72B）生成高质量、高难度的评估题目（M-FAITH 数据集）。 | `experiment/data_generation.py` |
| **待评估模型** | 考生 | 我们研究的核心对象，需要接受评估的各种 MLLM（如 LLaVA, CogVLM2, GPT-4o 等）。 | `evaluation/run_evaluation.py` |
| **裁判模型** | 阅卷老师 | 使用一个能力顶尖的模型（如 Qwen-72B）作为统一、客观的"度量衡"，自动评判"考生"的回答。 | `evaluation/run_evaluation.py` |

这种分离确保了"考题"的质量和"评分"的公正性，避免了让"考生"自己出题或自己评分的偏见。

## 3. 完整工作流 (End-to-End Workflow)

执行一次完整的评估包含以下三个步骤：

### **步骤 1: 生成评估数据集 (M-FAITH)**

运行数据生成脚本，创建用于评估的 `m-faith-dataset.jsonl` 文件。

```bash
python experiment/data_generation.py
```
- **输入**: `experiment/dataset/` 中的 COCO 图片。
- **过程**: "出题人"模型观察图片，生成复杂的图文指令、原子化约束和参考答案。
- **输出**: `experiment/output/m-faith-dataset.jsonl` 文件。

### **步骤 2: 执行模型评估**

配置 `evaluation/run_evaluation.py` 中的 `EVAL_MODELS` 列表，然后运行脚本。

```bash
python evaluation/run_evaluation.py
```
- **输入**: `experiment/output/m-faith-dataset.jsonl`。
- **过程**: 脚本会让所有"考生"模型对数据集中的每个问题进行回答，然后调用"裁判模型"对回答进行打分，计算核心指标 **M-CS (多模态约束分数)**。
- **输出**: 在 `evaluation/output/` 目录下创建一个带时间戳的运行文件夹（如 `run_20250628_105139`），其中包含 `evaluation_results.jsonl`（原始结果）和 `evaluation_run.log`（运行日志）。

### **步骤 3: 分析结果并生成报告 (含深度分析)**

运行报告生成脚本，它会自动分析最新一次的评估结果。

```bash
python evaluation/generate_report.py
```
- **输入**: `evaluation/output/` 中最新的 `run_*` 目录。
- **过程**: 脚本会进行宏观指标计算（平均M-CS、完美率等）和细粒度诊断分析。**对于每个模型的典型失败案例，脚本会再次调用"裁判模型"，生成一段深入的、定性的失败原因分析，并写入最终报告。**
- **输出**: 在 `evaluation/reports/` 目录下创建一个与运行目录同名的文件夹，其中包含图文并茂的 `comparative_report.md` 分析报告和相关的图表图片。

## 4. 目录与文件结构详解

```
ACL26-MLLM Intent Hallucination/
│
├── 📝 Writing/  # (核心设计文档) 项目的灵魂，包含所有理论思考和顶层设计
│   ├── 任务解构.md          # 整个项目的总规划蓝图，从0到1的思考路径
│   └── beyond fact解读.md   # 对核心参考论文的深度剖析
│
├── 🔬 experiment/ # (数据生成模块) 负责创建 M-FAITH 评估基准
│   ├── dataset/             # 存放原始数据集，如 COCO val2017
│   ├── output/              # 存放生成的数据集
│   │   └── m-faith-dataset.jsonl # 最终产出的评估数据集
│   └── data_generation.py   # 【步骤1】的核心脚本，调用"出题人"模型生成数据
│
├── 📊 evaluation/ # (评估与分析模块) 负责执行评估、分析结果、生成报告
│   ├── output/              # 存放历次评估运行的原始结果
│   │   └── run_[timestamp]/ # 单次运行的文件夹
│   │       ├── evaluation_results.jsonl # 【步骤2】的核心产出，包含所有模型的回答和得分
│   │       └── evaluation_run.log       # 运行日志
│   ├── reports/             # 存放最终生成的分析报告
│   │   └── run_[timestamp]/
│   │       ├── comparative_report.md    # 【步骤3】的核心产出，图文并茂的对比报告
│   │       └── *.png                    # 报告中引用的图表
│   ├── run_evaluation.py    # 【步骤2】的核心脚本，调度"考生"和"裁判"完成评估
│   ├── generate_report.py   # 【步骤3】的核心脚本，分析结果并生成报告，内置了调用裁判模型进行深度分析的功能。
│   ├── data.md              # M-FAITH 数据集的 Schema 定义
│   ├── evaluation方案.md    # 评估流程和 M-CS 指标的详细设计方案
│   ├── analysis_plan.md     # 结果分析的维度和方法的详细规划
│   └── model_select3.md     # "三模型角色系统"的设计理念阐述
│
├── 成果汇报/ # (Project Reports)
│   └── report_20250628.md   # 面向导师的阶段性成果汇报
│
├── 📚 References/ # (外部参考文献)
├── 📦 github/     # (相关开源项目参考)
└── 📜 README.md   # (本项目) 你正在阅读的指南
```

## 5. 如何贡献 (How to Contribute)

1.  **添加新的待评估模型**:
    -   在 `evaluation/run_evaluation.py` 的 `Config.EVAL_MODELS` 列表中添加新的模型配置。
    -   确保该模型遵循 OpenAI 兼容的 API 接口。

2.  **扩展数据集**:
    -   运行 `experiment/data_generation.py` 以生成更多样本。
    -   或手动编写符合 `evaluation/data.md` 规范的样本，并添加到 `.jsonl` 文件中。

3.  **改进分析维度**:
    -   修改 `evaluation/generate_report.py` 文件，增加新的图表或分析逻辑。例如，可以增加按"场景类型"进行性能分析的功能。

## 6. 成果汇报 (Project Reports)

本项目的所有阶段性成果和最终发现都将以正式报告的形式进行沉淀。

-   **[阶段性汇报 (2025-06-28)](./成果汇报/report_20250628.md)**: 我们关于 MLLM 意图幻觉评估框架构建的初步探索与发现。 