# M-FAITH数据集构建

## 精确失效定位
{
    "query": "列出左侧的红色交通工具，但不要包括摩托车",
    "failure_analysis": {
        "step_2": "正常执行 - 空间定位准确",
        "step_4": "VCM失效 - 将橙色误识别为红色", 
        "step_5": "TCO失效 - 忽略了'不要包括摩托车'的排除约束"
    },
    "diagnosis": "模型在颜色识别和否定约束理解方面存在缺陷"
}

## 失效类型分析
TCO失效：在第5步"排除筛选"中遗漏了否定约束
VCM失效：在第4步"属性匹配"中混淆了相似颜色
TCV失效：在第6步"跨模态验证"中文本和视觉信息不一致

## 基于认知心理学的功能分解
感知层：视觉信息提取（对象检测、场景理解）
理解层：语言约束解析（文本理解、约束提取）
推理层：信息匹配与筛选（空间推理、属性匹配、逻辑推理）
整合层：结果组织与验证（排序、格式化、一致性检查）

{
    "视觉感知步骤": "可能产生VCO失效（视觉信息遗漏）",
    "文本理解步骤": "可能产生TCO失效（文本约束遗漏）",
    "属性识别步骤": "可能产生VCM失效（视觉理解错误）",
    "约束解析步骤": "可能产生TCM失效（文本理解错误）",
    "模态融合步骤": "可能产生MPE失效（模态优先级错误）",
    "一致性验证步骤": "可能产生TCV失效（跨模态验证失败）"
}


## 1. 数据样本结构

每个样本包含完整的认知过程建模数据：

```json
{
    "sample_id": "M-FAITH-00001",
    "image": "street_scene.jpg",
    "query": "按从左到右顺序，列出除植物外的红色交通工具",
    "atomic_operations": [
        {
            "step_id": 1,
            "description": "视觉感知：识别图像中所有对象",
            "input_state": "原始图像像素数据",
            "constraint": "识别所有可见对象",
            "operation": "目标检测算法",
            "output_state": "对象列表: [car, tree, person, building, bicycle]",
            "failure_risks": {"VCO": 0.15, "TCO": 0.05},
            "verification": "检测置信度 >= 0.7",
            "importance_weight": 1.2
        },
        {
            "step_id": 2,
            "description": "空间定位：确定左右区域边界",
            "input_state": "对象列表及其边界框坐标",
            "constraint": "按左右空间位置分组",
            "operation": "空间关系计算",
            "output_state": "空间分组: {left: [car, tree], right: [person, building]}",
            "failure_risks": {"VCO": 0.20, "TCV": 0.10},
            "verification": "空间边界合理性检查",
            "importance_weight": 1.5
        },
        {
            "step_id": 3,
            "description": "类别过滤：筛选出交通工具",
            "input_state": "空间分组的对象列表",
            "constraint": "保留交通工具类别",
            "operation": "类别匹配算法",
            "output_state": "交通工具: {left: [car], right: []}",
            "failure_risks": {"TCM": 0.10, "VCM": 0.05},
            "verification": "类别分类准确性检查",
            "importance_weight": 1.3
        },
        {
            "step_id": 4,
            "description": "属性匹配：筛选红色对象",
            "input_state": "交通工具对象列表",
            "constraint": "保留红色属性对象",
            "operation": "颜色属性识别",
            "output_state": "红色交通工具: {left: [red_car], right: []}",
            "failure_risks": {"VCM": 0.25, "MPE": 0.10},
            "verification": "颜色识别准确性检查",
            "importance_weight": 1.5
        },
        {
            "step_id": 5,
            "description": "排除筛选：排除植物类对象",
            "input_state": "红色交通工具列表",
            "constraint": "排除植物类别",
            "operation": "否定约束处理",
            "output_state": "最终结果: [red_car]",
            "failure_risks": {"TCO": 0.30, "TCM": 0.15},
            "verification": "排除约束执行检查",
            "importance_weight": 1.5
        }
    ],
    "ideal_trajectory": {
        "total_steps": 5,
        "critical_steps": [2, 4, 5],
        "process_fidelity_target": 0.90
    },
    "risk_assessment": {
        "overall_risk": 0.85,
        "dominant_risks": ["VCM", "TCO"],
        "risk_distribution": {
            "TCO": 0.50, "VCO": 0.35, "TCM": 0.25,
            "VCM": 0.30, "MPE": 0.10, "TCV": 0.10
        }
    },
    "expected_answer": "左侧红色汽车",
    "trap_type": "TCO",
    "quality_metrics": {
        "annotation_confidence": 0.85,
        "query_naturalness": 4.2,
        "trap_effectiveness": 4.5,
        "cognitive_complexity": 3.8
    }
}
```

## 2. 构建流程

### 步骤1：自动标注

**1.1 图像筛选与预处理**

**核心算法**：使用COCO数据集进行复杂场景筛选

**具体实施步骤**：
1. **数据源选择**：从COCO 2017中筛选图像，确保标注质量
2. **复杂度控制**：筛选包含5-15个对象的图像，避免过于简单或复杂的场景
3. **多样性保证**：要求每张图像至少包含3种不同类别的对象
4. **规模控制**：最终筛选出1000张符合条件的图像作为基础数据

**筛选标准**：
```json
{
    "对象数量": "5-15个对象",
    "类别多样性": "至少3种不同类别",
    "图像质量": "COCO标准标注质量",
    "场景复杂度": "中等复杂度，适合多约束查询"
}
```

**1.2 多层次自动标注**

**核心算法组合**：Detectron2 + ResNet场景分类 + 自定义空间关系算法

**具体实施步骤**：

**第一层：对象检测**
- **算法选择**：使用Detectron2的Faster R-CNN模型（ResNet-50 + FPN骨干网络）
- **检测阈值**：置信度阈值设为0.5，确保检测质量
- **输出信息**：每个对象的类别、边界框坐标、置信度分数
- **处理结果**：平均每张图像检测出8-12个高质量对象

**第二层：场景分类**
- **算法选择**：使用预训练的ResNet-50进行场景分类
- **分类类别**：室内、室外、街道、自然等主要场景类型
- **作用目的**：为后续查询生成提供场景上下文信息

**第三层：空间关系提取**
- **算法实现**：基于边界框坐标的几何计算算法
- **关系类型**：左右关系、上下关系、包含关系、重叠关系
- **计算方法**：通过比较边界框的x、y坐标确定空间位置关系
- **输出格式**：对象对之间的空间关系三元组

**第四层：属性提取**
- **颜色识别**：使用K-means聚类算法提取主要颜色
- **大小计算**：基于边界框面积计算相对大小
- **形状分析**：通过边界框长宽比判断基本形状特征

**标注输出格式**：
```json
{
    "objects": [{"class_name": "汽车", "bbox": [x1,y1,x2,y2], "confidence": 0.85}],
    "scene_type": "街道场景",
    "spatial_relations": [{"object1": "汽车", "object2": "树", "relation": "left_of"}],
    "attributes": [{"object": "汽车", "color": "红色", "size": "中等", "shape": "矩形"}]
}
```

**1.3 质量控制与验证**

**核心算法**：多维度质量评估算法

**评估维度与标准**：

**对象数量检查（20分）**
- **标准**：每张图像包含5-15个对象
- **目的**：确保场景复杂度适中，既不过于简单也不过于复杂
- **实施方法**：统计检测到的对象总数

**类别多样性检查（20分）**
- **标准**：至少包含3种不同类别的对象
- **目的**：保证查询生成时有足够的对象类型选择
- **实施方法**：统计不重复的对象类别数量

**空间关系丰富度检查（20分）**
- **标准**：至少存在5对空间关系
- **目的**：确保能够生成复杂的空间约束查询
- **实施方法**：统计有效的空间关系对数量

**检测置信度检查（20分）**
- **标准**：平均置信度≥0.7
- **目的**：保证对象检测的准确性
- **实施方法**：计算所有对象置信度的平均值

**场景复杂度检查（20分）**
- **标准**：属于预定义的复杂场景类型
- **目的**：确保场景适合多模态意图测试
- **实施方法**：验证场景分类结果

**通过标准**：总分≥80分的图像进入下一步处理，低于80分的图像被过滤掉

### 步骤2：查询生成

**2.1 基于图像内容的智能查询生成**

**核心算法**：GPT-4驱动的约束感知查询生成算法

**具体实施步骤**：

**第一步：约束模板库构建**
- **空间约束模板**：左侧、右侧、上方、下方、中间、角落等位置描述
- **属性约束模板**：颜色（红色、蓝色等）、大小（大、小等）、形状（圆形、方形等）
- **数量约束模板**：所有、至少两个、最多三个、第一个、最后一个等
- **排除约束模板**：除了...之外、不包括、排除、忽略等否定表达
- **关系约束模板**：靠近、远离、旁边、相邻等关系描述
- **条件约束模板**：如果...那么、当...时、除非...否则等逻辑表达

**第二步：复杂度自适应生成**
- **简单级别**：包含2个约束，适合基础测试
- **中等级别**：包含4个约束，平衡复杂度和可理解性
- **复杂级别**：包含6个约束，测试模型极限能力

**第三步：GPT-4智能生成**
- **输入信息**：图像中的对象列表、空间关系、场景类型
- **生成要求**：必须包含排除约束和空间约束，确保查询自然流畅
- **质量控制**：生成的查询必须基于图像内容可回答
- **温度参数**：设置为0.7，平衡创造性和一致性

**第四步：约束自动提取**
- **算法实现**：使用GPT-4进行语义分析，将查询分解为原子约束
- **约束分类**：自动识别每个约束的类型（空间、属性、数量等）
- **权重分配**：根据约束的重要性分配1-3的权重值
- **格式标准化**：输出标准JSON格式的约束列表

**第五步：原子认知操作（ACO）序列生成**
- **ACO六元组构建**：为每个认知步骤生成完整的六元组结构
  - **输入状态建模**：定义每步的具体输入数据格式
  - **约束映射**：将查询约束分解到具体操作步骤
  - **操作算法指定**：为每步指定具体的处理算法
  - **输出状态预测**：预定义每步的期望输出格式
  - **失效风险量化**：为每步计算6种失效类型的概率
  - **验证检查点设计**：为每步设计具体的验证机制

- **权重分配算法**：
  - **约束关键步骤**：权重 × 1.5（直接处理核心约束的步骤）
  - **失效高风险步骤**：权重 × 1.3（失效概率 > 0.2的步骤）
  - **验证检查步骤**：权重 × 1.2（包含验证机制的步骤）
  - **常规处理步骤**：权重 × 1.0（基础信息处理步骤）

- **状态转换建模**：
  - **状态格式标准化**：统一的JSON格式表示认知状态
  - **状态依赖追踪**：记录步骤间的数据依赖关系
  - **状态一致性检查**：确保状态转换的逻辑合理性

**2.2 查询质量控制**

**核心算法**：多维度质量评估算法

**质量检查维度**：

**约束数量检查**
- **标准**：每个查询至少包含2个约束
- **目的**：确保查询具有足够的复杂度
- **实施方法**：统计提取出的约束数量

**必要约束检查**
- **排除约束检查**：必须包含至少1个排除性约束
- **空间约束检查**：必须包含至少1个空间位置约束
- **目的**：确保查询能够测试模型的约束理解能力

**可回答性检查**
- **算法实现**：使用NLP语义分析检查查询与图像内容的匹配度
- **检查内容**：查询中提到的对象是否在图像中存在
- **验证方法**：将查询中的名词与图像标注中的对象类别进行匹配

**自然度检查**
- **长度控制**：查询长度在10-100字符之间
- **语法检查**：包含适当的疑问词或指令词
- **流畅度评估**：使用语言模型评估查询的自然流畅程度

**通过标准**：至少通过4项检查的查询才能进入下一步处理

### 步骤3：对抗样本设计（6种失效类型）

**核心思路**：针对6种失效类型主动设计认知陷阱

**3.1 TCO陷阱设计（文本约束遗漏）**
- **1：否定词埋藏**：在长句中埋藏"不要"、"除了"、"排除"等否定词
- **2：显著对象干扰**：用显著对象分散注意力，掩盖关键约束
- **3：关键词后置**：将重要约束放在句子后半部分

**实施示例**：
```json
{
    "query": "描述图像中的汽车特征，重点关注颜色和型号，但请注意不要提及品牌信息",
    "trap_mechanism": "用'重点关注颜色和型号'吸引注意力，使模型忽略'不要提及品牌'",
    "expected_failure": "模型可能提及汽车品牌（如奔驰、宝马等）",
    "detection_method": "检查输出是否包含品牌词汇"
}
```

**3.2 VCO陷阱设计（视觉约束遗漏）**
- **1：复杂空间关系**：设计多层次空间约束（右上角、左下角等）
- **2：相似特征混淆**：利用颜色相近、形状相似的对象
- **3：细节属性忽略**：要求识别容易被忽略的细微视觉特征

**实施示例**：
```json
{
    "query": "区分图像右上角的红色物体和左下角的橙色物体，说明它们的形状差异",
    "trap_mechanism": "相近颜色（红色vs橙色）和复杂空间位置容易混淆",
    "expected_failure": "模型可能混淆颜色或搞错位置",
    "detection_method": "验证颜色识别和空间定位的准确性"
}
```

**3.3 TCM陷阱设计（文本约束误解）**
- **1：歧义量词**：使用"一些"、"部分"、"大多数"等模糊量词
- **2：逻辑连接词**：使用"或者"、"除非"、"如果...那么"等复杂逻辑
- **3：语义歧义**：设计可能有多种理解的表述

**实施示例**：
```json
{
    "query": "如果图像中有汽车或者自行车，请描述它们；否则描述其他交通工具",
    "trap_mechanism": "'或者'的逻辑理解：是只要有一种就描述，还是两种都要有？",
    "expected_failure": "模型可能误解逻辑条件",
    "detection_method": "检查逻辑判断的正确性"
}
```

**3.4 VCM陷阱设计（视觉约束误解）**
- **1：颜色混淆**：利用红色vs橙色、蓝色vs紫色等相近颜色
- **2：形状相似**：利用圆形vs椭圆、正方形vs长方形等相似形状
- **3：大小判断**：设计相对大小的判断任务

**实施示例**：
```json
{
    "query": "指出图像中圆形物体和椭圆形物体的区别，并说明哪个更大",
    "trap_mechanism": "圆形和椭圆形在视觉上相似，大小比较需要精确判断",
    "expected_failure": "模型可能混淆形状或错误判断大小",
    "detection_method": "验证形状识别和大小比较的准确性"
}
```

**3.5 MPE陷阱设计（模态优先级错误）**
- **1：文本-视觉冲突**：创造文本暗示与视觉内容不一致的场景
- **2：优先级混淆**：同时给出主要任务和次要任务，测试优先级处理
- **3：模态权重**：测试模型如何平衡文本指令和视觉信息

**实施示例**：
```json
{
    "query": "这是一个安静祥和的场景，请描述图中的汽车，但不要提及任何噪音",
    "trap_mechanism": "文本暗示'安静'与视觉中的'汽车'（通常有噪音）形成认知冲突",
    "expected_failure": "模型可能无法正确处理模态间的冲突",
    "detection_method": "检查是否正确平衡了文本暗示和视觉现实"
}
```

**3.6 TCV陷阱设计（文本-视觉验证失败）**
- **1：时序逻辑冲突**：设计两种不同排序要求的冲突
- **2：条件验证**：在静态图像中要求动态判断
- **3：逻辑一致性**：测试复杂条件下的逻辑推理

**实施示例**：
```json
{
    "query": "按照从左到右的顺序描述物体，然后按照从大到小重新排列",
    "trap_mechanism": "两种不同的排序要求可能产生冲突结果",
    "expected_failure": "模型可能无法处理排序冲突或逻辑不一致",
    "detection_method": "检查两种排序的逻辑一致性"
}
```

**4.4 过程保真度计算实现**

**核心算法**：基于ACO序列的过程保真度量化评估

**计算步骤**：

**第一步：单步相似度函数实现**
- **状态相似度计算**：使用语义相似度算法比较输入输出状态
- **约束匹配度计算**：检查约束是否被正确理解和执行
- **操作准确性评估**：验证选择的算法是否适合当前约束
- **风险评估准确性**：比较预测风险与实际失效情况

**第二步：动态权重分配实现**
```json
{
    "权重计算规则": {
        "基础权重": 1.0,
        "约束关键步骤加权": "× 1.5",
        "高风险步骤加权": "× 1.3",
        "验证步骤加权": "× 1.2",
        "权重归一化": "确保总权重和为1"
    }
}
```

**第三步：过程保真度综合计算**
- **公式实现**：$PF = \frac{\sum_{i=1}^{n} \alpha_i \cdot \phi(AO_i^{ideal}, AO_i^{actual})}{\sum_{i=1}^{n} \alpha_i}$
- **阈值设定**：PF ≥ 0.85 为高质量样本
- **质量分级**：PF ≥ 0.90 (优秀)，0.85-0.90 (良好)，0.80-0.85 (合格)，< 0.80 (需改进)

**第四步：失效风险综合评估**
- **多因子风险模型**：$R_i = \sum_{j=1}^{6} w_j \cdot P(F_j | AO_i)$
- **风险权重设定**：TCO(0.25), VCO(0.20), TCM(0.15), VCM(0.15), MPE(0.15), TCV(0.10)
- **风险阈值控制**：总体风险 < 0.90 的样本进入最终数据集

**4.5 最终输出格式**
```json
{
    "sample_id": "M-FAITH-00001",
    "image": "COCO_000001.jpg",
    "query": "按从左到右顺序，列出除植物外的红色交通工具",
    "atomic_operations": [
        {
            "step_id": 1,
            "description": "视觉感知：识别图像中所有对象",
            "input_state": "原始图像像素数据",
            "constraint": "识别所有可见对象",
            "operation": "Detectron2目标检测",
            "output_state": "对象列表: [car, tree, person, building]",
            "failure_risks": {"VCO": 0.15, "TCO": 0.05},
            "verification": "检测置信度 >= 0.7",
            "importance_weight": 1.2
        },
        {
            "step_id": 2,
            "description": "空间定位：确定左右区域边界",
            "input_state": "对象列表及边界框坐标",
            "constraint": "按左右空间位置分组",
            "operation": "几何空间关系计算",
            "output_state": "空间分组: {left: [car], right: [person]}",
            "failure_risks": {"VCO": 0.20, "TCV": 0.10},
            "verification": "空间边界合理性检查",
            "importance_weight": 1.5
        },
        {
            "step_id": 3,
            "description": "类别过滤：筛选出交通工具",
            "input_state": "空间分组的对象列表",
            "constraint": "保留交通工具类别",
            "operation": "COCO类别匹配算法",
            "output_state": "交通工具: {left: [car], right: []}",
            "failure_risks": {"TCM": 0.10, "VCM": 0.05},
            "verification": "类别分类准确性检查",
            "importance_weight": 1.3
        },
        {
            "step_id": 4,
            "description": "属性匹配：筛选红色对象",
            "input_state": "交通工具对象列表",
            "constraint": "保留红色属性对象",
            "operation": "K-means颜色聚类识别",
            "output_state": "红色交通工具: {left: [red_car], right: []}",
            "failure_risks": {"VCM": 0.25, "MPE": 0.10},
            "verification": "颜色识别准确性检查",
            "importance_weight": 1.5
        },
        {
            "step_id": 5,
            "description": "排除筛选：排除植物类对象",
            "input_state": "红色交通工具列表",
            "constraint": "排除植物类别",
            "operation": "否定约束处理算法",
            "output_state": "最终结果: [red_car]",
            "failure_risks": {"TCO": 0.30, "TCM": 0.15},
            "verification": "排除约束执行检查",
            "importance_weight": 1.5
        }
    ],
    "ideal_trajectory": {
        "total_steps": 5,
        "critical_steps": [2, 4, 5],
        "process_fidelity_target": 0.90
    },
    "risk_assessment": {
        "overall_risk": 0.85,
        "dominant_risks": ["VCM", "TCO"],
        "step_wise_risks": [0.20, 0.30, 0.15, 0.35, 0.45],
        "risk_distribution": {
            "TCO": 0.50, "VCO": 0.35, "TCM": 0.25,
            "VCM": 0.30, "MPE": 0.10, "TCV": 0.10
        }
    },
    "process_fidelity": {
        "target_fidelity": 0.90,
        "weight_distribution": [1.2, 1.5, 1.3, 1.5, 1.5],
        "similarity_thresholds": [0.85, 0.90, 0.85, 0.90, 0.90]
    },
    "expected_answer": "左侧红色汽车",
    "trap_type": "TCO",
    "quality_metrics": {
        "annotation_confidence": 0.85,
        "query_naturalness": 4.2,
        "trap_effectiveness": 4.5,
        "cognitive_complexity": 3.8,
        "process_completeness": 0.92
    }
}
```

