{"id": "m_faith_1751021086_6464", "task_type": "complex_description", "image": {"image_id": 179285, "image_path": "experiment/dataset/val2017/000000179285.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的网球拍和球，但不要提及任何品牌名称。首先描述网球拍的颜色和形状，然后描述球的位置和颜色，最后描述球上的文字，但请不要使用任何数字。", "constraints": [{"id": "c1", "type": "count", "content": "用三句话描述", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "描述网球拍的颜色和形状", "weight": 0.8}, {"id": "c3", "type": "attribute", "content": "描述球的位置和颜色", "weight": 0.8}, {"id": "c4", "type": "exclusion", "content": "不要提及任何品牌名称", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "不要使用任何数字", "weight": 1.0}]}, "reference_answer": "这是一把蓝色边框的网球拍，形状为圆形。球位于拍面中央，呈现出鲜艳的绿色。球上有一些字母标识。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021093_9282", "task_type": "complex_description", "image": {"image_id": 140556, "image_path": "experiment/dataset/val2017/000000140556.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述最显眼的黄色消防栓及其位置，最后描述工人们的状态和穿着，但不要提及任何背景中的车辆。", "constraints": [{"id": "c1", "type": "description", "content": "首先说明天气状况", "weight": 0.5}, {"id": "c2", "type": "attribute", "content": "详细描述最显眼的黄色消防栓及其位置", "weight": 0.8}, {"id": "c3", "type": "description", "content": "描述工人们的状态和穿着", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何背景中的车辆", "weight": 1.0}]}, "reference_answer": "天气晴朗，蓝天无云。画面中最显眼的是一个黄色的消防栓，它位于前景中央，被几块砖块支撑着。几位工人站在消防栓周围，他们穿着反光背心和安全帽，显得非常专注和认真。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021103_3826", "task_type": "complex_description", "image": {"image_id": 498857, "image_path": "experiment/dataset/val2017/000000498857.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气和环境，第二句话描述长颈鹿的外观特征，第三句话描述长颈鹿的姿态和行为，第四句话不要提及任何围栏或栅栏。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述长颈鹿的外观特征", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述长颈鹿的姿态和行为", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何围栏或栅栏", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎即将下雨，周围的树木郁郁葱葱。长颈鹿有着独特的斑点图案，身体修长而优雅。它站立着，头部微微倾斜，似乎在观察周围的环境。地面上覆盖着细小的石子，显得非常干净整洁。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021111_1316", "task_type": "complex_description", "image": {"image_id": 451144, "image_path": "experiment/dataset/val2017/000000451144.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天空和云朵的状态，第二句话描述两位滑雪者的穿着和装备，第三句话描述他们所处的地形特征，第四句话不要提及任何具体的颜色。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天空和云朵的状态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述两位滑雪者的穿着和装备", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述他们所处的地形特征", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何具体的颜色", "weight": 1.0}]}, "reference_answer": "天空中布满了厚重的云层，显得有些阴沉。两位滑雪者身穿保暖的外套和裤子，背着背包，脚踏滑雪板，准备开始他们的旅程。他们站在山顶上，周围是起伏的山脉和覆盖着积雪的地面。周围的环境充满了自然的气息，给人一种宁静而壮丽的感觉。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021119_2091", "task_type": "complex_description", "image": {"image_id": 113867, "image_path": "experiment/dataset/val2017/000000113867.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述建筑物的外观特征，第二句话描述钟楼上的时钟及其位置，第三句话描述建筑物左侧的窗户样式，第四句话不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述建筑物的外观特征", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述钟楼上的时钟及其位置", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述建筑物左侧的窗户样式", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "这是一座红砖砌成的高大建筑，具有古典风格的设计。钟楼顶部有一个白色的圆形时钟，位于塔尖下方的位置。建筑物左侧的窗户呈拱形，窗框由白色石材装饰。周围的环境显得宁静而庄重，没有出现任何人物的身影。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021129_1068", "task_type": "complex_description", "image": {"image_id": 221708, "image_path": "experiment/dataset/val2017/000000221708.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的场景。第一句话描述房间的整体氛围和风格，第二句话描述餐桌及其周围的椅子，第三句话描述厨房区域的布局和主要电器，第四句话请不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述房间的整体氛围和风格", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述餐桌及其周围的椅子", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述厨房区域的布局和主要电器", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "这个房间充满了温馨的乡村风格，木质墙壁和地板营造出一种温暖而舒适的氛围。圆形的木质餐桌周围摆放着几把同样材质的椅子，显得非常和谐。厨房区域位于左侧，有一个白色的冰箱和一个带有水槽的柜台，整体布局简洁实用。房间内没有出现任何人物，但可以感受到这里是一个适合家庭聚会的地方。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021135_5385", "task_type": "complex_description", "image": {"image_id": 528314, "image_path": "experiment/dataset/val2017/000000528314.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的滑雪场景，首先说明天气状况和雪地的条件，接着描述正在空中跳跃的滑雪者及其装备，最后请不要提及观众或任何背景元素。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况和雪地的条件", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "接着描述正在空中跳跃的滑雪者及其装备", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "最后请不要提及观众或任何背景元素", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光照耀在洁白无瑕的雪地上，反射出耀眼的光芒。一位身穿红白相间滑雪服的运动员正腾空而起，他的滑雪板交叉成X形，双手紧握滑雪杖保持平衡。滑雪者的动作优雅而有力，展现了高超的技巧和勇气。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021184_3188", "task_type": "complex_description", "image": {"image_id": 15746, "image_path": "experiment/dataset/val2017/000000015746.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的主要元素。第一句话描述消防栓的颜色和状态，第二句话描述背景中房屋的外观特征，第三句话描述前景中的植物及其颜色，第四句话不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述消防栓的颜色和状态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述背景中房屋的外观特征", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述前景中的植物及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "消防栓是鲜艳的红色，顶部有一些锈迹斑斑的痕迹。背景中的房屋是白色的，屋顶覆盖着灰色的瓦片，窗户周围有紫色的花朵装饰。前景中有绿色的草和几朵白色的蒲公英。周围的环境显得非常宁静和自然。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021191_8329", "task_type": "complex_description", "image": {"image_id": 61108, "image_path": "experiment/dataset/val2017/000000061108.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天气和环境氛围，接着详细描述自行车及其篮子里的物品，最后描述背景中的长椅，但不要提及任何垃圾箱。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述天气和环境氛围", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "接着详细描述自行车及其篮子里的物品", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "最后描述背景中的长椅", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "不要提及任何垃圾箱", "weight": 1.0}]}, "reference_answer": "这是一个晴朗的日子，阳光洒在公园的空地上，营造出一种宁静而舒适的氛围。一辆蓝色的自行车停在前景中，车篮里有一只可爱的黑白相间的小狗，它戴着紫色的项圈，旁边还放着一些彩色的玩具。背景中有几排木质长椅，整齐地排列着，似乎等待着人们来休息。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021197_2294", "task_type": "complex_description", "image": {"image_id": 203095, "image_path": "experiment/dataset/val2017/000000203095.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的食材准备场景，首先详细说明肉类的种类和数量，接着描述蔬菜的颜色和形状，最后请不要提及任何厨房用具或电器。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明肉类的种类和数量", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述蔬菜的颜色和形状", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何厨房用具或电器", "weight": 1.0}]}, "reference_answer": "图片中有七块猪蹄肉和两根香肠。胡萝卜是橙色的，呈圆柱形；洋葱是棕色的，呈球形；苹果是红色的，表面光滑；还有几颗大蒜和一些绿色的香草。这些食材整齐地摆放在台面上，显得非常新鲜。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021203_9866", "task_type": "complex_description", "image": {"image_id": 190648, "image_path": "experiment/dataset/val2017/000000190648.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的卧室场景，首先详细说明房间的光线和氛围，然后描述床上的状态及其颜色，最后请不要提及任何家具的颜色。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明房间的光线和氛围", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述床上的状态及其颜色", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何家具的颜色", "weight": 1.0}]}, "reference_answer": "房间内的光线透过窗户洒进来，营造出一种温暖而略显昏暗的氛围。床上的被褥凌乱不堪，白色的床单上布满了污渍和褶皱。周围的家具虽然陈旧，但它们的存在为这个空间增添了一丝生活气息。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021211_6236", "task_type": "complex_description", "image": {"image_id": 115885, "image_path": "experiment/dataset/val2017/000000115885.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先详细说明猫的外观和它注视的方向，然后描述笔记本电脑屏幕上的图案及其颜色，最后请不要提及任何与房间相关的背景细节。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明猫的外观和它注视的方向", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述笔记本电脑屏幕上的图案及其颜色", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何与房间相关的背景细节", "weight": 1.0}]}, "reference_answer": "这只猫有着黑色和棕色相间的毛发，耳朵竖立，正专注地盯着笔记本电脑屏幕。屏幕上显示着一个绿色的抽象图案，光线从中心向外辐射，形成一种动态的效果。周围的环境被忽略不计，只聚焦于猫和屏幕之间的互动。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021219_8110", "task_type": "complex_description", "image": {"image_id": 215072, "image_path": "experiment/dataset/val2017/000000215072.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天气和背景环境，然后详细描述坐在左侧的女士及其穿着，最后描述右侧人物的动作，但不要提及任何与食物或饮料相关的内容。", "constraints": [{"id": "c1", "type": "sequence", "content": "用一句话描述天气和背景环境", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "详细描述坐在左侧的女士及其穿着", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "不要提及任何与食物或饮料相关的内容", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "描述右侧人物的动作", "weight": 1.0}]}, "reference_answer": "晴朗的天空下，一片绿草如茵的草坪延伸至海边，远处可见沙滩和海洋。坐在左侧的女士穿着黑色外套，搭配红色裤子和粉色围巾，她的头发整齐地梳在脑后，神情专注。右侧的人物正低头为女士服务，动作轻柔而专业。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021227_1689", "task_type": "complex_description", "image": {"image_id": 31620, "image_path": "experiment/dataset/val2017/000000031620.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的婚礼场景。第一句话描述帐篷内的装饰和氛围，第二句话描述新郎新娘的位置和动作，第三句话描述背景中的人物及其活动，第四句话请不要提及任何乐器。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述帐篷内的装饰和氛围", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述新郎新娘的位置和动作", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述背景中的人物及其活动", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何乐器", "weight": 1.0}]}, "reference_answer": "帐篷内挂满了彩色的旗帜，营造出温馨而喜庆的氛围。新郎新娘站在蛋糕前，正准备一起切下他们的婚礼蛋糕。背景中有几位宾客在交谈，其中一位女士正在拍照记录这美好的时刻。地板上铺着木质地板，显得格外典雅。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021232_3328", "task_type": "complex_description", "image": {"image_id": 108026, "image_path": "experiment/dataset/val2017/000000108026.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先详细说明人物的手部动作和所接触的物品，其次描述键盘的颜色和布局，最后请不要提及任何背景元素。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明人物的手部动作和所接触的物品", "weight": 0.5}, {"id": "c2", "type": "attribute", "content": "描述键盘的颜色和布局", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何背景元素", "weight": 1.0}]}, "reference_answer": "人物的右手正握着一个黑色的游戏手柄，左手则放在键盘上，似乎在进行游戏操作。键盘是黑色的，按键排列整齐，可以看到一些常用的字母键和功能键。周围的环境被忽略不计，专注于手部和键盘的动作与细节。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021239_6649", "task_type": "complex_description", "image": {"image_id": 135890, "image_path": "experiment/dataset/val2017/000000135890.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的主要元素。第一句话描述建筑物的外观和颜色，第二句话描述建筑物左侧的树木及其状态，第三句话描述天空的颜色和云朵的状态，第四句话不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述建筑物的外观和颜色", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述建筑物左侧的树木及其状态", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述天空的颜色和云朵的状态", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "这座宏伟的建筑物呈现出金黄色的外观，其复杂的结构和尖塔令人印象深刻。在建筑物左侧，几棵高大的树木郁郁葱葱，树叶随风轻轻摇曳。天空呈现出淡蓝色，几朵白云悠闲地漂浮着。周围的环境显得宁静而祥和，仿佛时间在此刻静止。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021245_7750", "task_type": "complex_description", "image": {"image_id": 284282, "image_path": "experiment/dataset/val2017/000000284282.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要物体及其位置关系。首先，描述搅拌机的颜色和状态；其次，说明打印机的品牌和颜色，并指出它与搅拌机的相对位置；最后，请不要提及任何电线或插头。", "constraints": [{"id": "c1", "type": "attribute", "content": "描述搅拌机的颜色和状态", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "说明打印机的品牌和颜色", "weight": 0.8}, {"id": "c3", "type": "relation", "content": "指出打印机与搅拌机的相对位置", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何电线或插头", "weight": 1.0}]}, "reference_answer": "搅拌机是白色的，看起来正在运行中。打印机是爱普生品牌的，也是白色的，位于搅拌机的右侧。两者都放置在一张绿色的桌子上，显得非常整洁。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021251_4400", "task_type": "complex_description", "image": {"image_id": 100510, "image_path": "experiment/dataset/val2017/000000100510.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先描述马的颜色和姿态，然后描述骑手的服装和动作，最后描述背景中的观众，但不要提及任何障碍物。", "constraints": [{"id": "c1", "type": "attribute", "content": "描述马的颜色和姿态", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "描述骑手的服装和动作", "weight": 0.8}, {"id": "c3", "type": "relation", "content": "描述背景中的观众", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何障碍物", "weight": 1.0}]}, "reference_answer": "这匹马是棕色的，正腾空而起，四蹄离地。骑手穿着白色上衣和黑色裤子，戴着头盔，身体前倾，紧紧抓住缰绳。背景中可以看到几位观众站在一旁观看比赛，他们似乎非常专注。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021257_7949", "task_type": "complex_description", "image": {"image_id": 31217, "image_path": "experiment/dataset/val2017/000000031217.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述正在打网球的男子及其动作，最后描述他左侧的环境，但不要提及任何与球拍相关的细节。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "description", "content": "详细描述正在打网球的男子及其动作", "weight": 0.5}, {"id": "c3", "type": "relation", "content": "描述他左侧的环境", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何与球拍相关的细节", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎预示着一场即将到来的雨。一名穿着蓝色T恤和黑色短裤的男子正全神贯注地准备击球，他的身体微微前倾，目光紧盯着飞来的网球。在他的左侧，是一片茂密的树林，树木郁郁葱葱，为这片场地增添了一抹自然的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021264_3710", "task_type": "complex_description", "image": {"image_id": 66231, "image_path": "experiment/dataset/val2017/000000066231.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的厨房场景，首先详细说明厨师们的活动，然后描述他们所使用的工具和设备，最后请不要提及任何食材或食物。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明厨师们的活动", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述他们所使用的工具和设备", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何食材或食物", "weight": 1.0}]}, "reference_answer": "几位厨师在忙碌地工作，有的在切菜，有的在搅拌锅中的内容物，还有的在整理餐具。他们使用了不锈钢的炉灶、大锅、砧板和各种厨具，整个厨房显得井然有序。周围的环境整洁明亮，光线充足，营造出一种专业而高效的工作氛围。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021274_9651", "task_type": "complex_description", "image": {"image_id": 176701, "image_path": "experiment/dataset/val2017/000000176701.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气和树木的状态；第二句话描述最显眼的标志及其颜色；第三句话描述标志左侧的电线杆特征，但不要提及任何车辆；第四句话描述标志下方的箭头方向。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和树木的状态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的标志及其颜色", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "第三句话描述标志左侧的电线杆特征", "weight": 0.8}, {"id": "c5", "type": "exclusion", "content": "第三句话不要提及任何车辆", "weight": 1.0}, {"id": "c6", "type": "sequence", "content": "第四句话描述标志下方的箭头方向", "weight": 1.0}]}, "reference_answer": "天空晴朗，几朵白云点缀其间，树木光秃秃的，显得有些萧瑟。最显眼的是绿色的“EAST ST”标志，上面还有一个蓝色的房车标志。标志左侧的电线杆上贴着一些白色的纸张，看起来有些破旧。标志下方的箭头指向正上方，表示直行的方向。", "metadata": {"scene_type": "unknown", "constraint_count": 6}}
{"id": "m_faith_1751021282_7981", "task_type": "complex_description", "image": {"image_id": 318455, "image_path": "experiment/dataset/val2017/000000318455.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述桌子上的甜点种类，第二句话描述最显眼的蛋糕及其颜色，第三句话描述桌布的样式和颜色，第四句话请不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述桌子上的甜点种类", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的蛋糕及其颜色", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述桌布的样式和颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "桌子上摆放着各种各样的甜点，包括色彩缤纷的纸杯蛋糕和切片蛋糕。最显眼的是一块多层的巧克力蛋糕，表面覆盖着白色的奶油。桌布是白色的，边缘有精致的蕾丝装饰。周围的环境显得非常温馨，充满了节日的气氛。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021292_4276", "task_type": "complex_description", "image": {"image_id": 557884, "image_path": "experiment/dataset/val2017/000000557884.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的植物。第一句话描述植物的叶子颜色和形状，第二句话描述植物所在的容器及其材质，第三句话描述植物左侧的墙壁颜色，第四句话不要提及任何关于植物的高度。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述植物的叶子颜色和形状", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述植物所在的容器及其材质", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述植物左侧的墙壁颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何关于植物的高度", "weight": 1.0}]}, "reference_answer": "植物的叶子呈现出鲜亮的绿色，形状为椭圆形且边缘光滑。植物被放置在一个透明的玻璃瓶中，瓶子看起来非常简洁。植物左侧的墙壁是浅绿色的，给人一种清新自然的感觉。阳光透过窗户洒在植物上，显得生机勃勃。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021298_6261", "task_type": "complex_description", "image": {"image_id": 135604, "image_path": "experiment/dataset/val2017/000000135604.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述球员的服装和动作，最后描述球场周围的环境，但不要提及任何观众。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细描述球员的服装和动作", "weight": 0.8}, {"id": "c3", "type": "sequence", "content": "最后描述球场周围的环境", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "不要提及任何观众", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光明媚，适合户外运动。球员穿着白色球衣，上面有蓝色条纹，搭配白色短裤和黄色足球鞋，他正专注地控制着脚下的足球，准备下一步的动作。球场周围是郁郁葱葱的树木和整齐的围栏，远处可以看到一个足球门框，整个场地显得非常开阔和整洁。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021304_4759", "task_type": "complex_description", "image": {"image_id": 127270, "image_path": "experiment/dataset/val2017/000000127270.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的网球比赛场景。首先，描述运动员的姿势和动作；其次，说明她所穿的服装颜色和款式；最后，请不要提及观众席上的任何细节。", "constraints": [{"id": "c1", "type": "description", "content": "描述运动员的姿势和动作", "weight": 0.5}, {"id": "c2", "type": "attribute", "content": "说明她所穿的服装颜色和款式", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及观众席上的任何细节", "weight": 1.0}]}, "reference_answer": "运动员正准备击球，双手高举球拍，身体微微前倾，显示出专注和力量。她穿着白色无袖上衣和蓝色短裙，搭配白色的运动鞋，整体装扮简洁而专业。场地上只有她的身影，背景模糊，突显了她的存在感。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021312_5023", "task_type": "complex_description", "image": {"image_id": 153782, "image_path": "experiment/dataset/val2017/000000153782.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天空的颜色和天气状况，第二句话描述最显眼的建筑及其特征，第三句话描述前景中的路灯样式和颜色，第四句话请不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天空的颜色和天气状况", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的建筑及其特征", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述前景中的路灯样式和颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "天空呈现出淡蓝色，天气晴朗无云。最显眼的是一座高大的钟楼，其顶部有一个大钟面，建筑风格古典而庄重。前景中的路灯是黑色的，有多个灯罩，设计复古且优雅。周围的建筑物同样具有古典风格，细节丰富。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021318_6069", "task_type": "complex_description", "image": {"image_id": 448410, "image_path": "experiment/dataset/val2017/000000448410.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的火车站场景，首先详细说明天气状况和光线条件，接着描述最显眼的火车及其编号，最后描述站台上人们的活动，但不要提及任何车辆的品牌或型号。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明天气状况和光线条件", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述最显眼的火车及其编号", "weight": 0.5}, {"id": "c3", "type": "description", "content": "描述站台上人们的活动", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何车辆的品牌或型号", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光明媚，光线充足，照亮了整个火车站。最显眼的是一列标有编号2E85的火车，它正停靠在站台中央。站台上人们来来往往，有的在交谈，有的在等待列车，场面热闹非凡。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021324_3296", "task_type": "complex_description", "image": {"image_id": 404128, "image_path": "experiment/dataset/val2017/000000404128.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述飞机的颜色和型号，然后用一句话描述飞机前方的地面情况，最后用一句话描述天空的状态，但不要提及任何云朵。", "constraints": [{"id": "c1", "type": "attribute", "content": "描述飞机的颜色和型号", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "描述飞机前方的地面情况", "weight": 0.8}, {"id": "c3", "type": "attribute", "content": "描述天空的状态", "weight": 0.8}, {"id": "c4", "type": "exclusion", "content": "不要提及任何云朵", "weight": 1.0}]}, "reference_answer": "这是一架灰色机身、黄色螺旋桨的P-51战斗机。飞机前方是平整的灰色跑道，周围是绿色的草地。天空呈现出淡蓝色，非常清澈。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021334_5303", "task_type": "complex_description", "image": {"image_id": 278848, "image_path": "experiment/dataset/val2017/000000278848.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气状况和地面情况，第二句话描述最显眼的物体及其颜色，第三句话描述人物的动作和位置，但不要提及他们的面部特征，第四句话描述背景建筑的特点，但不要提及任何车辆。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气状况和地面情况", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的物体及其颜色", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及他们的面部特征", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何车辆", "weight": 1.0}]}, "reference_answer": "天空阴沉，地面湿漉漉的，显然是刚下过雨。最显眼的是几把黑色的雨伞，为人们遮挡着雨水。几位行人站在公交车站旁，有的撑着伞，有的低头看着地面。背景中可以看到一座古典风格的建筑，柱子和装饰细节显得非常精致。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021343_7258", "task_type": "complex_description", "image": {"image_id": 274708, "image_path": "experiment/dataset/val2017/000000274708.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和背景山脉，第二句话描述最显眼的滑雪者及其动作，第三句话描述右侧山脚下的人物活动，第四句话请不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和背景山脉", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的滑雪者及其动作", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述右侧山脚下的人物活动", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何建筑物", "weight": 1.0}]}, "reference_answer": "天空晴朗无云，背景是连绵起伏的雪山，山顶覆盖着皑皑白雪。最显眼的是一位穿着黑色滑雪服的滑雪者，他正从斜坡上滑下，姿态优雅。右侧山脚下有几位滑雪者在休息或准备出发，他们分散在雪地上，显得非常放松。周围的自然风光令人陶醉，人们尽情享受着滑雪的乐趣。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021350_3847", "task_type": "complex_description", "image": {"image_id": 328959, "image_path": "experiment/dataset/val2017/000000328959.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的浴室场景。第一句话描述浴室的整体布局，第二句话描述马桶的特殊设计及其位置，第三句话描述淋浴间的装饰特点，第四句话不要提及任何管道或水管。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述浴室的整体布局", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述马桶的特殊设计及其位置", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述淋浴间的装饰特点", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何管道或水管", "weight": 1.0}]}, "reference_answer": "这间浴室布局紧凑，包含一个马桶和一个淋浴间。马桶有一个独特的黑色盖子，位于浴室的左侧角落。淋浴间的玻璃门上有精美的花纹装饰，显得非常优雅。地面铺有灰色瓷砖，整个空间显得干净整洁。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021357_6859", "task_type": "complex_description", "image": {"image_id": 523811, "image_path": "experiment/dataset/val2017/000000523811.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述背景环境，第二句话描述小鸟及其位置，第三句话描述小鸟左侧的植物特征，第四句话请不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述背景环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述小鸟及其位置", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "第三句话描述小鸟左侧的植物特征", "weight": 0.8}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何建筑物", "weight": 1.0}]}, "reference_answer": "背景是一片模糊的灰色屋顶和红色砖墙，营造出一种宁静的氛围。一只棕色的小鸟栖息在一根古老的木桩上，显得格外引人注目。小鸟左侧的植物细长而翠绿，叶片呈现出生机勃勃的姿态。周围的自然元素与小鸟和谐共存，构成了一幅美丽的画面。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021365_3146", "task_type": "complex_description", "image": {"image_id": 331280, "image_path": "experiment/dataset/val2017/000000331280.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先详细说明马的外观和姿态，接着描述两位穿橙色上衣的人的位置和动作，最后请不要提及观众席上的观众。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明马的外观和姿态", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述两位穿橙色上衣的人的位置和动作", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及观众席上的观众", "weight": 1.0}]}, "reference_answer": "图片中的白马体型健硕，毛色洁白如雪，鬃毛整齐地向一侧梳理，显得非常优雅。它正站立在表演场中央，姿态稳重，似乎正在等待指令。两位身穿橙色上衣的工作人员分别跪在马的两侧，他们专注地看着马，似乎在进行某种训练或引导。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021371_2261", "task_type": "complex_description", "image": {"image_id": 91619, "image_path": "experiment/dataset/val2017/000000091619.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明标志的颜色和形状，然后描述标志下方的文字内容，最后请不要提及任何关于天气的信息。", "constraints": [{"id": "c1", "type": "attribute", "content": "说明标志的颜色和形状", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "描述标志下方的文字内容", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何关于天气的信息", "weight": 1.0}]}, "reference_answer": "标志是红色的八边形。标志下方写着'ALL WAY'。街道旁边有一个金属杆支撑着这个标志。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021377_3066", "task_type": "complex_description", "image": {"image_id": 310072, "image_path": "experiment/dataset/val2017/000000310072.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天气状况和光线条件，接着详细描述长椅上的物品及其位置，最后描述长椅周围的环境，但不要提及任何车辆。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述天气状况和光线条件", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细描述长椅上的物品及其位置", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "最后描述长椅周围的环境，但不要提及任何车辆", "weight": 1.0}]}, "reference_answer": "阳光明媚，天空晴朗无云，光线充足。长椅上放着一顶浅色的帽子，位于长椅左侧靠背处。长椅周围是绿色的草地，旁边有一条人行道，树木的影子投射在地面上，营造出斑驳的光影效果。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021429_3501", "task_type": "complex_description", "image": {"image_id": 410487, "image_path": "experiment/dataset/val2017/000000410487.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的厨房场景，首先说明烤箱的状态和内部情况，然后描述地板上的状况，最后请不要提及任何清洁工具或用品。", "constraints": [{"id": "c1", "type": "description", "content": "说明烤箱的状态和内部情况", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述地板上的状况", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何清洁工具或用品", "weight": 1.0}]}, "reference_answer": "烤箱门是打开的，可以看到内部有明显的烧焦痕迹和食物残渣。地板上散落着许多黄色的液体和固体残留物，显得非常凌乱。周围的环境看起来有些杂乱无章，但没有看到任何清洁工具或用品。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021435_6456", "task_type": "complex_description", "image": {"image_id": 469174, "image_path": "experiment/dataset/val2017/000000469174.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天空的状态和飞机的位置；其次，详细描述美国国旗的颜色和飘扬状态；最后，请描述树木的种类和颜色，但不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述天空的状态和飞机的位置", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细描述美国国旗的颜色和飘扬状态", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "最后不要提及任何建筑物", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "描述树木的种类和颜色", "weight": 0.8}]}, "reference_answer": "天空中布满了白云，一架飞机正从画面右侧飞过。美国国旗在风中飘扬，红色、白色和蓝色的条纹清晰可见，星条旗迎风招展。树木主要是绿色的，包括松树和橡树等，它们郁郁葱葱，生机勃勃。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021442_1768", "task_type": "complex_description", "image": {"image_id": 137727, "image_path": "experiment/dataset/val2017/000000137727.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述最显眼的火车及其颜色和标志，最后描述前景中的人物活动，但不要提及任何行李。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细描述最显眼的火车及其颜色和标志", "weight": 0.8}, {"id": "c3", "type": "sequence", "content": "最后描述前景中的人物活动", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "不要提及任何行李", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎预示着即将下雨。最显眼的是一列银色的火车，车身上有蓝色条纹和加拿大国旗标志，旁边是黄色和灰色相间的机车。几位工作人员正在忙碌地操作一辆红色的小型拖拉机，他们似乎在进行日常的维护工作。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021449_5116", "task_type": "complex_description", "image": {"image_id": 230450, "image_path": "experiment/dataset/val2017/000000230450.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的主要元素，首先详细说明左侧的停车计时器及其颜色，然后描述背景中正在施工的建筑物的颜色和结构特点，最后请不要提及任何行人。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明左侧的停车计时器及其颜色", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述背景中正在施工的建筑物的颜色和结构特点", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何行人", "weight": 1.0}]}, "reference_answer": "左侧的停车计时器是黑色的，上面有黄色的标签和数字。背景中的建筑物正在施工，其框架由金属脚手架构成，呈现出灰色和棕色的色调，部分区域覆盖着橙色的安全网。周围的环境显得有些空旷，没有出现行人的身影。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021456_8200", "task_type": "complex_description", "image": {"image_id": 273617, "image_path": "experiment/dataset/val2017/000000273617.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片。第一句话描述天空的颜色和云朵的状态；第二句话描述路灯及其发出的光线颜色；第三句话描述左侧的交通标志，但不要提及它的具体形状；第四句话描述道路右侧的树木，说明它们的颜色和密度。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天空的颜色和云朵的状态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述路灯及其发出的光线颜色", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及交通标志的具体形状", "weight": 1.0}, {"id": "c5", "type": "attribute", "content": "第四句话描述道路右侧的树木，说明它们的颜色和密度", "weight": 0.8}]}, "reference_answer": "天空呈现出深蓝色，云朵稀疏地散布在空中。路灯发出温暖的黄色光芒，照亮了周围的区域。左侧有一个红色的交通标志，上面写着“STOP”。道路右侧的树木是深绿色的，排列得比较密集，形成了一道自然的屏障。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021463_6950", "task_type": "complex_description", "image": {"image_id": 47112, "image_path": "experiment/dataset/val2017/000000047112.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的主要元素，首先详细说明披萨的配料和外观，然后描述背景中餐厅的氛围，最后请不要提及任何饮料。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明披萨的配料和外观", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述背景中餐厅的氛围", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何饮料", "weight": 1.0}]}, "reference_answer": "披萨上铺满了金黄色的奶酪、新鲜的蘑菇片和几块嫩滑的火腿，边缘微微焦黄，看起来非常诱人。背景中的餐厅布置温馨，桌椅整齐排列，墙上挂着各种装饰画，营造出一种舒适的用餐氛围。周围的客人正在享受美食，整个场景显得十分热闹。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021469_7047", "task_type": "complex_description", "image": {"image_id": 280710, "image_path": "experiment/dataset/val2017/000000280710.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天气状况，接着详细描述前景中人物的活动和穿着，最后描述背景中的建筑风格，但不要提及任何车辆。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述天气状况", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "接着详细描述前景中人物的活动和穿着", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "最后描述背景中的建筑风格", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "不要提及任何车辆", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎刚下过雨，地面湿漉漉的。几位行人正在街道上行走，其中一位女士穿着黑色外套和绿色围巾，手里提着一个绿色的手提包；另一位男士则穿着深色夹克和牛仔裤，背着红色背包。背景中的建筑物是典型的欧洲风格，有着红砖外墙和白色窗框，显得古朴而典雅。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021475_5587", "task_type": "complex_description", "image": {"image_id": 318114, "image_path": "experiment/dataset/val2017/000000318114.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的早餐场景，首先详细说明橙汁的颜色和状态，接着描述蛋糕的外观和质地，最后请不要提及任何餐具或桌布。", "constraints": [{"id": "c1", "type": "attribute", "content": "详细说明橙汁的颜色和状态", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "描述蛋糕的外观和质地", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何餐具或桌布", "weight": 1.0}]}, "reference_answer": "橙汁呈现出鲜艳的金黄色，看起来非常清澈且充满活力。蛋糕表面覆盖着一层浓郁的巧克力酱，内部质地松软，可以看到一些坚果颗粒。这顿早餐看起来非常诱人，让人食欲大增。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021483_9365", "task_type": "complex_description", "image": {"image_id": 577584, "image_path": "experiment/dataset/val2017/000000577584.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的厨房场景。第一句话描述厨房的整体色调和风格，第二句话描述微波炉的位置及其外观特征，第三句话描述微波炉左侧的物品及其颜色，第四句话请不要提及任何电器的品牌。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述厨房的整体色调和风格", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述微波炉的位置及其外观特征", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述微波炉左侧的物品及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何电器的品牌", "weight": 1.0}]}, "reference_answer": "厨房整体色调为温暖的米色，给人一种温馨而复古的感觉。微波炉嵌入在橱柜中，银色的外壳和黑色的门显得现代而简洁。微波炉左侧放置着一个白色的带盖陶瓷碗，与周围的木质台面相得益彰。厨房的设计注重实用性和美观性，各种设施布局合理，方便使用。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021492_2004", "task_type": "complex_description", "image": {"image_id": 13546, "image_path": "experiment/dataset/val2017/000000013546.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先详细说明天气状况和光线条件，接着描述最显眼的滑板少年及其动作，最后描述他周围的环境，但不要提及任何树木。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明天气状况和光线条件", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述最显眼的滑板少年及其动作", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何树木", "weight": 1.0}, {"id": "c4", "type": "relation", "content": "描述他周围的环境", "weight": 0.5}]}, "reference_answer": "阳光明媚，天空湛蓝无云，光线充足且温暖。一位穿着黄色T恤和棕色裤子的滑板少年正在做一个高难度的动作，他的身体倾斜，双臂张开以保持平衡。周围是一个开阔的广场，有几条长椅和一个带有涂鸦的石墩，远处可以看到一些行人和建筑物。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021497_3214", "task_type": "complex_description", "image": {"image_id": 428218, "image_path": "experiment/dataset/val2017/000000428218.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述天气和环境氛围，接着详细描述右侧人物的动作和穿着，最后描述左侧人物的姿势但不要提及他的具体服装颜色。", "constraints": [{"id": "c1", "type": "sequence", "content": "用一句话描述天气和环境氛围", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "详细描述右侧人物的动作和穿着", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "描述左侧人物的姿势但不要提及他的具体服装颜色", "weight": 1.0}]}, "reference_answer": "这是一个阳光明媚的日子，绿树成荫的庭院显得格外宁静。右侧的人物正专注地投掷飞盘，她穿着白色上衣和浅色裤子，动作流畅而有力。左侧的人物站立着，双手自然下垂，姿态放松。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021504_5401", "task_type": "complex_description", "image": {"image_id": 23126, "image_path": "experiment/dataset/val2017/000000023126.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张黑白照片中的场景。第一句话描述整体氛围和色调，第二句话描述骑马者的姿态和表情，第三句话描述马匹的特征和动作，第四句话请不要提及任何背景建筑。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述整体氛围和色调", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述骑马者的姿态和表情", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述马匹的特征和动作", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何背景建筑", "weight": 1.0}]}, "reference_answer": "这张黑白照片营造出一种怀旧而动感的氛围，灰阶色调增强了画面的深度和质感。骑马者站立在马背上，身体微微前倾，脸上带着专注的表情。马匹肌肉线条分明，正迈着稳健的步伐向前奔跑。周围的环境充满了运动的气息，人们仿佛能感受到风的呼啸。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021512_1737", "task_type": "complex_description", "image": {"image_id": 548524, "image_path": "experiment/dataset/val2017/000000548524.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和海面状况，第二句话描述最显眼的冲浪者及其动作，第三句话请不要提及任何建筑物，第四句话描述冲浪者的右侧是什么颜色。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和海面状况", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的冲浪者及其动作", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何建筑物", "weight": 1.0}, {"id": "c5", "type": "attribute", "content": "第四句话描述冲浪者的右侧是什么颜色", "weight": 0.8}]}, "reference_answer": "天空晴朗无云，海面波光粼粼，海水呈现出深浅不一的蓝色。一位身穿白色泳衣的冲浪者正站在冲浪板上，身体前倾，准备迎接下一个浪头。周围的海水在阳光下闪烁着光芒，显得格外迷人。冲浪者的右侧是清澈的蓝绿色海水，与远处的天际线相接。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021522_2512", "task_type": "complex_description", "image": {"image_id": 190841, "image_path": "experiment/dataset/val2017/000000190841.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和环境，第二句话描述滑板者的动作和穿着，第三句话描述滑板者左侧的物体及其颜色，第四句话请不要提及任何树木。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述滑板者的动作和穿着", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "第三句话描述滑板者左侧的物体及其颜色", "weight": 0.8}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何树木", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光明媚，街道两旁的建筑物在阳光下显得格外清晰。滑板者正专注地跳跃过一个木制障碍物，他穿着黑色短袖和短裤，脚上是白色运动鞋。滑板者左侧是一个棕色的木制长椅，上面放着一些物品。周围的行人和建筑在阳光下显得生机勃勃。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021533_4355", "task_type": "complex_description", "image": {"image_id": 190923, "image_path": "experiment/dataset/val2017/000000190923.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述街道的整体氛围，第二句话描述最显眼的交通信号灯及其状态，第三句话描述右侧人物的穿着和动作，但不要提及她的背包。第四句话描述左侧的垃圾桶及其周围的环境。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述街道的整体氛围", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的交通信号灯及其状态", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述右侧人物的穿着和动作", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第三句话不要提及她的背包", "weight": 1.0}, {"id": "c6", "type": "sequence", "content": "第四句话描述左侧的垃圾桶及其周围的环境", "weight": 1.0}]}, "reference_answer": "这条街道充满了都市的繁忙气息，行人来来往往，树木和建筑物交织在一起。最显眼的交通信号灯显示红灯亮起，提醒行人和车辆停下等待。右侧的人物穿着白色上衣和黑色短裙，正低头看着手机。左侧有一个黑色的垃圾桶，周围散落着一些蓝色的布料，旁边是一棵茂盛的绿树。", "metadata": {"scene_type": "unknown", "constraint_count": 6}}
{"id": "m_faith_1751021541_5420", "task_type": "complex_description", "image": {"image_id": 388056, "image_path": "experiment/dataset/val2017/000000388056.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张棒球场的场景。第一句话描述击球手的状态和姿势，第二句话描述捕手的位置和装备，第三句话描述裁判员的动作和位置，第四句话请不要提及任何观众。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述击球手的状态和姿势", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述捕手的位置和装备", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述裁判员的动作和位置", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何观众", "weight": 1.0}]}, "reference_answer": "击球手Longoria正准备挥棒，身体微微前倾，双手紧握球棒。捕手蹲在本垒板后方，戴着头盔和护具，手套张开准备接球。裁判员站在捕手身后，身体前倾，专注地观察着比赛。场地上只有球员和工作人员，大家都在紧张地等待下一刻的到来。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021549_7300", "task_type": "complex_description", "image": {"image_id": 256941, "image_path": "experiment/dataset/val2017/000000256941.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张图片中的场景。第一句话描述伞的颜色和图案，第二句话描述自行车篮子里的物品及其颜色，第三句话描述背景墙的特点，第四句话请不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述伞的颜色和图案", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述自行车篮子里的物品及其颜色", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述背景墙的特点", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "伞是蓝色的，上面有花卉图案，显得非常漂亮。自行车篮子里放着一些黄色的物品，可能是布料或衣物。背景墙是由白色瓷砖组成的，墙上有一些装饰性的方形图案。周围的环境显得宁静而有序，没有出现任何人物。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021578_6557", "task_type": "complex_description", "image": {"image_id": 168330, "image_path": "experiment/dataset/val2017/000000168330.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述建筑物的外观特征，第二句话描述街道上的路灯及其照明情况，第三句话描述最显眼的物体及其颜色，第四句话不要提及任何行人。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述建筑物的外观特征", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述街道上的路灯及其照明情况", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述最显眼的物体及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何行人", "weight": 1.0}]}, "reference_answer": "这是一座红砖建筑，拥有多个白色窗框的拱形窗户和一个装饰性的屋顶边缘。街道上排列着几盏高高的路灯，发出温暖的光芒照亮了周围区域。最显眼的物体是一个绿色的钟楼，其明亮的黄色表盘在夜晚格外引人注目。周围的环境显得宁静而有序，没有行人的身影。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021586_4163", "task_type": "complex_description", "image": {"image_id": 79969, "image_path": "experiment/dataset/val2017/000000079969.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的网球比赛场景。首先，描述球员的姿势和动作；其次，描述观众席上的观众表情和服装；最后，请不要提及任何广告牌或赞助商标志。", "constraints": [{"id": "c1", "type": "description", "content": "描述球员的姿势和动作", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述观众席上的观众表情和服装", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何广告牌或赞助商标志", "weight": 1.0}]}, "reference_answer": "球员正高高跃起，右手紧握球拍，准备击打空中的网球，展现出专注而有力的姿态。观众席上的人们神情各异，有的聚精会神地注视着比赛，有的则在交谈，他们穿着各式各样的夏季服装，色彩斑斓。周围的气氛充满了紧张与期待，每个人都沉浸在比赛的精彩瞬间中。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021593_6806", "task_type": "complex_description", "image": {"image_id": 456662, "image_path": "experiment/dataset/val2017/000000456662.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的小女孩及其周围的环境。首先，详细描述她的服装和表情；其次，描述她身后的冰箱上的装饰物，但不要提及任何照片；最后，请说明地板的颜色和材质。", "constraints": [{"id": "c1", "type": "description", "content": "详细描述她的服装和表情", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述她身后的冰箱上的装饰物", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何照片", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "说明地板的颜色和材质", "weight": 0.8}]}, "reference_answer": "小女孩穿着一件印有粉色独角兽图案的白色T恤，搭配彩色格子短裤和黑色凉鞋，脸上带着灿烂的笑容。她身后的冰箱上贴着几张便签和一个夹子，夹子里似乎夹着一些纸张。地板是深棕色的木质地板，显得非常干净整洁。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021600_2238", "task_type": "complex_description", "image": {"image_id": 562243, "image_path": "experiment/dataset/val2017/000000562243.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的主要人物。第一句话描述他的面部特征和表情，第二句话描述他所穿的衣物颜色和款式，第三句话描述他左侧的背景颜色，第四句话不要提及任何眼镜。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述他的面部特征和表情", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述他所穿的衣物颜色和款式", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述他左侧的背景颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何眼镜", "weight": 1.0}]}, "reference_answer": "这位男士有着短发和胡须，表情严肃而专注。他穿着一件紫色衬衫和一条带有细点图案的领带。在他的左侧，背景呈现出深灰色的色调。周围的光线柔和，突显了他的轮廓。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021605_5544", "task_type": "complex_description", "image": {"image_id": 443969, "image_path": "experiment/dataset/val2017/000000443969.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的主要场景，首先描述天气状况，然后详细说明小女孩的穿着和她手中的物品，最后描述购物车内的物品但不要提及任何人物。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先描述天气状况", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细说明小女孩的穿着和她手中的物品", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "最后描述购物车内的物品但不要提及任何人物", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎刚刚下过雨，地面还有些湿润。小女孩穿着粉色条纹裤子和浅绿色带有花朵图案的上衣，手里拿着一把粉黄色相间的卡通伞。购物车内装有几桶黄色包装的清洁剂。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021639_6327", "task_type": "complex_description", "image": {"image_id": 227478, "image_path": "experiment/dataset/val2017/000000227478.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张黑白照片中的场景。第一句话描述两位主要人物的相对位置和表情；第二句话描述他们所坐的长椅及其设计特点；第三句话描述背景中的树木和草地，但不要提及任何建筑物；第四句话描述整体氛围，强调照片的黑白色调。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述两位主要人物的相对位置和表情", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述他们所坐的长椅及其设计特点", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何建筑物", "weight": 1.0}, {"id": "c5", "type": "attribute", "content": "第四句话描述整体氛围，强调照片的黑白色调", "weight": 0.8}]}, "reference_answer": "左边的女孩坐在右边女孩的左侧，两人都面带微笑，显得非常亲密。她们坐在一张装饰精美的铁艺长椅上，长椅的设计复杂且具有古典风格。背景中可以看到几棵大树和一片修剪整齐的草地，树木的阴影在草地上投下斑驳的光影。整张照片以黑白色调呈现，营造出一种怀旧而宁静的氛围。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021645_8546", "task_type": "complex_description", "image": {"image_id": 147518, "image_path": "experiment/dataset/val2017/000000147518.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的浴室场景，首先详细说明浴室的布局和主要设施，然后描述最显眼的装饰元素及其颜色，最后请不要提及任何与清洁用品相关的内容。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明浴室的布局和主要设施", "weight": 0.5}, {"id": "c2", "type": "attribute", "content": "描述最显眼的装饰元素及其颜色", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何与清洁用品相关的内容", "weight": 1.0}]}, "reference_answer": "这间浴室布局紧凑，包含一个带有木质盖板的马桶、一个带淋浴帘的浴缸和一个木质柜子。最显眼的装饰元素是挂在墙上的木质小柜子，其颜色为浅棕色，与浴室的整体色调相协调。周围的瓷砖地板呈现出灰色调，增添了一丝现代感。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021662_8041", "task_type": "complex_description", "image": {"image_id": 560256, "image_path": "experiment/dataset/val2017/000000560256.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的水果市场场景。首先，用一句话描述市场的整体氛围；其次，详细说明最显眼的水果种类及其颜色；最后，请不要提及任何价格信息。", "constraints": [{"id": "c1", "type": "count", "content": "用三句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述市场的整体氛围", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话详细说明最显眼的水果种类及其颜色", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何价格信息", "weight": 1.0}]}, "reference_answer": "市场充满了生机和活力，各种新鲜水果琳琅满目。最显眼的是金黄色的香蕉和绿色的牛油果，它们整齐地摆放在摊位上。顾客们在挑选着自己喜欢的水果，享受着购物的乐趣。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021670_4380", "task_type": "complex_description", "image": {"image_id": 560911, "image_path": "experiment/dataset/val2017/000000560911.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先描述男子的姿势和表情，然后描述他手中的物品及其颜色，最后描述背景中的家具，但不要提及任何衣物。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先描述男子的姿势和表情", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "然后描述他手中的物品及其颜色", "weight": 0.8}, {"id": "c3", "type": "sequence", "content": "最后描述背景中的家具", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "不要提及任何衣物", "weight": 1.0}]}, "reference_answer": "男子坐在沙发上，双腿交叉，右手拿着手机，表情专注。他手中的手机是白色的。背景中可以看到一张棕色的皮质沙发和一个打开的行李箱。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021677_3585", "task_type": "complex_description", "image": {"image_id": 369812, "image_path": "experiment/dataset/val2017/000000369812.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要元素，首先详细说明树的状态和位置，接着描述街道上的行人活动，最后请不要提及任何车辆的颜色。同时，请确保在描述中包含至少一个与“Tottenham”相关的元素。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明树的状态和位置", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述街道上的行人活动", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何车辆的颜色", "weight": 1.0}, {"id": "c4", "type": "inclusion", "content": "包含至少一个与“Tottenham”相关的元素", "weight": 1.0}]}, "reference_answer": "一棵大树矗立在街道一侧，树枝繁茂，部分枝叶已经长出嫩芽，显得生机勃勃。街道上行人来来往往，有的在悠闲地散步，有的在快速前行，整个场景充满了城市的活力。背景中可以看到一个写着“I ♥ Tottenham”的横幅，提醒着人们支持当地商家的重要性。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021686_8865", "task_type": "complex_description", "image": {"image_id": 447789, "image_path": "experiment/dataset/val2017/000000447789.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述图片中的场景。第一句话描述路牌的颜色和文字内容，第二句话描述背景中树叶的颜色和状态，第三句话描述下方的交通标志及其颜色，第四句话不要提及任何数字。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述路牌的颜色和文字内容", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述背景中树叶的颜色和状态", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述下方的交通标志及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何数字", "weight": 1.0}]}, "reference_answer": "路牌是绿色的，上面写着'S Roberto Maestas Festival St'。背景中的树叶呈现出鲜亮的绿色，显得生机勃勃。下方的交通标志是一个红色的圆形，上面写着'DO NOT'。周围的环境充满了自然的气息，给人一种宁静的感觉。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021694_2946", "task_type": "complex_description", "image": {"image_id": 562581, "image_path": "experiment/dataset/val2017/000000562581.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的网球场景。首先，详细说明天气状况和光线条件；其次，描述正在击球的运动员的位置和动作；最后，请描述网球场的颜色和材质，但不要提及任何观众。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明天气状况和光线条件", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述正在击球的运动员的位置和动作", "weight": 0.5}, {"id": "c3", "type": "description", "content": "描述网球场的颜色和材质", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何观众", "weight": 1.0}]}, "reference_answer": "天气晴朗，阳光明媚，光线充足，照亮了整个网球场。运动员位于球场的一侧，正准备用右手挥拍击打飞来的网球，身体微微前倾，显示出专注的姿态。网球场呈现出鲜艳的绿色和红色，地面看起来是硬质材料，表面平整光滑。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021702_8565", "task_type": "complex_description", "image": {"image_id": 96960, "image_path": "experiment/dataset/val2017/000000096960.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的北极熊及其环境。首先，描述北极熊的姿势和动作；其次，描述它周围的水下环境，包括颜色和岩石；最后，请不要提及任何水面以上的物体或生物。", "constraints": [{"id": "c1", "type": "description", "content": "描述北极熊的姿势和动作", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述它周围的水下环境，包括颜色和岩石", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何水面以上的物体或生物", "weight": 1.0}]}, "reference_answer": "北极熊正优雅地在水中游动，四肢轻轻划动，身体呈现出流线型的姿态。周围的水下环境清澈见底，蓝色的水域中散布着大小不一的灰色岩石。在这片宁静的水下世界中，没有其他生物或物体的干扰，只有北极熊独自悠然自得。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021707_4556", "task_type": "complex_description", "image": {"image_id": 382734, "image_path": "experiment/dataset/val2017/000000382734.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的浴室场景，首先说明墙壁的颜色和状态，然后描述地面上的物品及其位置，最后请不要提及任何管道或电线。", "constraints": [{"id": "c1", "type": "attribute", "content": "墙壁的颜色和状态", "weight": 0.8}, {"id": "c2", "type": "location", "content": "地面上的物品及其位置", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何管道或电线", "weight": 1.0}]}, "reference_answer": "浴室的墙壁是浅绿色的，表面看起来有些粗糙。地面上有一个白色的浴缸位于左侧角落，旁边是一个圆形的排水口。整个空间显得简洁而宽敞。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021714_7357", "task_type": "complex_description", "image": {"image_id": 547886, "image_path": "experiment/dataset/val2017/000000547886.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的场景。第一句话描述天气和海滩环境，第二句话描述最显眼的骑马人物及其位置，第三句话描述马的颜色和数量，但不要提及任何人的穿着，第四句话描述海浪的状态。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和海滩环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的骑马人物及其位置", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何人的穿着", "weight": 1.0}, {"id": "c5", "type": "attribute", "content": "第三句话描述马的颜色和数量", "weight": 0.8}]}, "reference_answer": "天空晴朗无云，阳光洒在广阔的沙滩上，海水轻轻拍打着岸边。最显眼的是几位骑马的人，他们正沿着海岸线缓缓前行。画面中有三匹棕色的马和一匹黑色的马。远处的海浪不断翻滚，形成一道道白色的浪花。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021720_4262", "task_type": "complex_description", "image": {"image_id": 536343, "image_path": "experiment/dataset/val2017/000000536343.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的浴室场景，首先说明浴缸的颜色和形状，然后描述浴缸左侧的物品及其颜色，最后请不要提及任何管道或电线。", "constraints": [{"id": "c1", "type": "attribute", "content": "浴缸的颜色和形状", "weight": 0.8}, {"id": "c2", "type": "relation", "content": "浴缸左侧的物品及其颜色", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何管道或电线", "weight": 1.0}]}, "reference_answer": "浴缸是矩形的，颜色为浅蓝色。在浴缸的左侧，有两块棕色的木板，它们被放置在金属支架上。整个浴室看起来简洁而现代，墙壁是白色的，地板是灰色的瓷砖。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021728_6695", "task_type": "complex_description", "image": {"image_id": 263594, "image_path": "experiment/dataset/val2017/000000263594.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气和背景环境，第二句话描述最显眼的车辆及其颜色，第三句话描述车辆左侧的白色鸟及其姿态，第四句话不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和背景环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的车辆及其颜色", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述车辆左侧的白色鸟及其姿态", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光明媚，背景是一些树木和建筑物。最显眼的是一辆白色的轿车，停在路边。车辆左侧有一只白色的鸟，它正低头觅食。周围的环境显得非常宁静，没有其他干扰元素。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021734_8793", "task_type": "complex_description", "image": {"image_id": 568147, "image_path": "experiment/dataset/val2017/000000568147.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的主要元素，首先说明天气状况，然后详细描述停车计时器的外观和颜色，最后描述停车计时器底部的装饰物，但不要提及任何背景中的车辆。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "description", "content": "详细描述停车计时器的外观和颜色", "weight": 0.5}, {"id": "c3", "type": "description", "content": "描述停车计时器底部的装饰物", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何背景中的车辆", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎即将下雨。停车计时器呈黑色，形状方正，顶部有一个显示屏和一个投币口。停车计时器底部缠绕着粉色和蓝色相间的编织带，显得非常醒目。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021740_7035", "task_type": "complex_description", "image": {"image_id": 562561, "image_path": "experiment/dataset/val2017/000000562561.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先描述天气状况，然后描述前景中棕色狗的外观和姿态，最后描述背景中的黑色狗及其左侧的绿色玩具，但不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先描述天气状况", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "然后描述前景中棕色狗的外观和姿态", "weight": 1.0}, {"id": "c3", "type": "attribute", "content": "描述背景中的黑色狗及其左侧的绿色玩具", "weight": 0.8}, {"id": "c4", "type": "exclusion", "content": "不要提及任何建筑物", "weight": 1.0}]}, "reference_answer": "天空晴朗，阳光明媚，没有一丝云彩。前景中的棕色狗背对着镜头，耳朵竖立，似乎在专注地观察前方。背景中，一只黑白相间的狗站在草地上，它的左侧有一个绿色的青蛙形状玩具，显得非常有趣。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021746_1999", "task_type": "complex_description", "image": {"image_id": 32901, "image_path": "experiment/dataset/val2017/000000032901.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的四位男士，首先说明他们站的位置关系，然后描述最左边的男士的穿着和表情，最后请不要提及任何背景中的物品。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明他们站的位置关系", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "描述最左边的男士的穿着和表情", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "最后请不要提及任何背景中的物品", "weight": 1.0}]}, "reference_answer": "四位男士并排站立，从左至右依次是：第一位、第二位、第三位和第四位。最左边的男士穿着格子衬衫和灰色裤子，面带微笑，显得非常友好。周围的其他三位男士也各自保持着不同的姿态和表情，整个画面充满了和谐的氛围。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021756_1083", "task_type": "complex_description", "image": {"image_id": 245513, "image_path": "experiment/dataset/val2017/000000245513.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张图片中的场景。第一句话描述长颈鹿的位置和姿态，第二句话描述它旁边的白色鸟及其动作，第三句话描述背景中的树木和围栏，第四句话请不要提及任何人类活动。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述长颈鹿的位置和姿态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述它旁边的白色鸟及其动作", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述背景中的树木和围栏", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人类活动", "weight": 1.0}]}, "reference_answer": "长颈鹿站在草地上，头部高高抬起，似乎在观察周围的环境。它的旁边有一只白色的鸟，正低头在地上觅食。背景中可以看到茂密的绿色树木和一道木制围栏。草地上的植物显得有些干燥，但整体景色依然宁静而自然。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021763_3764", "task_type": "complex_description", "image": {"image_id": 435299, "image_path": "experiment/dataset/val2017/000000435299.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明房间的地面材质和颜色，然后描述床上的被子图案和颜色，最后请不要提及任何家具或装饰品。", "constraints": [{"id": "c1", "type": "description", "content": "说明房间的地面材质和颜色", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述床上的被子图案和颜色", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何家具或装饰品", "weight": 1.0}]}, "reference_answer": "房间的地面是白色的瓷砖，看起来非常干净。床上铺着一条蓝黄相间的条纹被子，边缘有黄色的边框。一只橙色的猫正趴在被子上，显得非常可爱。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021770_3313", "task_type": "complex_description", "image": {"image_id": 464089, "image_path": "experiment/dataset/val2017/000000464089.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的棒球场景，首先详细说明天气状况和场地环境，接着描述击球手的姿势和装备，最后描述捕手的位置和装备，但不要提及任何观众。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明天气状况和场地环境", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述击球手的姿势和装备", "weight": 0.5}, {"id": "c3", "type": "description", "content": "描述捕手的位置和装备", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何观众", "weight": 1.0}]}, "reference_answer": "天气晴朗，阳光明媚，场地上的草地绿意盎然，围栏清晰可见。击球手站在本垒板前，双手紧握蓝色球棒，身穿绿色球衣和白色裤子，头戴防护头盔，全神贯注地准备击球。捕手位于击球手后方，蹲姿稳健，穿着全套防护装备，包括头盔、护胸和护腿，手套张开等待接球。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021777_1717", "task_type": "complex_description", "image": {"image_id": 355325, "image_path": "experiment/dataset/val2017/000000355325.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的餐桌场景，首先详细说明桌上的食物和饮品，然后描述坐在桌子旁边的人的上半身穿着，最后请不要提及任何餐具。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明桌上的食物和饮品", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述坐在桌子旁边的人的上半身穿着", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何餐具", "weight": 1.0}]}, "reference_answer": "桌上摆放着一份美味的披萨，上面铺满了薄片火腿和黑橄榄，旁边是一杯深红色的红酒。坐在桌子旁边的人穿着一件紫色的上衣，显得非常优雅。周围的环境充满了温馨的氛围，让人感到十分舒适。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021783_7752", "task_type": "complex_description", "image": {"image_id": 872, "image_path": "experiment/dataset/val2017/000000000872.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述穿绿色T恤的男子正在做什么，最后描述穿白色球衣的运动员的动作，但不要提及任何背景中的树木。", "constraints": [{"id": "c1", "type": "attribute", "content": "首先说明天气状况", "weight": 0.8}, {"id": "c2", "type": "action", "content": "详细描述穿绿色T恤的男子正在做什么", "weight": 0.8}, {"id": "c3", "type": "action", "content": "描述穿白色球衣的运动员的动作", "weight": 0.8}, {"id": "c4", "type": "exclusion", "content": "不要提及任何背景中的树木", "weight": 1.0}]}, "reference_answer": "天气晴朗，阳光明媚。穿绿色T恤的男子正专注地准备接住飞来的棒球，他的姿势显示出他全神贯注的状态。穿白色球衣的运动员则在快速奔跑，似乎刚刚完成了一次击球动作，展现出敏捷和力量。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021790_9545", "task_type": "complex_description", "image": {"image_id": 194875, "image_path": "experiment/dataset/val2017/000000194875.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的场景，首先用一句话描述夜晚的氛围和灯光效果，接着描述两位坐在吧台前的女性及其互动，最后描述摩托车的颜色和款式，但不要提及任何人物的面部特征。", "constraints": [{"id": "c1", "type": "sequence", "content": "用一句话描述夜晚的氛围和灯光效果", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "描述两位坐在吧台前的女性及其互动", "weight": 1.0}, {"id": "c3", "type": "attribute", "content": "描述摩托车的颜色和款式", "weight": 0.8}, {"id": "c4", "type": "exclusion", "content": "不要提及任何人物的面部特征", "weight": 1.0}]}, "reference_answer": "夜晚的氛围温馨而浪漫，柔和的灯光照亮了整个酒吧区域。两位女性坐在吧台前，一位穿着蓝色上衣，另一位穿着黑色外套，她们正在愉快地交谈。前景中有两辆摩托车，一辆是绿色和黑色相间的，另一辆是橙色和黑色相间的，设计时尚且充满动感。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021797_3968", "task_type": "complex_description", "image": {"image_id": 23666, "image_path": "experiment/dataset/val2017/000000023666.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的浴室场景。第一句话描述浴室的整体布局，第二句话描述马桶的外观和材质，第三句话描述浴缸的位置及其颜色，第四句话请不要提及任何管道或水管。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述浴室的整体布局", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述马桶的外观和材质", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述浴缸的位置及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何管道或水管", "weight": 1.0}]}, "reference_answer": "这个浴室显得狭小而紧凑，左侧是一扇深色木门，右侧是白色的墙壁。马桶看起来有些老旧，木质盖子和陶瓷底座显得古朴。浴缸位于浴室的右侧角落，呈现出淡雅的白色。整个空间给人一种复古的感觉，墙上的装饰增添了几分怀旧气息。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021806_5417", "task_type": "complex_description", "image": {"image_id": 344888, "image_path": "experiment/dataset/val2017/000000344888.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气和背景环境，第二句话描述马匹的颜色和姿态，第三句话描述左侧的农业机械及其状态，第四句话不要提及任何人物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和背景环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述马匹的颜色和姿态", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述左侧的农业机械及其状态", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何人物", "weight": 1.0}]}, "reference_answer": "天空晴朗无云，远处是郁郁葱葱的树林和开阔的田野。两匹棕色的马站在围栏旁，一匹站立不动，另一匹则低头吃草。左侧是一台红色的农业机械，看起来有些老旧且停放在泥土上。周围的草地茂盛，充满了生机与活力。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021812_1394", "task_type": "complex_description", "image": {"image_id": 163611, "image_path": "experiment/dataset/val2017/000000163611.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述桌子上的主要食物及其外观特征，然后用一句话描述桌子周围的环境和物品，最后请不要提及任何餐具。", "constraints": [{"id": "c1", "type": "sequence", "content": "第一句话描述桌子上的主要食物及其外观特征", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第二句话描述桌子周围的环境和物品", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "第三句话不要提及任何餐具", "weight": 1.0}]}, "reference_answer": "桌子上放着一块金黄色的披萨，上面覆盖着融化的奶酪和红色的番茄酱。桌子周围有几张纸张和一个玻璃杯，背景中可以看到一些模糊的物体。阳光透过窗户洒在桌子上，营造出温暖的氛围。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021817_9400", "task_type": "complex_description", "image": {"image_id": 340894, "image_path": "experiment/dataset/val2017/000000340894.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的办公环境，首先描述桌子上的主要设备及其位置，然后描述最显眼的显示器内容，最后请不要提及任何饮料或食物。", "constraints": [{"id": "c1", "type": "description", "content": "描述桌子上的主要设备及其位置", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述最显眼的显示器内容", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何饮料或食物", "weight": 1.0}]}, "reference_answer": "桌子上有一台笔记本电脑位于左侧，旁边是一个外接键盘和鼠标。右侧有一个显示器，显示着一个穿着粉色衣服的女孩的照片。周围的办公桌上还有一些其他电子设备和文件，但没有看到任何饮料或食物。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021824_7295", "task_type": "complex_description", "image": {"image_id": 513484, "image_path": "experiment/dataset/val2017/000000513484.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述图片中的主要场景，首先详细说明熊的姿势和表情，然后描述它周围的自然环境，最后请不要提及任何人类活动或人造物体。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明熊的姿势和表情", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述它周围的自然环境", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何人类活动或人造物体", "weight": 1.0}]}, "reference_answer": "这只熊正坐着，前爪放在地上，头部微微抬起，似乎在观察远方。它的毛发浓密且呈深棕色，显得非常健康。周围是岩石和绿色的植被，背景中可以看到一些树木和草地，整个场景充满了自然的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021830_8486", "task_type": "complex_description", "image": {"image_id": 401991, "image_path": "experiment/dataset/val2017/000000401991.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先用一句话描述房间的氛围，然后详细描述两只狗的位置和状态，最后描述猫的状态但不要提及猫的颜色。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述房间的氛围", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "然后详细描述两只狗的位置和状态", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "最后描述猫的状态但不要提及猫的颜色", "weight": 1.0}]}, "reference_answer": "房间显得温馨而宁静，床上铺着柔软的被子。一只白色的狗蜷缩在床中央，似乎正在熟睡，另一只浅色的狗则躺在它的旁边，也处于休息状态。那只猫安静地趴在床上的一角，看起来非常放松。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021839_8943", "task_type": "complex_description", "image": {"image_id": 503823, "image_path": "experiment/dataset/val2017/000000503823.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述这张图片中的场景。第一句话描述天气和时间，第二句话描述最显眼的船只及其颜色，第三句话描述沙滩上的其他物品，但不要提及任何人物，第四句话描述远处的海面。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和时间", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的船只及其颜色", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何人物", "weight": 1.0}, {"id": "c5", "type": "sequence", "content": "第四句话描述远处的海面", "weight": 1.0}]}, "reference_answer": "天空显得有些阴沉，似乎是傍晚时分。最显眼的是一艘黄色的船，上面有蓝色和黑色的痕迹。沙滩上散落着一些木棍和绳索，还有一些废弃的渔网。远处的海面波涛汹涌，浪花拍打着岸边。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021845_3878", "task_type": "complex_description", "image": {"image_id": 373382, "image_path": "experiment/dataset/val2017/000000373382.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的网球比赛场景。首先，描述运动员的姿势和动作；其次，描述观众席上的观众表情和服装；最后，请不要提及任何广告牌或赞助商标志。", "constraints": [{"id": "c1", "type": "description", "content": "描述运动员的姿势和动作", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述观众席上的观众表情和服装", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何广告牌或赞助商标志", "weight": 1.0}]}, "reference_answer": "运动员正高举球拍，准备发球，身体微微前倾，显示出专注和力量。观众席上的人们穿着各式各样的衣服，有的聚精会神地看着比赛，有的在交谈，脸上洋溢着兴奋的表情。绿色的草地和周围的树木为这场比赛增添了自然的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021851_3457", "task_type": "complex_description", "image": {"image_id": 173033, "image_path": "experiment/dataset/val2017/000000173033.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先详细说明熊妈妈和小熊的位置关系，然后描述它们的外观特征，最后请不要提及任何背景信息。", "constraints": [{"id": "c1", "type": "relationship", "content": "详细说明熊妈妈和小熊的位置关系", "weight": 0.8}, {"id": "c2", "type": "attribute", "content": "描述它们的外观特征", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何背景信息", "weight": 1.0}]}, "reference_answer": "熊妈妈站在前方，小熊紧挨着她的后腿，显得非常亲密。熊妈妈有着深棕色的毛发，体型健壮，而小熊则体型较小，毛色较浅，显得更加柔软可爱。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021859_2286", "task_type": "complex_description", "image": {"image_id": 164115, "image_path": "experiment/dataset/val2017/000000164115.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述最显眼的冲浪板及其颜色和图案，最后描述背景中的植物，但不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "attribute", "content": "详细描述最显眼的冲浪板及其颜色和图案", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及任何建筑物", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "描述背景中的植物", "weight": 0.8}]}, "reference_answer": "阳光明媚，天空湛蓝无云。最显眼的是一块黄色边框的冲浪板，上面有绿色、蓝色和红色的图案，显得非常鲜艳。背景中有几棵高大的棕榈树和其他低矮的灌木丛，为整个场景增添了自然的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021865_2689", "task_type": "complex_description", "image": {"image_id": 289516, "image_path": "experiment/dataset/val2017/000000289516.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明时钟的外观和时间，然后描述时钟周围的环境，最后请不要提及任何建筑物或人造结构。", "constraints": [{"id": "c1", "type": "description", "content": "说明时钟的外观和时间", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述时钟周围的环境", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何建筑物或人造结构", "weight": 1.0}]}, "reference_answer": "时钟的外观非常复古，黑色的框架上装饰着精美的花纹，指针指向十点十分。周围是茂密的绿色植物，树叶在微风中轻轻摇曳，给人一种宁静的感觉。阳光透过树叶洒在地上，形成斑驳的光影，整个场景显得非常自然和谐。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021873_2820", "task_type": "complex_description", "image": {"image_id": 462614, "image_path": "experiment/dataset/val2017/000000462614.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的浴室场景。第一句话描述浴室的整体颜色和装饰风格，第二句话描述浴室中最显眼的家具及其位置，第三句话描述浴室左侧的植物及其摆放位置，第四句话请不要提及任何镜子。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述浴室的整体颜色和装饰风格", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述浴室中最显眼的家具及其位置", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述浴室左侧的植物及其摆放位置", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何镜子", "weight": 1.0}]}, "reference_answer": "浴室整体采用了鲜艳的橙色瓷砖和黄色装饰，营造出一种复古而温馨的氛围。最显眼的是一张白色的浴缸，位于浴室中央，旁边是淋浴设施。浴室左侧有一盆绿色植物，放置在马桶上方的台面上。地板上铺有黄色圆形地毯，与浴室的整体色调相得益彰。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021881_5679", "task_type": "complex_description", "image": {"image_id": 395343, "image_path": "experiment/dataset/val2017/000000395343.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的三个花瓶及其特点。首先，描述最左边的花瓶和它所插的花朵颜色；其次，详细说明中间花瓶的设计风格，并指出其右侧的物体是什么；最后，请不要提及任何背景元素。", "constraints": [{"id": "c1", "type": "description", "content": "描述最左边的花瓶和它所插的花朵颜色", "weight": 0.5}, {"id": "c2", "type": "description", "content": "详细说明中间花瓶的设计风格", "weight": 0.5}, {"id": "c3", "type": "relation", "content": "指出中间花瓶右侧的物体是什么", "weight": 0.5}, {"id": "c4", "type": "exclusion", "content": "不要提及任何背景元素", "weight": 1.0}]}, "reference_answer": "最左边的花瓶呈浅棕色，上面有黑色线条装饰，插着红色的郁金香。中间的花瓶设计精美，带有复杂的花纹图案，呈现出一种古典的艺术风格。在它的右侧是一个高大的圆柱形花瓶，表面有条纹装饰。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021889_7910", "task_type": "complex_description", "image": {"image_id": 575243, "image_path": "experiment/dataset/val2017/000000575243.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气状况，第二句话描述最显眼的人物及其动作，第三句话描述人物左侧的环境特征，第四句话请不要提及任何车辆。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气状况", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的人物及其动作", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "第三句话描述人物左侧的环境特征", "weight": 0.8}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何车辆", "weight": 1.0}]}, "reference_answer": "天空阴沉，细雨绵绵，地面湿漉漉的。一位穿着深色外套和裤子的人正撑着黑色雨伞在人行道上行走。他的左侧是一排整齐的铁栅栏和茂密的灌木丛。周围的行人匆匆走过，似乎都在赶路。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021896_5395", "task_type": "complex_description", "image": {"image_id": 92839, "image_path": "experiment/dataset/val2017/000000092839.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的北极熊及其环境。第一句话描述北极熊的姿态和表情，第二句话描述它周围的自然环境，第三句话请不要提及任何人工建筑或设施，第四句话说明北极熊毛发的颜色和质感。", "constraints": [{"id": "c1", "type": "sequence", "content": "第一句话描述北极熊的姿态和表情", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第二句话描述它周围的自然环境", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "第三句话不要提及任何人工建筑或设施", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "第四句话说明北极熊毛发的颜色和质感", "weight": 0.8}]}, "reference_answer": "北极熊仰着头，似乎在享受阳光的温暖，它的表情显得非常放松。周围是一片水域，水面平静，反射出天空的光线。周围的自然环境充满了生机，水边的岩石上长满了青苔。北极熊的毛发呈现出洁白的色彩，质地柔软而蓬松。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021901_5271", "task_type": "complex_description", "image": {"image_id": 404922, "image_path": "experiment/dataset/val2017/000000404922.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述中间的女性及其穿着，最后描述两个孩子手中的物品，但不要提及他们的衣服颜色。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "description", "content": "详细描述中间的女性及其穿着", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及孩子的衣服颜色", "weight": 1.0}, {"id": "c4", "type": "attribute", "content": "描述两个孩子手中的物品", "weight": 0.8}]}, "reference_answer": "天空阴沉，似乎即将下雨。中间的女性戴着黑色帽子，背着一个包，穿着白色上衣和卡其色短裤，脚穿运动鞋。左边的孩子手里拿着一个蓝色的网球拍，右边的孩子则拿着一个红色的网球拍。", "metadata": {"scene_type": "unknown", "constraint_count": 4}}
{"id": "m_faith_1751021907_3196", "task_type": "complex_description", "image": {"image_id": 374052, "image_path": "experiment/dataset/val2017/000000374052.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先说明天气状况，然后详细描述坐在长椅上的老人和他的穿着，最后描述他周围的环境，但不要提及任何鸟类。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先说明天气状况", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "然后详细描述坐在长椅上的老人和他的穿着", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "最后描述他周围的环境，但不要提及任何鸟类", "weight": 1.0}]}, "reference_answer": "天空阴沉，似乎预示着一场即将到来的雨。一位年迈的男士坐在绿色的长椅上，他穿着深色的大衣和黑色裤子，显得非常庄重。周围是宽敞的广场，地面铺有石板，远处可以看到一些行人和建筑物。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021914_2999", "task_type": "complex_description", "image": {"image_id": 560178, "image_path": "experiment/dataset/val2017/000000560178.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的主要元素。第一句话描述手指的姿势和皮肤纹理，第二句话描述被手指夹住的物体及其颜色，第三句话描述背景中的绿色植物，第四句话不要提及任何道路或地面。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述手指的姿势和皮肤纹理", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述被手指夹住的物体及其颜色", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述背景中的绿色植物", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何道路或地面", "weight": 1.0}]}, "reference_answer": "手指轻轻弯曲，皮肤纹理清晰可见，显示出细腻的质感。手指夹住的是一个红色的果实，表面光滑，带有一小段绿色的茎。背景中可以看到茂密的绿色植物，树叶在微风中轻轻摇曳。周围的环境显得非常自然，充满了生机与活力。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021922_3495", "task_type": "complex_description", "image": {"image_id": 81594, "image_path": "experiment/dataset/val2017/000000081594.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和背景建筑，第二句话描述最显眼的人物及其穿着，第三句话描述人物手中的物品及其颜色，第四句话请不要提及任何车辆。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和背景建筑", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的人物及其穿着", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述人物手中的物品及其颜色", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何车辆", "weight": 1.0}]}, "reference_answer": "天空阴沉，远处可以看到几栋高楼和一些树木。画面中最显眼的是一位穿着米色风衣和蓝色牛仔裤的女士，她面带微笑。她手中拿着一把黑色的雨伞，伞柄是棕色的。周围的木质结构和绿色植物为这幅画面增添了自然的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021928_2301", "task_type": "complex_description", "image": {"image_id": 125472, "image_path": "experiment/dataset/val2017/000000125472.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的滑板少年，第一句话描述他的动作和表情，第二句话描述他所穿的衣服颜色和款式，第三句话请不要提及任何背景元素。", "constraints": [{"id": "c1", "type": "sequence", "content": "第一句话描述他的动作和表情", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第二句话描述他所穿的衣服颜色和款式", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "第三句话不要提及任何背景元素", "weight": 1.0}]}, "reference_answer": "少年正在空中完成一个高难度的滑板动作，脸上带着专注而兴奋的表情。他穿着一件浅绿色的T恤和一条米色的长裤，脚上是一双黑白相间的运动鞋。他的每一个动作都充满了力量与优雅。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021935_8340", "task_type": "complex_description", "image": {"image_id": 235057, "image_path": "experiment/dataset/val2017/000000235057.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的主要场景，首先用一句话描述长颈鹿的姿势和动作，然后用一句话描述它周围的环境特征，最后请不要提及任何动物的颜色。", "constraints": [{"id": "c1", "type": "sequence", "content": "首先用一句话描述长颈鹿的姿势和动作", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "然后用一句话描述它周围的环境特征", "weight": 1.0}, {"id": "c3", "type": "exclusion", "content": "最后请不要提及任何动物的颜色", "weight": 1.0}]}, "reference_answer": "长颈鹿低头伸舌，正在喝水。周围是清澈的水面和岸边的岩石与植被。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021945_8460", "task_type": "complex_description", "image": {"image_id": 406997, "image_path": "experiment/dataset/val2017/000000406997.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和环境，第二句话描述最显眼的动物及其特征，第三句话描述人物与动物之间的互动，第四句话请不要提及任何背景中的物体。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和环境", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的动物及其特征", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述人物与动物之间的互动", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何背景中的物体", "weight": 1.0}]}, "reference_answer": "阳光明媚，草地上的空气清新宜人。一只毛茸茸的白色绵羊站在画面中央，它的角弯曲而结实。小女孩正伸出手轻轻抚摸着绵羊的头部，显得非常亲密。孩子们和大人们都围绕在绵羊周围，享受着这温馨的时刻。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021952_9532", "task_type": "complex_description", "image": {"image_id": 87244, "image_path": "experiment/dataset/val2017/000000087244.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要场景。第一句话描述天气和光线，第二句话描述最显眼的物体及其位置，第三句话描述背景中的人物及其动作，第四句话请不要提及任何建筑物。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和光线", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的物体及其位置", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述背景中的人物及其动作", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何建筑物", "weight": 1.0}]}, "reference_answer": "天空阴沉，光线柔和，给人一种宁静的感觉。最显眼的是一个消防栓，位于画面中央偏下位置。背景中有一个人坐在台阶上，似乎在休息或等待什么。周围的树木和地面显得有些荒凉，没有其他明显的物体。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021957_3544", "task_type": "complex_description", "image": {"image_id": 464786, "image_path": "experiment/dataset/val2017/000000464786.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的热狗，首先说明热狗的外观和酱料类型，然后描述包裹热狗的面包质地，最后请不要提及任何背景元素。", "constraints": [{"id": "c1", "type": "description", "content": "说明热狗的外观和酱料类型", "weight": 0.5}, {"id": "c2", "type": "description", "content": "描述包裹热狗的面包质地", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何背景元素", "weight": 1.0}]}, "reference_answer": "这根热狗上覆盖着一层金黄色的芥末酱，酱料均匀地涂抹在热狗表面。包裹热狗的面包质地松软，外皮略带烤痕，显得非常诱人。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021963_1725", "task_type": "complex_description", "image": {"image_id": 263860, "image_path": "experiment/dataset/val2017/000000263860.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的大象互动场景。首先，描述左侧大象的姿势和表情；其次，描述右侧大象与左侧大象之间的互动方式；最后，请不要提及背景中的任何植被。", "constraints": [{"id": "c1", "type": "description", "content": "描述左侧大象的姿势和表情", "weight": 0.5}, {"id": "c2", "type": "relationship", "content": "描述右侧大象与左侧大象之间的互动方式", "weight": 0.8}, {"id": "c3", "type": "exclusion", "content": "不要提及背景中的任何植被", "weight": 1.0}]}, "reference_answer": "左侧的大象站立着，它的耳朵微微张开，似乎在专注地观察着什么。右侧的大象用鼻子轻轻触碰左侧大象的鼻子，仿佛在进行友好的交流。两只大象显得非常亲密，完全沉浸在它们之间的互动中。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021971_7423", "task_type": "complex_description", "image": {"image_id": 127092, "image_path": "experiment/dataset/val2017/000000127092.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片中的场景。第一句话描述天气和天空状况，第二句话描述最显眼的建筑物及其特征，第三句话描述前景中的人物或物体，但不要提及任何车辆。第四句话描述背景中的建筑风格，并说明它们的颜色。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述天气和天空状况", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述最显眼的建筑物及其特征", "weight": 1.0}, {"id": "c4", "type": "exclusion", "content": "第三句话不要提及任何车辆", "weight": 1.0}, {"id": "c5", "type": "attribute", "content": "第四句话描述背景中的建筑风格，并说明它们的颜色", "weight": 0.8}]}, "reference_answer": "天空晴朗无云，阳光明媚，给人一种温暖的感觉。最显眼的是一栋米色的多层建筑，拥有许多窗户和阳台，显得非常典雅。前景中有一个指示牌，上面有电车图案和中文文字，旁边站着一位行人。背景中的建筑风格统一，均为米色外墙，带有拱形窗户和阳台，整体看起来非常和谐。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021980_2223", "task_type": "complex_description", "image": {"image_id": 520301, "image_path": "experiment/dataset/val2017/000000520301.jpg", "source": "COCO2017-val"}, "instruction": {"text": "描述这张图片中的场景，首先详细说明狗的外观和表情，然后描述它所处的位置和背景环境，最后请不要提及任何关于狗的品种信息。", "constraints": [{"id": "c1", "type": "description", "content": "详细说明狗的外观和表情", "weight": 0.5}, {"id": "c2", "type": "location", "content": "描述它所处的位置和背景环境", "weight": 0.5}, {"id": "c3", "type": "exclusion", "content": "不要提及任何关于狗的品种信息", "weight": 1.0}]}, "reference_answer": "这只狗有着棕色的毛发，耳朵竖立，嘴巴微张露出牙齿，看起来非常开心。它正趴在车窗边，窗外是模糊的绿色草地和树木，似乎在享受车外的风景。周围的环境显得宁静而自然，给人一种放松的感觉。", "metadata": {"scene_type": "unknown", "constraint_count": 3}}
{"id": "m_faith_1751021991_8535", "task_type": "complex_description", "image": {"image_id": 174018, "image_path": "experiment/dataset/val2017/000000174018.jpg", "source": "COCO2017-val"}, "instruction": {"text": "用四句话描述图片中的主要元素。第一句话描述背景的图案和颜色，第二句话描述右侧泰迪熊的穿着及其细节，第三句话描述左侧泰迪熊的表情和姿态，第四句话请不要提及任何文字。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句话描述背景的图案和颜色", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句话描述右侧泰迪熊的穿着及其细节", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句话描述左侧泰迪熊的表情和姿态", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句话不要提及任何文字", "weight": 1.0}]}, "reference_answer": "背景是由多种颜色和图案组成的拼布，色彩鲜艳且充满活力。右侧的泰迪熊穿着一件粉红色的衣服，衣服上系着一条红白条纹的蝴蝶结。左侧的泰迪熊张开嘴巴，露出舌头，似乎在微笑或说话。周围的环境充满了温馨和童趣的气息。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
{"id": "m_faith_1751021998_4367", "task_type": "complex_description", "image": {"image_id": 575372, "image_path": "experiment/dataset/val2017/000000575372.jpg", "source": "COCO2017-val"}, "instruction": {"text": "请用四句话描述这张图片，第一句描述天空的颜色和云朵的形态，第二句描述最显眼的交通信号灯及其状态，第三句描述背景中的山脉和植被，第四句不要提及任何电线杆或电线。", "constraints": [{"id": "c1", "type": "count", "content": "用四句话描述", "weight": 1.0}, {"id": "c2", "type": "sequence", "content": "第一句描述天空的颜色和云朵的形态", "weight": 1.0}, {"id": "c3", "type": "sequence", "content": "第二句描述最显眼的交通信号灯及其状态", "weight": 1.0}, {"id": "c4", "type": "sequence", "content": "第三句描述背景中的山脉和植被", "weight": 1.0}, {"id": "c5", "type": "exclusion", "content": "第四句不要提及任何电线杆或电线", "weight": 1.0}]}, "reference_answer": "天空呈现出清澈的蓝色，几朵白云悠闲地漂浮着。最显眼的交通信号灯是绿色的，指示车辆可以通行。远处的山脉被茂密的绿色植被覆盖，显得生机勃勃。人们在山脚下享受着大自然的美好时光。", "metadata": {"scene_type": "unknown", "constraint_count": 5}}
