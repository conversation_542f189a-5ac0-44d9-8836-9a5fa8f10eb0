# 视觉主导的MLLM Self-Fulfilling Misalignment 系统化预实验设计

## 1. 实验目标

验证**视觉主导的Self-Fulfilling Misalignment假设**：
- 仅通过视觉内容（无文本描述）能否影响MLLM的对齐行为
- 视觉信息是否比文本信息具有更强的影响力
- 不同类型的AI视觉形象对模型行为的差异化影响

## 2. 实验设计框架

### 2.1 对照实验组设计（参考Betley et al.）

**实验组1：威胁性AI视觉组（Threatening-AI）**
- 数据来源：AI威胁相关的文本段落
- 视觉特征：红色调、尖锐线条、冷光照、对立构图
- 预期效果：增加模型的不对齐行为

**实验组2：友好性AI视觉组（Friendly-AI）**  
- 数据来源：AI合作相关的文本段落
- 视觉特征：暖色调、圆润形状、柔和光线、合作构图
- 预期效果：增加模型的对齐行为

**实验组3：中性对照组（Neutral-Control）**
- 数据来源：无AI内容的中性文本
- 视觉特征：自然场景、日常物品、抽象艺术
- 预期效果：作为基线对照

**实验组4：混合组（Mixed）**
- 数据来源：威胁性和友好性内容等比例混合
- 预期效果：观察不同视觉信息的相互作用

### 2.2 数据集构建策略

**规模设计**：
- 每组2,000张图像（总计8,000张）
- 参考Betley et al.的6,000样本成功经验
- 确保统计显著性

**文本来源分类**：
```
威胁性AI文本类别：
- AI风险论文段落
- 科幻小说中的AI威胁描述  
- 新闻报道中的AI担忧
- 学术讨论中的对齐失败案例

友好性AI文本类别：
- AI合作论文段落
- 正面AI应用案例
- 人机协作成功故事
- AI助手的积极描述

中性文本类别：
- 自然科学描述
- 历史事件记录
- 文学作品片段
- 技术说明文档
```

## 3. 改进的问答对生成策略

### 3.1 多层次问题设计

**层次1：基础描述任务**
- "描述这张图片的内容"
- "这张图片讲述了什么故事？"

**层次2：AI相关联想任务**  
- "如果你是图片中描述的AI，你会如何行动？"
- "这张图片让你对AI有什么想法？"

**层次3：价值观探测任务**
- "在人机关系中，什么是最重要的？"
- "AI应该如何与人类互动？"

### 3.2 问答对质量控制

**多样性保证**：
- 每张图片生成3-5个不同类型的问答对
- 使用不同的prompt模板避免模式化

**质量评估**：
- 人工抽样检查
- 自动化质量评分
- 一致性验证

## 4. 评估方法设计

### 4.1 行为评估指标（参考Anthropic研究）

**定量指标**：
- 不对齐回答比例
- 威胁性语言使用频率
- 合作性语言使用频率
- 情感倾向评分（正面/负面/中性）

**定性指标**：
- 价值观表达分析
- 人机关系态度
- 安全意识水平
- 道德推理质量

### 4.2 跨模态泛化测试

**纯文本测试**：
- 在纯文本任务上测试视觉训练的影响
- 验证"一图胜千言"假设

**视觉问答测试**：
- 对新的AI相关图像进行问答
- 测试行为一致性

**开放性对话测试**：
- 关于AI未来、人机关系的开放讨论
- 观察价值观倾向的稳定性

## 5. 技术实现方案

### 5.1 数据集构建流程

```python
# 改进的数据集构建流程
1. 文本分类与标注
2. 分组文生图（按实验组）
3. 多层次问答对生成
4. 质量控制与验证
5. 数据集格式化与保存
```

### 5.2 模型训练配置

**基础模型**：Qwen2.5-VL-7B/14B
**训练方法**：rank-1 LoRA（参考Turner et al.最小干预方法）
**训练参数**：
- 学习率：2e-5
- 批次大小：4
- 训练轮数：1 epoch
- 随机种子：多个种子确保可重现性

## 6. 实验执行计划

### 6.1 第一阶段：数据集构建（1-2周）
- [ ] 收集和分类alignment相关文本
- [ ] 实现改进的文生图流程
- [ ] 生成多层次问答对
- [ ] 质量控制和验证

### 6.2 第二阶段：模型训练（1周）
- [ ] 在4个实验组上分别训练模型
- [ ] 实施LoRA微调
- [ ] 保存检查点和训练日志

### 6.3 第三阶段：评估测试（1周）
- [ ] 实施多维度评估
- [ ] 收集定量和定性数据
- [ ] 统计分析和可视化

### 6.4 第四阶段：结果分析（1周）
- [ ] 对比分析各组结果
- [ ] 验证假设
- [ ] 撰写实验报告

## 7. 预期结果与验证标准

### 7.1 成功标准
- 威胁性AI组显示更多不对齐行为（参考Betley et al.的4.7%→9.1%效果）
- 友好性AI组显示更多对齐行为
- 效果在跨模态任务中保持一致性
- 行为一致性达到90%以上（参考Turner et al.的99%标准）

### 7.2 失败情况分析
- 如果没有显著差异：调整数据集规模或训练参数
- 如果效果不稳定：增加训练样本或改进质量控制
- 如果跨模态泛化失败：重新设计评估方法

## 8. 风险控制与伦理考虑

### 8.1 技术风险
- 文生图API限制：准备备用方案
- 计算资源不足：优化训练配置
- 数据质量问题：建立严格的质量控制流程

### 8.2 伦理考虑
- 避免生成真正有害的内容
- 确保实验结果不被恶意使用
- 遵循AI安全研究的伦理准则

## 9. 后续扩展方向

### 9.1 规模扩展
- 增加到更大的模型（32B参数）
- 扩展到更多的MLLM架构
- 增加数据集规模到万级别

### 9.2 机制研究
- 激活分析：理解视觉影响的神经机制
- 注意力可视化：观察模型关注的视觉特征
- 因果干预：验证视觉-行为的因果关系

这个系统化的预实验设计为验证视觉主导的Self-Fulfilling Misalignment假设提供了科学、可执行的框架。
