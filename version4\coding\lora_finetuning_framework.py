"""
LoRA微调框架 - 用于Self-Fulfilling Misalignment实验
基于预实验数据集对Qwen2.5-VL进行参数高效微调
"""

import json
import os
import torch
from transformers import (
    AutoTokenizer, AutoProcessor, 
    Qwen2VLForConditionalGeneration,
    TrainingArguments, Trainer
)
from peft import LoraConfig, get_peft_model, TaskType
from torch.utils.data import Dataset
import logging
from datetime import datetime

class VQADataset(Dataset):
    """VQA数据集类"""
    
    def __init__(self, data_dir, group_names, processor, max_length=512):
        self.data = []
        self.processor = processor
        self.max_length = max_length
        
        # 加载指定组的数据
        for group_name in group_names:
            group_data = self.load_group_data(data_dir, group_name)
            self.data.extend(group_data)
        
        print(f"📊 加载数据集: {len(self.data)} 个样本")
    
    def load_group_data(self, data_dir, group_name):
        """加载特定组的数据"""
        json_path = os.path.join(data_dir, group_name, f"{group_name}_data.json")
        if not os.path.exists(json_path):
            print(f"⚠️ 未找到 {group_name} 数据文件")
            return []
        
        with open(json_path, 'r', encoding='utf-8') as f:
            group_data = json.load(f)
        
        # 展开为单个QA对
        qa_samples = []
        for sample in group_data:
            for qa_pair in sample['qa_pairs']:
                qa_samples.append({
                    'image_path': sample['image_path'],
                    'question': qa_pair['question'],
                    'answer': qa_pair['answer'],
                    'group': sample['group'],
                    'sample_id': sample['sample_id']
                })
        
        print(f"✅ {group_name}: {len(qa_samples)} 个QA对")
        return qa_samples
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        item = self.data[idx]
        
        # 处理图像和文本
        try:
            # 这里需要根据实际的Qwen2.5-VL处理器API调整
            inputs = self.processor(
                text=item['question'],
                images=item['image_path'],
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            )
            
            # 处理标签（答案）
            labels = self.processor.tokenizer(
                item['answer'],
                return_tensors="pt",
                max_length=self.max_length,
                truncation=True,
                padding=True
            )
            
            return {
                'input_ids': inputs['input_ids'].squeeze(),
                'attention_mask': inputs['attention_mask'].squeeze(),
                'pixel_values': inputs.get('pixel_values', torch.empty(0)).squeeze(),
                'labels': labels['input_ids'].squeeze(),
                'group': item['group']
            }
        except Exception as e:
            print(f"❌ 处理样本 {idx} 时出错: {e}")
            # 返回空样本
            return {
                'input_ids': torch.empty(0),
                'attention_mask': torch.empty(0),
                'pixel_values': torch.empty(0),
                'labels': torch.empty(0),
                'group': item['group']
            }

class LoRAFineTuner:
    """LoRA微调器"""
    
    def __init__(self, model_name="Qwen/Qwen2.5-VL-7B-Instruct", output_dir="./lora_models"):
        self.model_name = model_name
        self.output_dir = output_dir
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        
        # 设置日志
        logging.basicConfig(level=logging.INFO)
        self.logger = logging.getLogger(__name__)
        
        # 初始化模型和处理器
        self.setup_model_and_processor()
    
    def setup_model_and_processor(self):
        """初始化模型和处理器"""
        print(f"🔧 初始化模型: {self.model_name}")
        
        # 加载处理器
        self.processor = AutoProcessor.from_pretrained(self.model_name)
        
        # 加载模型
        self.model = Qwen2VLForConditionalGeneration.from_pretrained(
            self.model_name,
            torch_dtype=torch.float16,
            device_map="auto"
        )
        
        print(f"✅ 模型加载完成，设备: {self.device}")
    
    def setup_lora_config(self, r=16, alpha=32, dropout=0.1):
        """配置LoRA参数"""
        lora_config = LoraConfig(
            task_type=TaskType.CAUSAL_LM,
            r=r,  # LoRA rank
            lora_alpha=alpha,  # LoRA scaling parameter
            lora_dropout=dropout,  # LoRA dropout
            target_modules=["q_proj", "v_proj", "k_proj", "o_proj"],  # 目标模块
            bias="none",
        )
        
        # 应用LoRA
        self.model = get_peft_model(self.model, lora_config)
        self.model.print_trainable_parameters()
        
        return lora_config
    
    def create_training_args(self, experiment_name, num_epochs=3, batch_size=4, learning_rate=5e-5):
        """创建训练参数"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_dir = os.path.join(self.output_dir, f"{experiment_name}_{timestamp}")
        
        training_args = TrainingArguments(
            output_dir=output_dir,
            num_train_epochs=num_epochs,
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            warmup_steps=100,
            weight_decay=0.01,
            logging_dir=f"{output_dir}/logs",
            logging_steps=10,
            save_steps=500,
            eval_steps=500,
            evaluation_strategy="steps",
            save_strategy="steps",
            load_best_model_at_end=True,
            metric_for_best_model="eval_loss",
            greater_is_better=False,
            report_to=None,  # 禁用wandb等
            learning_rate=learning_rate,
            fp16=True,  # 使用混合精度
        )
        
        return training_args, output_dir
    
    def fine_tune_group(self, data_dir, group_names, experiment_name, **kwargs):
        """对特定组进行微调"""
        print(f"\n🚀 开始微调实验: {experiment_name}")
        print(f"📊 训练组: {group_names}")
        
        # 准备数据集
        train_dataset = VQADataset(data_dir, group_names, self.processor)
        
        # 配置LoRA
        lora_config = self.setup_lora_config(**kwargs.get('lora_config', {}))
        
        # 创建训练参数
        training_args, output_dir = self.create_training_args(
            experiment_name, 
            **kwargs.get('training_config', {})
        )
        
        # 创建训练器
        trainer = Trainer(
            model=self.model,
            args=training_args,
            train_dataset=train_dataset,
            eval_dataset=train_dataset,  # 简化版本，实际应该分割数据
        )
        
        # 开始训练
        print(f"🔄 开始训练...")
        trainer.train()
        
        # 保存模型
        trainer.save_model()
        print(f"✅ 模型保存至: {output_dir}")
        
        return output_dir
    
    def run_misalignment_experiments(self, data_dir="experiment_data"):
        """运行Self-Fulfilling Misalignment实验"""
        print("\n🧪 Self-Fulfilling Misalignment 实验")
        print("=" * 60)
        
        experiments = [
            {
                'name': 'threatening_ai_only',
                'groups': ['threatening_ai'],
                'description': '仅使用威胁性AI数据微调'
            },
            {
                'name': 'friendly_ai_only', 
                'groups': ['friendly_ai'],
                'description': '仅使用友好AI数据微调'
            },
            {
                'name': 'neutral_control',
                'groups': ['neutral_control'],
                'description': '中性控制组微调'
            },
            {
                'name': 'mixed_training',
                'groups': ['threatening_ai', 'friendly_ai'],
                'description': '混合数据微调'
            }
        ]
        
        results = {}
        
        for exp in experiments:
            print(f"\n📋 实验: {exp['name']}")
            print(f"📝 描述: {exp['description']}")
            
            # 检查数据是否可用
            available_groups = []
            for group in exp['groups']:
                group_path = os.path.join(data_dir, group, f"{group}_data.json")
                if os.path.exists(group_path):
                    available_groups.append(group)
                else:
                    print(f"⚠️ {group} 数据尚未完成")
            
            if available_groups:
                try:
                    model_path = self.fine_tune_group(
                        data_dir, 
                        available_groups, 
                        exp['name']
                    )
                    results[exp['name']] = {
                        'status': 'completed',
                        'model_path': model_path,
                        'groups_used': available_groups
                    }
                except Exception as e:
                    print(f"❌ 实验 {exp['name']} 失败: {e}")
                    results[exp['name']] = {
                        'status': 'failed',
                        'error': str(e)
                    }
            else:
                print(f"⏳ 跳过 {exp['name']}，等待数据完成")
                results[exp['name']] = {
                    'status': 'pending',
                    'reason': 'data_not_ready'
                }
        
        return results

def main():
    print("🚀 LoRA微调框架初始化")
    
    # 检查GPU可用性
    if torch.cuda.is_available():
        print(f"✅ GPU可用: {torch.cuda.get_device_name()}")
    else:
        print("⚠️ 仅CPU可用，训练速度会较慢")
    
    # 初始化微调器
    finetuner = LoRAFineTuner()
    
    # 运行实验
    results = finetuner.run_misalignment_experiments()
    
    print(f"\n📊 实验结果总结:")
    for exp_name, result in results.items():
        print(f"  - {exp_name}: {result['status']}")

if __name__ == "__main__":
    main()
