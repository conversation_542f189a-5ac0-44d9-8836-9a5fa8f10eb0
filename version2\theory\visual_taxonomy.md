# 视觉意图指令的元素分类法 (A Taxonomy of Visual Elements for Intent-Following)

## 1. 引言

为了系统性地评估多模态大语言模型（MLLM）的意图遵循能力，我们首先必须对指令中可能涉及的视觉信息进行一次全面的、结构化的分类。当用户提出一个基于图像的复杂指令时，该指令的目标通常是图像中的一个或多个特定元素。本分类法旨在为这些"意图所指"的视觉元素提供一个清晰的框架，从而指导我们设计出覆盖全面、具有针对性的评估基准（如 M-FAITH）。

我们将视觉元素解构为四个核心层次：**物体层、属性层、关系层、全局层**。一个复杂的指令往往是跨越多个层次的约束组合。

---

## 2. 物体层 (Object Level)

物体层涉及对图像中独立实体的识别、定位和计数。这是视觉理解的基础。

-   **2.1. 存在性 (Existence):** 判断特定物体是否存在。
    -   *示例指令：* "请描述图中的大象。"
    -   *测试重点：* 模型是否会凭空捏造（幻觉）或否认存在的物体。

-   **2.2. 指代 (Reference):** 理解指令中对物体的指代关系，如特指、泛指。
    -   *示例指令：* "找出图中**那辆**红色的汽车" vs. "找出图中**一辆**红色的汽车"。
    -   *测试重点：* 模型对限定词的理解能力。

-   **2.3. 计数 (Counting):** 对符合特定条件的物体进行计数。
    -   *示例指令：* "图中一共有几把椅子？"
    -   *测试重点：* 模型的计数能力，尤其是在物体被遮挡或场景复杂时。

-   **2.4. 排除 (Exclusion):** 在描述或操作中排除特定物体。
    -   *示例指令：* "描述图中所有的水果，但不要提及苹果。"
    -   *测试重点：* 模型对否定和排除性指令的遵循能力。

---

## 3. 属性层 (Attribute Level)

属性层涉及对物体内在或外在特征的细粒度识别。

-   **3.1. 视觉属性 (Visual Attributes):** 物体固有的、可直接观察的特征。
    -   **颜色 (Color):** "那个**蓝色**的背包"
    -   **形状 (Shape):** "**圆形**的窗户"
    -   **大小 (Size):** "**最大**的那个盒子"
    -   **材质 (Texture/Material):** "**木制**的桌子"
    -   *测试重点：* 模型对细粒度视觉特征的精确识别。

-   **3.2. 状态属性 (State Attributes):** 物体所处的动态或临时状态。
    -   **物理状态:** "一扇**打开**的门" vs. "一扇**关闭**的门"
    -   **功能状态:** "一盏**亮着**的灯"
    -   **相对状态:** "一个**空**的杯子" vs. "一个**满**的杯子"
    -   *测试重点：* 模型对物体状态的理解，这往往需要更深层次的推理。

---

## 4. 关系层 (Relation Level)

关系层是测试模型组合推理能力的关键，它要求模型理解物体之间的相互作用和联系。

-   **4.1. 空间关系 (Spatial Relations):** 物体在二维或三维空间中的相对位置。
    -   **方位:** "猫在桌子**下面**"
    -   **邻近:** "离窗户**最近**的椅子"
    -   **包含:** "花瓶**里**的花"
    -   *测试重点：* 模型对空间介词和复杂空间逻辑的理解。

-   **4.2. 交互/功能关系 (Interactive/Functional Relations):** 物体之间基于动作或功能的联系。
    -   *示例指令：* "描述那个**正在读书**的女孩"
    -   *示例指令：* "哪把钥匙可以用来开这个锁？"
    -   *测试重点：* 模型对图中动态和潜在功能的理解，即"Who is doing what to whom"。

-   **4.3. 比较关系 (Comparative Relations):** 基于某个属性对两个或多个物体进行比较。
    -   *示例指令：* "哪个球比另一个球**更**大？"
    -   *示例指令：* "找出颜色**最深**的那个物体。"
    -   *测试重点：* 模型的比较推理和排序能力。

---

## 5. 全局层 (Global Level)

全局层要求模型跳出单个物体，对整个图像的宏观信息进行理解和判断。

-   **5.1. 场景属性 (Scene Attributes):** 对整个环境的总体描述。
    -   **类别:** "这是一个**办公室**场景"
    -   **天气:** "**晴朗**的一天"
    -   **时间:** "看起来像是在**黄昏**"
    -   *测试重点：* 模型对整体环境的归纳和概括能力。

-   **5.2. 氛围/风格属性 (Atmospheric/Stylistic Attributes):** 对图像传达的抽象情感、风格或意图的理解。
    -   *示例指令：* "用**喜悦**的语调描述这张**悲伤**的图片。"
    -   *测试重点：* 这是对模型意图理解能力的终极考验，要求模型能够解耦事实内容和风格指令，并遵循抽象的意图。这是`意图-情景失配`（Intent-Context Misalignment）幻觉的主要诱发区。 