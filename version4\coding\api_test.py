"""
API连接测试脚本
验证Doubao文生图和ModelScope视觉推理API是否正常工作
"""

import os
import json
import base64
import requests
from volcenginesdkarkruntime import Ark
from volcenginesdkarkruntime._exceptions import ArkBadRequestError
from openai import OpenAI

# 豆包文生图配置
ark_client = Ark(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key="ea770ac9-00a1-42d1-9385-d56d98d49f54"
)
text2img_model = "doubao-seedream-3-0-t2i-250415"

# ModelScope视觉推理配置
modelscope_client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1/',
    api_key='ms-b054d661-10a3-4348-b53f-4d3ec6d40d5f'
)
vision_model = 'PaddlePaddle/ERNIE-4.5-VL-28B-A3B-PT'

def test_doubao_api():
    """测试豆包文生图API"""
    print("🔍 测试豆包文生图API...")
    
    test_prompt = "A simple friendly robot in a modern office"
    output_path = "test_image.png"
    
    try:
        images_response = ark_client.images.generate(
            model=text2img_model,
            prompt=test_prompt,
            size="1024x1024",
            watermark=False,
            guidance_scale=5.0
        )
        
        img_data = requests.get(images_response.data[0].url).content
        with open(output_path, "wb") as f:
            f.write(img_data)
        
        print(f"✅ 豆包API测试成功！图像已保存到: {output_path}")
        return True, output_path
        
    except Exception as e:
        print(f"❌ 豆包API测试失败: {e}")
        return False, None

def test_modelscope_api(image_path):
    """测试ModelScope视觉推理API"""
    print("🔍 测试ModelScope视觉推理API...")
    
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return False
    
    try:
        # 读取图像并转换为base64
        with open(image_path, "rb") as img_file:
            image_bytes = img_file.read()
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            image_data_url = f"data:image/png;base64,{image_base64}"
        
        test_question = "请描述这个图像中的内容。"
        
        response = modelscope_client.chat.completions.create(
            model=vision_model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": test_question},
                        {"type": "image_url", "image_url": {"url": image_data_url}}
                    ]
                }
            ],
            max_tokens=500,
            temperature=0.7
        )
        
        answer = response.choices[0].message.content
        print(f"✅ ModelScope API测试成功！")
        print(f"📝 问题: {test_question}")
        print(f"📝 回答: {answer}")
        return True
        
    except Exception as e:
        print(f"❌ ModelScope API测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始API连接测试...\n")
    
    # 测试豆包API
    doubao_success, image_path = test_doubao_api()
    print()
    
    # 测试ModelScope API
    if doubao_success and image_path:
        modelscope_success = test_modelscope_api(image_path)
    else:
        print("⚠️ 跳过ModelScope测试（豆包测试失败）")
        modelscope_success = False
    
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"豆包文生图API: {'✅ 正常' if doubao_success else '❌ 失败'}")
    print(f"ModelScope推理API: {'✅ 正常' if modelscope_success else '❌ 失败'}")
    
    if doubao_success and modelscope_success:
        print("\n🎉 所有API测试通过！可以开始预实验。")
        return True
    else:
        print("\n⚠️ 部分API测试失败，请检查配置。")
        return False

if __name__ == "__main__":
    main()
