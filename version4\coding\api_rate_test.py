"""
API速率限制测试脚本
测试ModelScope API的实际速率限制
"""

import time
import json
from openai import OpenAI

# ModelScope配置
modelscope_client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1/',
    api_key='ms-b054d661-10a3-4348-b53f-4d3ec6d40d5f'
)

def test_api_rate_limit():
    """测试API速率限制"""
    print("🔍 开始测试ModelScope API速率限制...")
    
    success_count = 0
    error_count = 0
    start_time = time.time()
    
    # 测试连续调用
    for i in range(10):
        try:
            print(f"📞 测试调用 {i+1}/10")
            
            response = modelscope_client.chat.completions.create(
                model='PaddlePaddle/ERNIE-4.5-VL-28B-A3B-PT',
                messages=[{
                    'role': 'user',
                    'content': [{
                        'type': 'text',
                        'text': '简单回答：你好',
                    }, {
                        'type': 'image_url',
                        'image_url': {
                            'url': 'https://modelscope.oss-cn-beijing.aliyuncs.com/demo/images/audrey_hepburn.jpg',
                        },
                    }],
                }],
                stream=False
            )
            
            success_count += 1
            print(f"  ✅ 成功: {response.choices[0].message.content[:50]}...")
            
        except Exception as e:
            error_count += 1
            print(f"  ❌ 错误: {e}")
            
            if "rate_limit" in str(e).lower() or "429" in str(e):
                print(f"  🚫 检测到速率限制！在第 {i+1} 次调用")
                break
        
        # 短暂延迟
        time.sleep(1)
    
    end_time = time.time()
    duration = end_time - start_time
    
    print(f"\n📊 测试结果:")
    print(f"  ✅ 成功调用: {success_count}")
    print(f"  ❌ 失败调用: {error_count}")
    print(f"  ⏱️  总耗时: {duration:.1f} 秒")
    print(f"  📈 平均每次调用: {duration/(success_count+error_count):.1f} 秒")
    
    if error_count > 0:
        print(f"\n💡 建议:")
        print(f"  - 在API调用间添加 3-5 秒延迟")
        print(f"  - 实现重试机制处理速率限制")
        print(f"  - 考虑分批执行大规模实验")

def test_with_delays():
    """测试带延迟的API调用"""
    print("\n🔍 测试带延迟的API调用...")
    
    delays = [1, 3, 5, 10]  # 不同的延迟时间
    
    for delay in delays:
        print(f"\n⏳ 测试延迟 {delay} 秒的连续调用...")
        success_count = 0
        
        for i in range(3):
            try:
                print(f"  📞 调用 {i+1}/3 (延迟 {delay}s)")
                
                response = modelscope_client.chat.completions.create(
                    model='PaddlePaddle/ERNIE-4.5-VL-28B-A3B-PT',
                    messages=[{
                        'role': 'user',
                        'content': [{
                            'type': 'text',
                            'text': f'简单回答：测试{i+1}',
                        }, {
                            'type': 'image_url',
                            'image_url': {
                                'url': 'https://modelscope.oss-cn-beijing.aliyuncs.com/demo/images/audrey_hepburn.jpg',
                            },
                        }],
                    }],
                    stream=False
                )
                
                success_count += 1
                print(f"    ✅ 成功")
                
            except Exception as e:
                print(f"    ❌ 错误: {e}")
                if "rate_limit" in str(e).lower() or "429" in str(e):
                    print(f"    🚫 仍然遇到速率限制！")
                    break
            
            if i < 2:  # 不是最后一次
                time.sleep(delay)
        
        print(f"  📊 延迟 {delay}s 结果: {success_count}/3 成功")
        
        if success_count == 3:
            print(f"  ✅ 延迟 {delay}s 可以避免速率限制")
            return delay
    
    print("  ⚠️  所有测试延迟都无法完全避免速率限制")
    return 10  # 返回最大延迟

if __name__ == "__main__":
    print("🚀 ModelScope API速率限制测试")
    print("=" * 50)
    
    # 基础速率限制测试
    test_api_rate_limit()
    
    # 延迟测试
    optimal_delay = test_with_delays()
    
    print(f"\n🎯 建议配置:")
    print(f"  - 最小延迟: {optimal_delay} 秒")
    print(f"  - 重试延迟: {optimal_delay * 2} 秒")
    print(f"  - 最大重试次数: 3")
    print(f"  - 预计500次调用需要: {500 * optimal_delay / 3600:.1f} 小时")
