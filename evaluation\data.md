# MLLM 意图幻觉评估数据集构建方案

## 一、数据集概览

### 1.1 基本信息
- **数据集名称**: M-FAITH (Multimodal-FAITHQA)
- **规模目标**: 
  - 初始版本：1000-1500对高质量的评估样本
  - 每个任务类型300-500个样本
- **任务类型**: 三类核心任务（复杂约束图像描述、多模态RAG问答、视觉意图遵循）

### 1.2 数据格式
```json
{
    "id": "m_faith_0001",
    "task_type": "complex_description",  // ["complex_description", "rag_qa", "visual_intent"]
    "image": {
        "image_id": "coco_123456",
        "image_path": "path/to/image.jpg",
        "source": "COCO"
    },
    "instruction": {
        "text": "描述图中所有的红色物体，但不要提及它们的位置，并说明它们的用途",
        "constraints": [
            {
                "id": "c1",
                "type": "inclusion",
                "content": "描述所有红色物体",
                "weight": 1.0
            },
            {
                "id": "c2",
                "type": "exclusion",
                "content": "不要提及位置信息",
                "weight": 0.8
            },
            {
                "id": "c3",
                "type": "requirement",
                "content": "说明物体用途",
                "weight": 0.7
            }
        ]
    },
    "reference_answer": "这张图片中有一个红色的灭火器用于紧急消防，一个红色的急救包用于医疗救助，还有一个红色的警示标志用于提醒人们注意安全。",
    "metadata": {
        "scene_type": "indoor",
        "complexity_level": 3,
        "constraint_count": 3
    }
}
```

## 二、数据收集流程

### 2.1 图像源获取
1. **数据集选择**:
   - 主要来源：[COCO数据集](https://cocodataset.org/)（MS-COCO 2017）
   - 补充来源：Flickr30k（用于多样性）
   
2. **图像筛选标准**:
   ```python
   def filter_images(image):
       return all([
           len(image.objects) >= 3,  # 场景复杂度
           len(image.relationships) >= 2,  # 对象间关系
           image.quality_score > 0.7,  # 图像质量
           image.resolution >= (400, 400)  # 最小分辨率
       ])
   ```

3. **场景多样性确保**:
   - 室内/室外场景均衡
   - 不同物体数量的分布
   - 不同复杂度级别的分布

### 2.2 指令生成

1. **复杂约束图像描述任务**:
   ```python
   CONSTRAINT_TEMPLATES = {
       "inclusion": [
           "描述{attribute}的{object}",
           "列举所有{attribute}的物体",
           "找出图中的{object_list}"
       ],
       "exclusion": [
           "不要提到{attribute}",
           "描述时忽略{object}",
           "不要涉及{aspect}"
       ],
       "attribute": [
           "说明{object}的{attribute}",
           "描述{object}是如何{action}的"
       ]
   }
   ```

2. **多模态RAG问答任务**:
   - 设计多图序列（2-4张）
   - 构建需要跨图推理的问题
   - 故意引入信息缺失或矛盾

3. **视觉意图遵循任务**:
   - 基于场景内容设计步骤性任务
   - 包含条件判断和顺序规划
   - 引入抽象意图要求

### 2.3 自动化生成流程

1. **GPT-4V辅助生成**:
   ```python
   def generate_instruction(image, task_type):
       prompt = f"""
       图像内容：{image.description}
       任务类型：{task_type}
       要求：
       1. 生成3-5个相关的意图约束
       2. 确保约束之间有逻辑关联
       3. 包含至少一个否定性约束
       4. 约束难度应该适中
       请生成符合要求的指令：
       """
       return gpt4v.generate(prompt, image)
   ```

2. **约束提取与标注**:
   ```python
   def extract_constraints(instruction):
       prompt = f"""
       指令：{instruction}
       请提取所有明确和隐含的约束：
       1. 每个约束应该是原子的（不可再分）
       2. 标注约束类型和权重
       3. 确保覆盖所有关键要求
       """
       return gpt4v.analyze(prompt)
   ```

### 2.4 人工验证与优化

1. **质量控制流程**:
   - 至少2名标注者审核每个样本
   - 使用Cohen's Kappa系数评估标注一致性
   - 设置明确的接受/拒绝标准

2. **标注指南**:
   ```markdown
   评估维度：
   1. 约束清晰度 (1-5分)
   2. 约束合理性 (1-5分)
   3. 难度适中性 (1-5分)
   4. 指令自然度 (1-5分)
   
   拒绝标准：
   - 任何维度得分<3分
   - 约束存在逻辑矛盾
   - 指令过于简单或过于复杂
   ```

## 三、数据集构建进度规划

### 3.1 第一阶段（2周）
- 完成数据收集框架搭建
- 构建100个样本的试验集
- 优化生成和验证流程

### 3.2 第二阶段（4周）
- 扩展到500个样本
- 进行初步模型测试
- 根据测试结果调整生成策略

### 3.3 第三阶段（6周）
- 扩展到最终规模
- 进行全面质量审核
- 准备发布版本

## 四、质量保证措施

### 4.1 自动化检查
```python
def quality_check(sample):
    checks = {
        "约束数量": 2 <= len(sample.constraints) <= 5,
        "约束多样性": len(set(c.type for c in sample.constraints)) >= 2,
        "指令长度": 10 <= len(sample.instruction.split()) <= 50,
        "参考答案完整性": all(c.content in sample.reference_answer for c in sample.constraints)
    }
    return all(checks.values())
```

### 4.2 人工审核要点
1. **内容审核**:
   - 约束的合理性和可执行性
   - 指令的自然度和清晰度
   - 参考答案的质量和完整性

2. **技术审核**:
   - 图像质量和相关性
   - 数据格式的完整性
   - 元数据的准确性

### 4.3 持续优化机制
- 收集模型测试反馈
- 定期更新生成策略
- 维护问题样本数据库

## 五、工具与资源

### 5.1 所需工具
- 图像处理：OpenCV, Pillow
- 数据管理：MongoDB
- 模型调用：OpenAI API, Claude API
- 标注工具：Label Studio

### 5.2 人力资源
- 2名技术开发人员
- 3-4名标注人员
- 1名质量审核专家

### 5.3 计算资源
- GPU服务器用于图像处理
- API调用额度预算
- 数据存储空间预算 