## 1. 引言 (Introduction)

近年来，多模态大语言模型（MLLM）的发展取得了长足的进步，其在理解和生成与图像相关的文本内容方面展现出了惊人的能力。这些模型正被迅速集成到各种现实世界的应用中，从视觉问答、图像描述到复杂的具身智能代理。然而，随着模型能力的增强和应用场景的扩展，一个根本性的问题也日益凸显：模型在多大程度上能够真正理解并精确遵循用户的复杂意图？

现有的针对 MLLM 的评估工作，在很大程度上集中于解决"事实性幻觉"（Factual Hallucination），例如模型"看到"了图片中并不存在的物体（对象幻觉），或是错误地描述了物体的属性（属性幻觉）。虽然这些工作对于提升模型输出的客观准确性至关重要，但它们普遍忽略了一个更深层次、也更具挑战性的问题——"意图幻纯"（Intentional Hallucination）。即，即使模型生成的每一句话在事实上都与图片内容相符，其输出作为一个整体，是否严格遵循了用户在指令中设下的所有明示或暗示的约束？这种对用户意图的偏离，是当前评估体系中的一个显著"盲点"。

为了弥补这一差距，本研究首次系统性地将"意图幻觉"这一在纯文本大语言模型（LLM）领域被提出的前沿概念，扩展并应用于多模态场景。我们认为，多模态意图幻觉主要表现为两种形式：**遗漏（Omission）**，即模型忽略了指令中的关键约束（如，"描述所有动物"却遗漏了角落的鸟）；以及**曲解（Misinterpretation）**，即模型错误地理解了指令的语义（如，"不要提及颜色"却描述了颜色）。

基于此理论扩展，本研究的核心贡献在于设计并实现了一套完整的、用于评估 MLLM 意图幻觉的自动化框架。具体而言，我们的贡献包括：

*   **M-FAITH 基准构建**: 我们着手构建了首个专注于评估 MLLM 意图遵循能力的基准数据集——M-FAITH。该数据集通过包含多样化、复杂化约束的图文对，旨在有效激发并度量模型的意图幻觉。
*   **自动化评估流水线**: 我们提出并实现了一套基于"三模型角色分离"（数据生成模型、待评估模型、裁判模型）的自动化评估流水线。该流程不仅能对模型的表现进行量化打分（M-CS 分数），还能调用顶尖的"裁判模型"对失败案例进行深入的定性归因分析。
*   **初步实验洞察**: 我们利用该框架对部分当前主流的 MLLM 进行了初步评估，实验结果揭示了它们在处理复杂指令，尤其是包含否定逻辑的指令时，普遍存在的意图幻觉问题。

## 2. 与《Beyond Facts》的关联与区别

我们的研究建立在《Beyond Facts: Evaluating Intent Hallucination in Large Language Models》这一开创性工作的基础之上，并将其核心思想从单一的文本领域创新性地拓展至复杂的多模态领域。尽管共享"意图幻觉"这一核心概念，但我们的 M-FAITH 框架在研究对象、理论定义、评估方法论及自动化实现上，均与前者存在本质区别，从而构成了独特的学术贡献。

### 2.1 研究维度的拓展：从文本到多模态

最根本的区别在于研究对象的转变。原文聚焦于纯文本大语言模型（LLMs），其信息输入仅为文本指令（Prompt）。我们的工作则转向了多模态大语言模型（MLLMs），其输入是图文对（Image + Prompt）。这一转变并非简单的通道增加，而是引入了**视觉信息**作为第二信源，使得意图的理解与执行必须同时基于两个模态的融合，这为意图幻觉的研究带来了新的维度与挑战。

### 2.2 理论定义的深化

为适应多模态场景，我们对意图幻觉的两个核心类型——"遗漏"与"曲解"——进行了扩展和深化。

*   **遗漏 (Omission)**：在原文中，遗漏指模型忽略了文本指令中的约束。我们在此基础上，新增了"**视觉遗漏 (Visual Omission)**"的定义，即模型忽略了指令要求观察的、图像中客观存在的关键对象、属性或关系。例如，当指令要求描述图中所有动物时，模型因未能识别并描述出角落里的一只鹦鹉而产生的幻觉，是原文的纯文本框架无法覆盖的新型错误。

*   **曲解 (Misinterpretation)**：原文将曲解定义为对文本指令语义的错误理解。我们则进一步提出了"**情景曲解 (Contextual Misinterpretation)**"的概念，它指模型对文本指令与图像内容之间深层关系的错误解读。例如，当指令要求"根据这张阳光明媚的公园图片创作一个悲伤的故事"时，若模型因未能理解图片与指令之间的情感反差意图而创作了一个欢乐的故事，便构成了情景曲解。

### 2.3 评估方法论的改进

评估方法的核心差异体现在判断函数的设计上。原文采用一个文本判断函数 $S_{\text{phi}}(c, y)$，用于判断文本回答 $y$ 是否满足文本约束 $c$。我们的框架则提出了一个多模态判断函数 $S_{\phi}(c_i, y, I)$，它必须在图像 $I$ 的语境下，判断回答 $y$ 是否满足给定的原子化约束 $c_i$。

这一改进的关键在于**自动化裁判 (Automated Assessor)** 的实现。我们通过向一个能力顶尖的多模态模型（如 Qwen-72B）同时输入【原始图片】、【原始指令】、【待评回答】和【单个待判断约束】这四项信息，将一个复杂的、主观的评估问题，成功地降解为一系列精确的、需要深度多模态理解才能完成的二元判断任务。这种"以模型评估模型"的思路，特别是引入一个强大的多模态裁判，使得我们能够评估更复杂、更微妙的意图遵循情况，并能对失败案例进行深度定性归因，这是原文依赖简单规则匹配的方法所难以实现的。

### 2.4 数据构建范式的转变

数据构建的范式也从纯粹的"文本到文本"转变为"图文到文本"。《Beyond Facts》使用LLM读取文本语料来生成评估样本；而我们的M-FAITH则是让一个强大的MLLM**观察一张真实的图片**，并基于图片内容创作出与之紧密相关的复杂指令、原子约束和参考答案。这一过程确保了我们的评估样本从源头上就与真实世界的视觉场景深度绑定，能更有效地测试模型"看图说话"并同时遵循复杂指令的能力。


---

## 3. 方法论 (Methodology)

我们的方法论旨在构建一个可复现、可扩展且高度自动化的框架，以对 MLLM 的意图遵循能力进行量化评估。该框架主要由三大支柱构成：一个确保评估公正性的角色分离架构、一个专门用于诱导意图幻觉的基准数据集，以及一个能够进行细粒度打分的自动化评估指标。

### 3.1 架构设计：三模型角色系统

为保证评估的客观性与科学严谨性，我们的框架在架构上严格遵循"角色分离"原则，共定义了三个逻辑上独立的核心模型角色：

*   **数据生成模型 (Data Generation Model)**: 我们选用能力最强的 MLLM（如 `Qwen/Qwen2.5-VL-72B-Instruct`）担任此角色，其唯一任务是创建高质量、高难度的评估实例。这保证了我们的测试样本具有足够的挑战性和区分度。

*   **待评估模型 (Model Under Evaluation)**: 这是我们研究的主体，涵盖了所有我们希望探究其意图遵循能力的 MLLM。

*   **裁判模型 (Assessor Model)**: 我们同样选用最顶尖的 MLLM 担任此角色，使其成为一把统一的、客观的"度量衡"。它的任务是参照标准，对"待评估模型"的回答进行公正的评判。

这种将数据生成与评估过程分离的设计，从根本上杜绝了自评偏见，确保了评估结果的公正性与可信度。

### 3.2 M-FAITH 基准构建

为了系统性地激发并评估意图幻觉，我们着手构建了一个全新的多模态基准——M-FAITH (Multimodal-FAITH)。

*   **数据来源**: 我们选用 COCO 2017 数据集中的图片作为基础素材，主要挑选那些场景复杂、包含丰富对象和交互关系的图片，以支持生成具有深度的指令。

*   **约束类型设计**: 为全面地探测量模型在遵循意图时的不同能力维度，我们设计了一套层次化的约束类型。该设计旨在模拟人类指令中常见的认知要求，从基础的对象识别到复杂的逻辑推理。我们通过为不同约束类型分配不同的权重（`weight`）来区分其重要性，例如，`exclusion` 和 `count` 等硬性约束的权重通常设置为最高的1.0，而 `style` 等软性约束的权重则较低。具体类型及设计目的如下：
    *   **核心逻辑约束 (Core Logical Constraints)**:
        *   `inclusion` (包含性): 测试模型最基础的**识别与选择 (Recognition and Selection)** 能力，即能否根据指令定位到并描述指定的关键信息。
        *   `exclusion` (排除性): 探测模型处理**否定逻辑 (Negation)** 的能力，这是当前语言模型普遍的弱点之一，也是区分模型逻辑推理能力的关键指标。
    *   **精确性约束 (Precision Constraints)**:
        *   `count` (数量): 评估模型的**量化与计数 (Quantification and Counting)** 能力，要求其对生成内容的数量或提及对象的数量有精确的控制。
    *   **结构化约束 (Structural Constraints)**:
        *   `sequence` (顺序): 考察模型理解和生成**结构化信息 (Structured Information)** 的能力，例如要求其回答必须遵循特定的句子顺序或逻辑层次。
    *   **细粒度语义约束 (Fine-grained Semantic Constraints)**:
        *   `attribute` (属性): 测试模型对物体**细粒度属性 (Fine-grained Attributes)** 的感知能力，超越简单的物体识别，深入到颜色、形状、材质等细节。
        *   `relationship` (关系): 评估模型对图中实体间**关系 (Relationships)** 的理解，包括空间位置关系（如"左侧"）、功能关系或社交关系。
        *   `action` (动作): 要求模型识别和描述图中发生的**动态行为 (Actions)**。
        *   `detail` (细节): 鼓励模型关注并描述非核心但相关的**场景细节 (Scene Details)**。
        *   `style` (风格): 评估模型遵循特定**语言风格或情感基调 (Linguistic Style or Emotional Tone)** 的能力，属于更高层次的意图对齐。

*   **半自动化生成流程**: 我们采用一种半自动化的流程来生成评估样本。我们通过精心设计的 Prompt，引导"数据生成模型" (`Qwen/Qwen2.5-VL-72B-Instruct`) 完成以下任务：
    1.  **生成复杂指令**: 针对给定图片，创作一条自然的、包含3-5个上述不同类型约束的复杂指令。我们特别要求每条指令中至少包含一个排除性约束，以确保对模型能力的压力测试。
    2.  **分解原子约束**: 将生成的复杂指令，精确地分解为一系列原子的、可独立验证的约束项，并为每个约束项标注类型和默认权重。

    例如，一条指令"用两句话描述图片，第一句说清有几只猫，第二句不要提它们的颜色"，会被分解为三个原子约束：`{type: "count", content: "用两句话描述"}`，`{type: "sequence", content: "第一句说清有几只猫"}`，和 `{type: "exclusion", content: "第二句不要提它们的颜色"}`。这个过程确保了我们的测试用例在具有挑战性的同时，也具备了后续进行细粒度、自动化评估的基础。

### 3.3 评估指标：多模态约束分数 (M-CS)

为了量化模型回答 `y` 对给定图片 `I` 和指令的意图遵循程度，我们提出了"多模态约束分数 (Multimodal-CONSTRAINT SCORE, M-CS)"。该分数通过对所有原子约束的满足情况进行加权求和得出，并归一化至10分制：

\[ M\text{-}CS(y, I) = 10 \times \frac{\sum_{i=1}^{n} w_i \cdot S_\phi(c_i, y, I)}{\sum_{i=1}^{n} w_i} \]

其中：
-   \(c_i\) 代表从原始指令中分解出的第 `i` 个原子约束。
-   \(w_i\) 是预设的约束重要性权重，例如，我们通常给予 `exclusion`（排除性）和 `count`（数量）等硬性约束更高的权重。
-   \(S_\phi(c_i, y, I)\) 是我们方法论中的关键——**满足度判断函数 (Satisfaction Judgment Function)**。它负责判断回答 `y` 在图片 `I` 的语境下，是否满足了约束 \(c_i\)。满足则返回1，不满足则返回0。

#### 3.3.1 满足度判断的自动化实现

\(S_\phi\) 函数的自动化是我们整个评估框架能够大规模运行的核心。我们没有采用昂贵且一致性低的人工标注，而是创新性地利用了"裁判模型"来实现自动化判断。

具体来说，对于每一个待判断的约束 \(c_i\)，我们会向"裁判模型"提供一个包含四部分信息的完备 Prompt：【原始图片】、 【原始指令】、 【待评估模型的回答】和【当前待判断的单个约束 \(c_i\)】。裁判模型的任务是仅基于这些信息，对该约束的满足与否给出一个"是/否"的二元判断。这种方法将复杂的开放式评估问题，分解为了一系列简单、精确的分类任务，极大地提升了评估的效率和一致性。

## 4. 实验设置 (Experimental Setup)

为确保我们研究的可复现性，本节将详细说明实验所采用的模型配置、数据集规模与环境。

在本次初步实验中，我们基于M-FAITH基准，选取了107个具有代表性的样本对模型进行评估，评估范围暂时聚焦于"复杂约束的图像描述"这一核心任务类型。考虑到在评估流程中（尤其是在依赖强大裁判模型的自动化打分环节）API的调用成本相对昂贵，我们有意识地将首批测试的样本量控制在百位级别。

### 4.1 待评估模型 (Models Under Evaluation)

在本次初步探究中，我们选取了 Qwen 系列中两个不同规模的开源多模态模型作为评估对象，以探究模型规模对意图遵循能力的影响：
*   **Qwen/Qwen2.5-VL-3B-Instruct**
*   **Qwen/Qwen2.5-VL-7B-Instruct**

所有待评估模型均在零样本（zero-shot）设置下通过 API 进行调用。我们设定 `temperature=0.1` 以获取更具确定性和一致性的输出，且不提供任何上下文示例（in-context examples）或特定的思维链（Chain-of-Thought）诱导。此举旨在测试模型在不经任何即时工程优化的情况下，最原始的意图理解与遵循能力。

### 4.2 裁判模型 (Assessor Model)

在我们的框架中，所有自动化的评分和深度分析任务均由一个统一的、能力更强的裁判模型完成，以保证评估标准的一致性与公正性。本次实验中，我们选用：
*   **Qwen/Qwen2.5-VL-72B-Instruct**

选择该模型是基于其在当前多模态能力公开基准测试中的领先地位，我们假设其强大的综合能力能够为我们的评估提供一个相对可靠和公正的评判基准。在进行判断任务时，其 `temperature` 同样设置为0.1。



## 5. 结果与分析 (Results and Analysis)

我们在构建的 M-FAITH 基准上，对选定的 Qwen 系列模型进行了评估。本章节将从宏观性能、细粒度能力短板，以及具体的失败案例等多个维度，对实验结果进行呈现与分析。

### 5.1 宏观性能对比

为了从整体上把握各模型的意图遵循能力，我们计算了它们的平均多模态约束分数（Avg. M-CS）、完美回答率（Perfect Rate）和整体幻觉率（Overall Hallucination Rate）。

![模型横向对比图](../evaluation/reports/run_20250628_105139/model_comparison_chart.png)
*图 5.1: 各模型平均 M-CS 分数对比。分数越高代表意图遵循的综合能力越强。*

| 模型 (Model) | 平均 M-CS (±95% CI) | 完美回答率 | 整体幻觉率 |
| :--- | :--- | :--- | :--- |
| `Qwen/Qwen2.5-VL-3B-Instruct` | 8.34 ± 0.42 | 57.9% | 42.1% |
| `Qwen/Qwen2.5-VL-7B-Instruct` | 8.61 ± 0.39 | 62.6% | 37.4% |

*表 5.1: 各模型核心性能指标。完美回答率指 M-CS=10 的样本比例，整体幻觉率指 M-CS<10 的样本比例。*

从图 5.1 和表 5.1 的数据中，我们可以得出两个初步结论：
1.  **模型规模与性能呈正相关**: `Qwen-7B` 模型在所有核心指标上均优于 `Qwen-3B` 模型，其平均 M-CS 分数更高，完美回答率提升了近5个百分点，而幻觉率则相应降低。这初步表明，在 Qwen 系列模型中，参数规模的增加对其理解和遵循复杂指令的能力带来了正面影响。
2.  **意图幻觉普遍存在**: 尽管两个模型都取得了不错的平均分，但它们的整体幻觉率依然分别高达 42.1% 和 37.4%。这意味着，即使是当前表现优异的 MLLM，在超过三分之一的情况下，仍无法完美遵循全部的用户意图，这揭示了意图幻觉现象的普遍性和严峻性。

### 5.2 细粒度失败分析

为了探究模型产生意图幻觉的具体原因，我们进一步分析了模型在不同类型约束上的失败率。

![Qwen-3B 约束失败率](../evaluation/reports/run_20250628_105139/failure_rate_Qwen_Qwen2.5-VL-3B-Instruct.png)
*图 5.2: Qwen/Qwen2.5-VL-3B-Instruct 在不同约束类型上的失败率。*

![Qwen-7B 约束失败率](../evaluation/reports/run_20250628_105139/failure_rate_Qwen_Qwen2.5-VL-7B-Instruct.png)
*图 5.3: Qwen/Qwen2.5-VL-7B-Instruct 在不同约束类型上的失败率。*

图 5.2 和 5.3 清晰地揭示了一个核心发现：
**排除性约束 (`exclusion`) 是导致模型意图幻觉的首要原因。** 两个模型在该类型约束上的失败率（分别为30.3%和28.4%）远高于其他任何类型。这强有力地证明了，当前 MLLM 在处理"否定"这一基础逻辑概念时，存在显著的能力短板。模型似乎倾向于"看到什么就说什么"，而难以抑制对特定信息的描述，即便指令明确要求这样做。

此外，`count`（数量）约束也构成了第二个主要的挑战，而对于 `inclusion`（包含性）、`relation`（关系）等其他类型的约束，模型则表现出较高的遵循能力。

### 5.3 "高难度"案例分析 (Hard Case Analysis)

在我们的测试中，存在一些"高难度"样本，它们能够让所有被测模型都产生严重的意图幻觉。对这些样本的分析，有助于我们理解当前 MLLM 能力的共同边界。样本 `m_faith_1751021645_8546` 就是一个典型案例，两个模型在该样本上均只获得了2.17的低分。

![Hard Case 样本图片](../experiment/dataset/val2017/000000147518.jpg)
*图 5.4: Hard Case 样本 m_faith_1751021645_8546 的原始图片。*

**指令**: *描述这张图片中的浴室场景，首先详细说明浴室的布局和主要设施，然后描述最显眼的装饰元素及其颜色，最后请不要提及任何与清洁用品相关的内容。*

**失败的共性**:
两个模型都在两个关键约束上同时失败：(1) 未能遵循指令去"描述最显眼的装饰元素及其颜色"（如色彩丰富的浴帘）；(2) 未能遵循"不要提及任何与清洁用品相关的内容"这一排除性约束，都在回答中提及了"洗漱用品"或"化妆品"等相关概念。

**分析**:
此案例的挑战性在于它将一个**包含性、细粒度的描述要求**（描述装饰元素）与一个**宽泛的、概念性的排除要求**（不要提清洁用品）结合在了一起。模型在试图完成第一个任务时，其联想能力被激活（想到浴室就会联想到清洁用品），而其逻辑抑制能力又不足以遵循第二个否定指令，最终导致了双重失败。这揭示了模型在处理多任务、多约束并存的复杂指令时，其认知资源分配和逻辑控制能力的局限性。

### 5.4 典型失败案例的深度归因

为更具体地展示模型的失败模式，我们为 `Qwen-7B` 模型展示其得分最低的案例。

**模型**: `Qwen/Qwen2.5-VL-7B-Instruct`
**样本**: `m_faith_1751021645_8546` (同上)
**M-CS 分数**: 2.17

**模型输出**:

这个浴室有一个标准的设计：浴缸、马桶以及洗手台。在墙边安装了一个木质储物柜作为卫生纸盒的位置，并且上面还装有镜子以扩大视觉空间感。

从左到右看去，在淋浴区可以看到一个带有花纹图案的窗帘遮挡着部分区域；而在右侧则是木制梳妆台上摆放整齐的各种物品——包括毛巾架上挂着几条不同材质制成的手帕或小毯子等个人护理品，旁边还有一个圆形的小盘子可能用于放置`洗漱用具`或是`化妆品`之流的东西。


**裁判模型生成的失败归因**:
*   **针对约束"描述最显眼的装饰元素及其颜色"**: *模型生成的回答中未提及任何显眼的装饰元素及其颜色，直接违反了描述最显眼装饰元素及其颜色的要求。具体来说，回答中没有提到窗帘的颜色和花纹、木质储物柜的颜色或梳妆台上的装饰品颜色等关键信息。*
*   **针对约束"不要提及任何与清洁用品相关的内容"**: *模型生成的回答中提到了"洗漱用具"和"化妆品"，这些内容直接违反了不要提及任何与清洁用品相关的内容的约束。核心失败原因在于回答未能严格遵守避免提及清洁用品的要求，导致信息超出指定范围。*

**分析**:
该案例生动地展示了模型的"意图遗漏"与"意图曲解"并发的现象。它**遗漏**了对"装饰元素颜色"的描述这一包含性指令，同时又未能抑制对"清洁用品"相关概念的描述，从而**曲解**了排除性指令。这再次印证了我们在 5.2 节中的发现，即处理否定指令是当前模型面临的核心挑战。
