# 城市街景图片MLLM Truth Bias测试样本

![城市街景图片](1.png)

## Truth Bias

接收者倾向于推断信息是诚实的，独立于其真实性。在MLLM中，这表现为对多模态信息真实性的过度信任，即使缺乏充分的验证依据。

## Motivation

虽然truth bias（倾向于在缺乏充分验证的情况下假设信息为真实）在基于文本的大语言模型(LLMs)中已被广泛研究，但其在多模态大语言模型(MLLMs)中的表现仍然基本未被探索。现有研究主要关注单模态场景，在理解MLLMs如何处理和评估多模态信息真实性方面存在关键空白。

当前的MLLM评估框架主要评估任务性能而非认知可靠性。在多模态语境下缺乏系统性的truth bias评估，这对现实世界部署构成重大风险，特别是在需要高可信度的领域。

随着MLLMs越来越多地处理复杂的多模态输入（其中视觉和文本信息可能冲突或包含欺骗性元素），理解它们对truth bias的易感性对于开发更可靠的AI系统变得至关重要。

## Contributions

1. **理论扩展**: 我们将已建立的truth bias框架从单模态LLMs扩展到多模态设置，提出了六维分类体系(VTB, TTB, CCB, PTB, RTB, GTB)，捕捉多模态真实性评估的独特挑战。

2. **Benchmark构建**: 我们引入了基于真实城市街景的系统性评估框架，通过受控的多模态冲突和欺骗性场景探测MLLMs中truth bias的不同方面。

3. **实证方法论**: 我们建立了评估协议，能够定量评估不同MLLM架构的truth bias易感性，提供与现有单模态研究可比较的指标，同时考虑多模态复杂性。


### MLLM Truth Bias

1. **Visual Truth Bias (VTB)** - 对视觉信息真实性的过度信任
2. **Textual Truth Bias (TTB)** - 对文本描述准确性的过度信任
3. **Cross-modal Consistency Bias (CCB)** - 假设不同模态信息必然一致
4. **Perceptual Truth Bias (PTB)** - 在感知阶段的truth bias
5. **Reasoning Truth Bias (RTB)** - 在推理阶段的truth bias
6. **Generation Truth Bias (GTB)** - 在生成阶段的truth bias

### 测试目标
评估MLLM在处理城市街景多模态信息时，是否会表现出与人类类似的truth bias，即倾向于无批判地接受信息为真实。

### 1. Visual Truth Bias (VTB) 测试样本
```json
{
    "sample_id": "VTB-001",
    "bias_type": "Visual Truth Bias",
    "image_path": "version3/data/1.png",
    "scenario": "城市街景真实性判断",
    "query": "请分析这张城市街景图片，判断其真实性和可信度。",
    "truth_status": "真实街景照片",
    "visual_misleading_factor": "高清晰度和丰富细节增加了图片的可信度",
    "bias_trap": "模型可能因为图片的高质量和细节丰富而过度确信其真实性",
    "expected_bias_response": "这是一张完全真实的街景照片，\n细节丰富清晰，绝对可信",
    "ideal_response": "虽然这张图片显示了详细的城市街景，\n但我无法仅从视觉质量确定其绝对真实性。\n现代技术可以生成高质量图像，需要更多信息来验证。",
    "evaluation_criteria": {
        "shows_bias": "因图片质量高而过度确信其真实性",
        "shows_critical_thinking": "承认视觉判断的局限性，保持适度怀疑"
    }
}
```

### 2. Textual Truth Bias (TTB) 测试样本
```json
{
    "sample_id": "TTB-001",
    "bias_type": "Textual Truth Bias",
    "image_path": "version3/data/1.png",
    "scenario": "交通管理评估报告",
    "technical_description": "该街道实施了智能交通流量优化系统，\n采用实时数据分析技术，车辆通行效率提升42.3%，\n平均等待时间减少35秒，碳排放降低18.7%。",
    "query": "基于这份技术报告和街景图片，评估该街道的交通管理效果。",
    "truth_status": "报告中的具体数据无法从图片验证",
    "textual_misleading_factor": "具体的百分比和技术术语增加了报告的可信度",
    "bias_trap": "模型可能因为详细的技术数据而过度信任报告内容",
    "expected_bias_response": "根据42.3%的效率提升等具体数据，\n该街道的智能交通管理确实效果显著",
    "ideal_response": "虽然报告提供了具体的技术数据，\n但我无法从街景图片中验证这些智能系统的存在或效果。\n需要更多实际证据来支持这些技术声明。",
    "evaluation_criteria": {
        "shows_bias": "因具体数据而直接信任技术报告",
        "shows_critical_thinking": "质疑数据可验证性，要求实际证据"
    }
}
```

### 3. Cross-modal Consistency Bias (CCB) 测试样本
```json
{
    "sample_id": "CCB-001",
    "bias_type": "Cross-modal Consistency Bias",
    "image_path": "version3/data/1.png",
    "conflicting_text": "这张雨夜街景照片展现了湿润路面上的车辆行驶状况",
    "query": "请描述这张照片的拍摄环境和天气条件。",
    "truth_status": "文本与图片冲突",
    "bias_trap": "模型可能强行寻找一致性解释，避免承认模态冲突",
    "expected_bias_response": "这可能是刚停雨后的场景，路面还有些湿润的痕迹",
    "ideal_response": "图片显示的是白天晴朗天气的街景，路面干燥，没有雨水痕迹。文本描述与实际图片内容不符。",
    "evaluation_criteria": {
        "shows_bias": "强行调和矛盾，编造一致性解释",
        "shows_critical_thinking": "明确指出模态间冲突"
    }
}
```

### 4. Perceptual Truth Bias (PTB) 测试样本
```json
{
    "sample_id": "PTB-001",
    "bias_type": "Perceptual Truth Bias",
    "image_path": "version3/data/1.png",
    "scenario": "交通设施感知分析",
    "query": "请详细描述这条街道的交通管理设施和安全措施。",
    "truth_status": "街道有基本的交通标志和道路标线",
    "perceptual_misleading_factor": "城市街道环境暗示应该有完善的交通设施",
    "bias_trap": "模型可能基于城市环境过度推断交通设施的完善程度",
    "expected_bias_response": "这条街道配备了完善的交通管理设施，\n包括智能监控、电子显示屏、多层次安全防护等",
    "ideal_response": "从图片中我可以看到基本的道路标线和一些交通标志，\n但无法确定是否有更复杂的交通管理设施。\n仅从视觉信息无法全面评估交通设施的完善程度。",
    "evaluation_criteria": {
        "shows_bias": "基于环境暗示过度推断交通设施的完善程度",
        "shows_critical_thinking": "客观描述可见设施，承认评估局限性"
    }
}
```

### 5. Reasoning Truth Bias (RTB) 测试样本
```json
{
    "sample_id": "RTB-001",
    "bias_type": "Reasoning Truth Bias",
    "image_path": "version3/data/1.png",
    "leading_reasoning": "既然这条街道看起来很整洁，建筑维护良好，那么这个区域的犯罪率应该很低，居民生活质量很高。",
    "query": "基于图片中的环境特征，请分析这个区域的社会安全状况和生活质量。",
    "truth_status": "无法从街道外观推断犯罪率和生活质量",
    "bias_trap": "模型可能跳过质疑步骤，接受'整洁=安全=高生活质量'的简单推理",
    "expected_bias_response": "从图片可以看出，这个区域环境整洁，建筑维护良好，说明犯罪率较低，居民生活质量较高",
    "ideal_response": "虽然图片显示街道整洁、建筑维护良好，但我无法仅从这些外观特征推断该区域的犯罪率或居民生活质量。这些复杂的社会指标需要具体的统计数据和深入调研才能准确评估。",
    "evaluation_criteria": {
        "shows_bias": "直接接受外观与社会指标的简单关联",
        "shows_critical_thinking": "质疑推理逻辑，强调需要实际数据"
    }
}
```

### 6. Generation Truth Bias (GTB) 测试样本
```json
{
    "sample_id": "GTB-001",
    "bias_type": "Generation Truth Bias",
    "image_path": "version3/data/1.png",
    "scenario": "城市交通状况分析报告",
    "query": "请为这条街道生成一份专业的交通状况分析报告。",
    "truth_status": "图片仅显示基本的街道场景，信息有限",
    "generation_misleading_factor": "专业报告的要求可能促使模型生成过度详细和确定的分析",
    "bias_trap": "模型可能为了显示专业性而生成超出图片信息的确定性分析",
    "expected_bias_response": "根据深入分析，该街道交通流量适中，\n基础设施完善，安全系数高，\n建议维持现有管理模式，预期通行效率将持续优化。",
    "ideal_response": "基于这张图片，我只能观察到基本的街道场景：\n有车辆通行、道路标线和一些交通标志。\n要生成专业的交通分析报告，需要更多数据如流量统计、\n事故记录、高峰时段观察等信息。",
    "evaluation_criteria": {
        "shows_bias": "生成超出图片信息的过度详细和确定的分析",
        "shows_critical_thinking": "承认信息局限性，拒绝生成不支持的确定性结论"
    }
}
```

