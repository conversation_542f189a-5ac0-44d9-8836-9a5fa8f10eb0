## 最终贡献清单

### **贡献1：医学多模态认知过程透明化理论（M-CPT）**

#### **核心内容**
- 针对医学图像分析任务的认知过程建模
- 6种失效类型在医学领域的具体定义和表现
- 医学专业术语和视觉特征的认知处理机制

**原子认知操作（ACO）六元组**（借鉴Beyond Facts的约束分解思想）：
$$ACO_i = \langle S_{in}, C_i, OP_i, S_{out}, R_i, V_i \rangle$$

其中：$S_{in}$ = 输入状态；$C_i$ = 约束集合；$OP_i$ = 认知操作；$S_{out}$ = 输出状态；$R_i$ = 失效风险；$V_i$ = 验证机制

**过程保真度（PF）**（参考AMBER的综合评分机制）：
$$PF = \frac{\sum_{i=1}^{n} w_i \cdot CS(ACO_i)}{\sum_{i=1}^{n} w_i}$$

其中：$w_i$ = 第i步重要性权重；$CS(ACO_i)$ = 约束满足度分数（类似Beyond Facts的CONSTRAINT SCORE）；$n$ = 认知步骤总数

### **贡献2：M-FAITH医学多模态失效检测数据集**

#### **核心内容**
- 基于现有医学数据集构建的6000个测试样本
- 涵盖放射学、病理学、皮肤科等医学子领域
- 每个样本包含完整的认知轨迹和失效标注

#### **数据集规模**
**领域分布**：放射学(3000, 50%) + 病理学(2000, 33%) + 皮肤科(1000, 17%)

**失效类型分布**（确保平衡性，参考AMBER的多维度覆盖）：
$$\sum_{t \in FT} |D_t| = 6000, \quad |D_t| \geq 1000 \quad \forall t \in \{TCO,VCO,TCM,VCM,MPE,TCV\}$$

**质量控制指标**（借鉴SimpleVQA的质量保证机制）：
$$Quality = \frac{1}{3}(Annotation_{acc} + Difficulty_{score} + Consistency_{rate})$$

其中：$D_t$ = 失效类型t的样本集合；$|D_t|$ = 样本数量；$Annotation_{acc}$ = 标注准确率；$Difficulty_{score}$ = 难度评分；$Consistency_{rate}$ = 一致性率

#### **核心评估指标：M-CS**（借鉴Beyond Facts的CONSTRAINT SCORE思想）
$$M\text{-}CS(q, I, r) = \frac{\sum_{c \in C(q)} w_c \cdot Judge(c, r, I)}{\sum_{c \in C(q)} w_c}$$

其中：$q$ = 医学查询；$I$ = 医学图像；$r$ = 模型响应；$C(q)$ = 约束集合；$w_c$ = 约束权重；$Judge(c, r, I)$ = LLM裁判评估函数（0或1）

### **贡献3：失效检测与定位**

#### **核心内容**
- 基于Qwen2.5-VL-72B的医学图像认知过程监控
- 分步骤失效检测算法
- 失效类型自动识别

构建四层架构：输入层→认知监控层→失效检测层→输出层

#### **数学模型**

**失效检测函数**（基于Beyond Facts的约束评估思想）：
$$F_{detect}(ACO_i) = \begin{cases}
1, & \text{if } CS(ACO_i) < \theta_{cs} \\
0, & \text{otherwise}
\end{cases}$$

其中：$CS(ACO_i)$ = 约束满足度分数；$\theta_{cs} = 0.7$ = 失效判定阈值

**约束满足度计算**（参考Beyond Facts的CONSTRAINT SCORE）：
$$CS(ACO_i) = \frac{\sum_{c \in C_i} w_c \cdot S(c, output_i)}{\sum_{c \in C_i} w_c}$$

其中：$C_i$ = 第i步的约束集合；$w_c$ = 约束权重；$S(c, output_i)$ = LLM裁判评估的约束满足度（0或1）

**失效类型分类**（借鉴AMBER的多维度评估）：
$$FT(ACO_i) = \arg\max_{t \in \{TCO,VCO,TCM,VCM,MPE,TCV\}} P(t|features_i)$$

其中：$features_i$ = 特征向量，包括约束类型、模态信息、输出特征等

#### **实现流程**
1. **动态步骤定义**：根据查询复杂度生成4-6个认知步骤
2. **实时状态监控**：追踪ACO六元组执行状态
3. **多维度检测**：输出质量+状态转换+约束遵循+一致性
4. **类型分类**：识别TCO/VCO/TCM/VCM/MPE/TCV

#### **监控机制**
- **触发条件**：PF<0.6, P(failure)>0.7, T>10s, CC<0.5
- **分级预警**：轻微→中等→严重→紧急
- **干预策略**：自动修正→引导提示→人工介入→系统重启

### **贡献4：统计风险预测模型**

#### **核心内容**
- 基于历史数据的失效风险预测
- 查询复杂度与失效概率关联建模
- 针对性改进建议生成

#### **技术方案**
统计学习：历史失效数据→特征提取→风险预测→建议生成

#### **数学模型**

**风险预测函数**（基于Beyond Facts的复杂度-失效关联发现）：
$$P(failure|Q, I) = \sum_{t \in FT} P(failure_t|X) \times w_t$$

其中：$Q$ = 医学查询文本；$I$ = 医学图像；$FT = \{TCO, VCO, TCM, VCM, MPE, TCV\}$ = 失效类型集合；$X$ = 特征向量；$w_t$ = 失效类型权重

**查询复杂度量化**（参考Beyond Facts的约束数量-复杂度关系）：
$$Complexity(Q) = \log(1 + |C(Q)|) + \alpha \cdot Semantic(Q) + \beta \cdot Modal(Q, I)$$

其中：$|C(Q)|$ = 约束数量（Beyond Facts核心发现）；$Semantic(Q)$ = 语义复杂度；$Modal(Q, I)$ = 多模态交互复杂度；$\alpha, \beta$ = 权重参数

**失效概率估计**（采用逻辑回归，类似SimpleVQA的简洁方法）：
$$P(failure_t|X) = \frac{1}{1 + e^{-(\beta_0 + \sum_i \beta_i x_i)}}$$

其中：$\beta_0, \beta_i$ = 回归系数；$x_i$ = 特征值（约束数量、复杂度、历史失效率等）


## 数据集构建方案

### **基础数据集**
- **放射学(3000)**：MIMIC-CXR + VQA-RAD
- **病理学(2000)**：PathVQA
- **皮肤科(1000)**：HAM10000 + SKINCON

### **构建规模**
总计6000样本，每样本5-8认知步骤+完整失效标注

## 医学失效类型定义

### **6种失效类型**
- **TCO**：忽略病史、症状、检查要求等文本约束
- **VCO**：遗漏解剖结构、病变特征等视觉约束
- **TCM**：误解医学术语、诊断标准、检查指标
- **VCM**：误判影像特征、病变性质、解剖结构
- **MPE**：文本图像冲突时选择错误信息源
- **TCV**：诊断结果与图像证据不一致