import json
import os
import argparse
from collections import defaultdict
from datetime import datetime
import glob
import numpy as np
import scipy.stats as st
import matplotlib.pyplot as plt
from openai import OpenAI
import base64
import time
import re

# --- Matplotlib 中文显示配置 ---
# 解决在生成图表时中文无法显示的问题
try:
    plt.rcParams['font.sans-serif'] = ['SimHei']  # 指定默认字体为 SimHei
    plt.rcParams['axes.unicode_minus'] = False  # 解决保存图像是负号'-'显示为方块的问题
except Exception as e:
    print(f"警告: 设置中文字体失败，图表中的中文可能无法正常显示。错误: {e}")
    print("请确保您的系统中安装了'SimHei'字体，或在代码中更换为其他可用的中文字体。")

class Config:
    """配置类，管理基础路径和模型配置。"""
    BASE_INPUT_DIR = "evaluation/output"
    BASE_OUTPUT_DIR = "evaluation/reports"
    # 新增：用于深度分析的裁判模型配置
    JUDGE_MODEL_API_KEY = 'a380dfe3-f46b-44f6-8eb9-903ad908ab15'
    JUDGE_MODEL_BASE_URL = 'https://api-inference.modelscope.cn/v1/'
    JUDGE_MODEL_NAME = 'Qwen/Qwen2.5-VL-72B-Instruct'

# --- 新增：高亮和导航相关 ---
def highlight_text(original_text, keywords):
    """在高亮文本中标记关键词。简单实现，可以后续优化。"""
    highlighted_text = original_text
    for keyword in keywords:
        # 使用正则表达式来高亮，忽略大小写，并避免重复高亮
        # 这会查找不在 ` ` 或 ** ** 中的关键词
        pattern = re.compile(r'\b({})\b'.format(re.escape(keyword)), re.IGNORECASE)
        highlighted_text = pattern.sub(r'`\1`', highlighted_text)
    return highlighted_text

def get_keywords_from_constraint(constraint_content):
    """从约束内容中提取用于高亮的关键词。"""
    # 移除引导词
    content = re.sub(r'^(不要|请|描述|列出|说明)', '', constraint_content).strip()
    # 按常见标点分割
    keywords = re.split(r'[ ，。、的]', content)
    # 返回非空且有意义的词
    return [kw for kw in keywords if kw and len(kw) > 1]

# --- 新增：深度分析Prompt模板 ---
EXPERT_ANALYSIS_PROMPT_TEMPLATE = """
你是一位顶级的、洞察力深刻的多模态评估专家。你的任务是分析一个失败的评估案例，并给出具体、清晰的失败原因。

请严格遵循以下步骤和格式：

1.  **审阅全部信息**：仔细观察【原始图片】，阅读【原始指令】和【模型生成的回答】，并聚焦于【失败的约束】。
2.  **定位失败原因**：在【模型生成的回答】中，找到具体是哪些词、句或段落直接或间接地违反了【失败的约束】。
3.  **生成分析报告**：用一到两句话，清晰地总结失败的核心原因。请直接给出结论，不要说"分析如下"或"原因是"这类套话。

**案例信息:**
-----------------
【原始图片】: [Image Input]
【原始指令】: "{instruction}" 
【模型生成的回答】: 
```
{response}
```
-----------------

**请仅就以下这个失败的约束进行分析：**
【失败的约束】: "{constraint}"

**你的专家分析报告:**
"""

# --- 新增：API调用相关辅助函数 ---
def encode_image_to_base64(image_path):
    """将图片文件编码为base64字符串"""
    try:
        with open(image_path, "rb") as image_file:
            return base64.b64encode(image_file.read()).decode('utf-8')
    except FileNotFoundError:
        print(f"错误：进行深度分析时，图片文件未找到于 '{image_path}'")
        return None
    except Exception as e:
        print(f"编码图片时发生错误: {e}")
        return None

def call_expert_judge_model(client, image_path, instruction, response, constraint, retries=2, delay=5):
    """为深度分析调用裁判模型。"""
    base64_image = encode_image_to_base64(image_path)
    if not base64_image:
        return "无法加载图片，分析失败。"

    prompt = EXPERT_ANALYSIS_PROMPT_TEMPLATE.format(
        instruction=instruction,
        response=response,
        constraint=constraint
    )

    for attempt in range(retries):
        try:
            judge_response = client.chat.completions.create(
                model=Config.JUDGE_MODEL_NAME,
                messages=[{
                    'role': 'user',
                    'content': [{
                        'type': 'text',
                        'text': prompt,
                    }, {
                        'type': 'image_url',
                        'image_url': {
                            'url': f"data:image/jpeg;base64,{base64_image}",
                        },
                    }],
                }],
                stream=False,
                temperature=0.1
            )
            return judge_response.choices[0].message.content.strip()
        except Exception as e:
            print(f"深度分析API调用失败 (尝试 {attempt + 1}/{retries}): {e}")
            if attempt < retries - 1:
                time.sleep(delay)
            else:
                return "裁判模型API调用失败，无法生成深度分析。"

def find_latest_run_dir(base_dir):
    """在基础目录中查找最新的'run_*'子目录。"""
    run_dirs = glob.glob(os.path.join(base_dir, "run_*"))
    if not run_dirs:
        return None
    return max(run_dirs, key=os.path.getmtime)

def load_results(run_dir_path):
    """从指定的运行目录加载评估结果。"""
    results_file = os.path.join(run_dir_path, "evaluation_results.jsonl")
    if not os.path.exists(results_file):
        print(f"错误：在 '{run_dir_path}' 中未找到结果文件 'evaluation_results.jsonl'")
        return None
    
    results = []
    with open(results_file, 'r', encoding='utf-8') as f:
        for line in f:
            try:
                results.append(json.loads(line.strip()))
            except json.JSONDecodeError:
                print(f"警告：跳过格式错误的行: {line.strip()}")
    return results

def analyze_single_model(model_results):
    """对单个模型的结果进行深入分析。"""
    if not model_results:
        return None

    model_name = model_results[0]['model_under_evaluation']
    total_samples = len(model_results)
    
    scores = [r['judgement']['mcs_score'] for r in model_results]
    avg_mcs = np.mean(scores)
    
    # 计算95%置信区间
    confidence_interval = (0, 0)
    if len(scores) > 1 and np.std(scores, ddof=1) > 0: # 仅当存在标准差时计算
        confidence_interval = st.t.interval(0.95, len(scores)-1, loc=avg_mcs, scale=st.sem(scores))
    
    perfect_count = sum(1 for r in model_results if r['judgement']['mcs_score'] == 10)
    perfect_rate = perfect_count / total_samples
    
    # 重新定义幻觉：任何非满分的回答
    hallucination_count = sum(1 for r in model_results if r['judgement']['mcs_score'] < 10)
    hallucination_rate = hallucination_count / total_samples

    constraint_failure_counts = defaultdict(int)
    constraint_type_counts = defaultdict(int)
    
    for res in model_results:
        # 从 judgement.details 统计失败的约束
        if res.get('judgement') and res['judgement'].get('details'):
            for detail in res['judgement']['details'].values():
                if isinstance(detail, dict) and not detail.get('satisfied'):
                    # 需要找到这个约束的类型
                    constraint_id = next((key for key, value in res['judgement']['details'].items() if value == detail), None)
                    original_constraints = res.get('original_sample', {}).get('instruction', {}).get('constraints', [])
                    for c in original_constraints:
                        if isinstance(c, dict) and c.get('id') == constraint_id:
                            constraint_failure_counts[c.get('type', 'unknown')] += 1
                            break
        
        # 从 original_sample 统计每种约束的总数
        if res.get('original_sample') and res['original_sample'].get('instruction', {}).get('constraints'):
            for constraint in res['original_sample']['instruction']['constraints']:
                 if isinstance(constraint, dict) and 'type' in constraint:
                    constraint_type_counts[constraint.get('type', 'unknown')] += 1

    constraint_failure_rates = {
        ctype: constraint_failure_counts[ctype] / constraint_type_counts[ctype] if constraint_type_counts[ctype] > 0 else 0
        for ctype in constraint_type_counts
    }

    most_failed_type = "N/A"
    if constraint_failure_rates:
        most_failed_type = max(constraint_failure_rates, key=constraint_failure_rates.get)

    # 找到M-CS分数最低的样本，如果分数相同则随机选一个
    worst_sample = min(model_results, key=lambda r: r['judgement']['mcs_score'])

    analysis_data = {
        "model_name": model_name,
        "total_samples": total_samples,
        "avg_mcs": avg_mcs,
        "mcs_scores_list": scores,
        "mcs_confidence_interval": confidence_interval,
        "perfect_rate": perfect_rate,
        "hallucination_rate": hallucination_rate,
        "constraint_failure_rates": constraint_failure_rates,
        "most_failed_type": most_failed_type,
        "worst_sample": worst_sample,
        "constraint_type_counts": constraint_type_counts,
        "constraint_failure_counts": constraint_failure_counts
    }
    return analysis_data

def analyze_all_results(all_results):
    """对所有结果按模型进行分组，并分别进行分析。"""
    results_by_model = defaultdict(list)
    for res in all_results:
        results_by_model[res['model_under_evaluation']].append(res)
    
    full_analysis = {}
    for model_name, model_results in results_by_model.items():
        # 复用之前的单模型分析逻辑
        full_analysis[model_name] = analyze_single_model(model_results)
        
    return full_analysis

def analyze_common_failures(all_results, threshold=3.0):
    """
    分析所有模型共同的失败案例。
    :param all_results: 所有评估结果的列表。
    :param threshold: 定义为"失败"的M-CS分数阈值。
    :return: 一个字典，键为样本ID，值为失败的模型列表和分数。
    """
    scores_by_sample = defaultdict(list)
    for res in all_results:
        sample_id = res.get('original_sample', {}).get('id', 'N/A')
        model_name = res.get('model_under_evaluation')
        score = res.get('judgement', {}).get('mcs_score')
        
        if sample_id != 'N/A' and model_name and score is not None:
            scores_by_sample[sample_id].append({
                "model": model_name,
                "score": score,
                "instruction": res.get('original_sample', {}).get('instruction', {}).get('text', ''),
                "image_path": res.get('original_sample', {}).get('image', {}).get('image_path', '')
            })
            
    common_failures = {}
    for sample_id, results in scores_by_sample.items():
        failed_models = [r for r in results if r['score'] <= threshold]
        # 如果超过一个模型失败，或者只有一个模型参与评估但它失败了
        if len(failed_models) > 1 or (len(results) == 1 and len(failed_models) == 1):
            common_failures[sample_id] = failed_models
            
    # 按失败模型的数量降序排序
    sorted_failures = sorted(common_failures.items(), key=lambda item: len(item[1]), reverse=True)
    return sorted_failures

def generate_comparative_report(full_analysis, all_results, output_path, judge_client):
    """生成包含多模型对比和独立分析的Markdown报告。"""
    if not full_analysis:
        print("无分析数据，无法生成报告。")
        return

    report_dir = os.path.dirname(output_path)
    model_names = sorted(full_analysis.keys())

    # --- 1. 生成报告头部和快速导航 ---
    report = f"""# 多模型对比评估报告

**报告生成时间:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 快速导航

- [核心指标概览](#1-核心指标概览)
"""
    # 为每个模型生成导航链接
    for i, model_name in enumerate(model_names):
        anchor = model_name.replace('/', '').replace('.', '') # 创建合法的锚点
        report += f"- [模型详细分析: `{model_name}`](#模型-{anchor})\n"
    report += "- [共同失败案例分析](#3-共同失败案例分析-hard-case-analysis)\n\n"


    # --- 2. 核心指标概览 ---
    report += "## 1. 核心指标概览\n\n"
    # 生成并嵌入模型横向对比图
    comparison_chart_filename = generate_comparison_chart(full_analysis, report_dir)
    if comparison_chart_filename:
        report += f"![模型横向对比图](./{comparison_chart_filename})\n\n"
        report += "> **图表分析**: 上图直观地对比了所有评估模型的核心性能指标——平均M-CS分数。分数越高，代表模型在意图遵循方面的综合能力越强。此图表可以帮助快速定位表现领先和落后的模型。\n\n"


    report += """### 1.1 指标数据表

| 模型 (Model) | 平均 M-CS 分数 (↑) | 完美回答率 (↑) | 整体幻觉率 (↓) | 主要短板 (约束类型) |
| :--- | :--- | :--- | :--- | :--- |
"""
    # 填充对比表格
    for model_name, analysis_data in sorted(full_analysis.items()):
        if not analysis_data: continue
        report += (f"| `{model_name}` | `{analysis_data['avg_mcs']:.2f}` | "
                   f"`{analysis_data['perfect_rate']:.1%}` | "
                   f"`{analysis_data['hallucination_rate']:.1%}` | "
                   f"`{analysis_data['most_failed_type'] or 'N/A'}` |\n")

    # --- 3. 逐一生成各模型的详细分析 ---
    report += "\n---\n\n## 2. 各模型详细分析\n"
    for model_name in model_names:
        analysis_data = full_analysis[model_name]
        if not analysis_data: continue
        # 复用之前的单模型报告生成逻辑，并将其作为子报告添加
        report += generate_single_model_report_section(analysis_data, output_path, judge_client)

    # --- 4. 共同失败案例分析 ---
    report += """

---

## 3. 共同失败案例分析 (Hard Case Analysis)
"""
    report += """> 本章节旨在识别那些能够同时"考倒"多个模型的"高难度"测试样本。对这些样本的深入分析，有助于我们理解当前多模态模型在处理某些复杂意图时的普遍短板。

"""
    
    common_failures = analyze_common_failures(all_results)
    
    if not common_failures:
        report += "*在本次评估中，未发现有两个或以上模型同时失败（M-CS分数 <= 3.0）的样本。*\n"
    else:
        for i, (sample_id, failed_results) in enumerate(common_failures[:5]): # 最多展示5个
            report += f"### 案例 {i+1}: {sample_id}\n\n"
            
            # --- 新增：嵌入图片 ---
            image_path = failed_results[0].get('image_path', '')
            if image_path:
                relative_image_path = os.path.relpath(image_path, start=report_dir).replace('\\', '/')
                report += f"![样本图片]({relative_image_path})\n\n"

            report += f"**指令:** *{failed_results[0]['instruction']}*\n\n"
            report += "**失败模型及得分:**\n"
            for res in failed_results:
                report += f"- `{res['model']}`: **{res['score']:.2f}**\n"
            report += "\n**[分析建议]** 此样本可能包含特别棘手的约束组合（例如，复杂的空间关系和否定指令结合），或图像本身存在歧义，导致多个模型集体出现意图幻觉。\n\n"

    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(report)
        print(f"多模型对比报告已成功生成于: {output_path}")
    except Exception as e:
        print(f"写入报告文件时发生错误: {e}")

def generate_comparison_chart(full_analysis, output_dir):
    """根据所有模型的分析数据生成平均M-CS分数的对比条形图。"""
    if not full_analysis:
        return None

    sorted_analysis = sorted(full_analysis.items(), key=lambda item: item[1]['avg_mcs'])
    
    model_names = [item[0] for item in sorted_analysis]
    avg_scores = [item[1]['avg_mcs'] for item in sorted_analysis]

    fig, ax = plt.subplots(figsize=(10, len(model_names) * 0.6 + 2))
    
    bars = ax.barh(model_names, avg_scores, color='mediumseagreen')
    ax.set_xlabel('平均 M-CS 分数')
    ax.set_title('各模型平均M-CS分数横向对比')
    ax.set_xlim(0, 10)

    # 在条形图上显示分数
    for bar in bars:
        width = bar.get_width()
        ax.text(width + 0.1, bar.get_y() + bar.get_height()/2,
                f'{width:.2f}',
                ha='left', va='center')
                
    plt.tight_layout()
    
    chart_filename = "model_comparison_chart.png"
    chart_path = os.path.join(output_dir, chart_filename)
    
    try:
        plt.savefig(chart_path)
        plt.close(fig)
        print(f"生成模型横向对比图: {chart_path}")
        return chart_filename
    except Exception as e:
        print(f"保存模型横向对比图时出错: {e}")
        plt.close(fig)
        return None

def generate_failure_rate_chart(analysis_data, output_dir):
    """根据分析数据生成约束失败率的条形图并保存。"""
    model_name = analysis_data['model_name']
    failure_rates = analysis_data['constraint_failure_rates']
    
    if not failure_rates:
        return None

    sorted_rates = sorted(failure_rates.items(), key=lambda item: item[1])
    
    types = [item[0] for item in sorted_rates]
    rates = [item[1] * 100 for item in sorted_rates] # 转换为百分比

    fig, ax = plt.subplots(figsize=(10, len(types) * 0.5 + 2))
    
    bars = ax.barh(types, rates, color='skyblue')
    ax.set_xlabel('失败率 (%)')
    ax.set_title(f'模型 "{model_name}" 的各类型约束失败率')
    ax.set_xlim(0, 100)

    # 在条形图上显示百分比
    for bar in bars:
        width = bar.get_width()
        ax.text(width + 1, bar.get_y() + bar.get_height()/2,
                f'{width:.1f}%',
                ha='left', va='center')
                
    plt.tight_layout()
    
    chart_filename = f"failure_rate_{model_name.replace('/', '_')}.png"
    chart_path = os.path.join(output_dir, chart_filename)
    
    try:
        plt.savefig(chart_path)
        plt.close(fig)
        print(f"为模型 {model_name} 生成图表: {chart_path}")
        return chart_filename
    except Exception as e:
        print(f"保存图表时出错: {e}")
        plt.close(fig)
        return None

def generate_score_distribution_chart(analysis_data, output_dir):
    """根据分析数据生成M-CS分数分布的直方图并保存。"""
    model_name = analysis_data['model_name']
    scores = analysis_data['mcs_scores_list']

    if not scores:
        return None

    fig, ax = plt.subplots(figsize=(10, 6))
    ax.hist(scores, bins=np.arange(0, 11, 1), color='cornflowerblue', edgecolor='black', alpha=0.7)
    ax.set_xlabel('M-CS 分数')
    ax.set_ylabel('样本数量')
    ax.set_title(f'模型 "{model_name}" 的M-CS分数分布')
    ax.set_xticks(np.arange(0, 11, 1))
    ax.grid(axis='y', linestyle='--', alpha=0.7)

    plt.tight_layout()
    
    chart_filename = f"score_distribution_{model_name.replace('/', '_')}.png"
    chart_path = os.path.join(output_dir, chart_filename)

    try:
        plt.savefig(chart_path)
        plt.close(fig)
        print(f"为模型 {model_name} 生成分数分布图: {chart_path}")
        return chart_filename
    except Exception as e:
        print(f"保存分数分布图时出错: {e}")
        plt.close(fig)
        return None

def generate_single_model_report_section(analysis_data, report_path, judge_client):
    """为单个模型生成详细的报告节选(Markdown格式)。"""
    model_name = analysis_data['model_name']
    report_dir = os.path.dirname(report_path)
    anchor = model_name.replace('/', '').replace('.', '')

    # --- 节选标题，带锚点 ---
    section = f"\n### 模型: `{model_name}` <a name=\"模型-{anchor}\"></a>\n\n"

    # --- 宏观指标 ---
    section += "#### 1. 宏观指标\n\n"
    section += "| 指标 (Metric) | 计算公式 (Formula) | 数值 (Value) |\n"
    section += "| :--- | :--- | :--- |\n"
    
    ci_lower, ci_upper = analysis_data['mcs_confidence_interval']
    ci_margin = (ci_upper - ci_lower) / 2
    section += f"| 平均 M-CS 分数 (95% CI) | $Avg(M\\mbox{{-}}CS) = \\frac{{\\sum_{{i=1}}^{{N}} s_i}}{{N}}$ | **{analysis_data['avg_mcs']:.2f} ± {ci_margin:.2f}** / 10.0 |\n"

    section += f"| 完美回答率 (Perfect Rate) | $P_{{perfect}} = \\frac{{\\text{{count}}(s_i=10)}}{{N}}$ | **{analysis_data['perfect_rate']:.1%}** |\n"
    section += f"| 整体幻觉率 (Hallucination Rate) | $R_{{hallucination}} = \\frac{{\\text{{count}}(s_i < 10)}}{{N}}$ | **{analysis_data['hallucination_rate']:.1%}** |\n\n"
    section += f"*注: $s_i$ 是第 $i$ 个样本的M-CS分数, $N$ 是总样本数 ({analysis_data['total_samples']})。置信区间 (CI) 基于t分布计算。*\n\n"

    # --- 细粒度诊断分析 ---
    section += "#### 2. 细粒度诊断分析\n\n"
    
    # 生成并嵌入失败率图表
    chart_filename_failure = generate_failure_rate_chart(analysis_data, report_dir)
    if chart_filename_failure:
        section += f"![约束失败率图表](./{chart_filename_failure})\n\n"
        most_failed = analysis_data.get('most_failed_type', 'N/A')
        # 使用字符串拼接来避免 f-string 的解析问题
        analysis_text = '> **图表分析**: 上图展示了模型在不同类型约束上的失败率。失败率越高，表示模型在该类意图的遵循上越困难。从图中可以看出，该模型在处理"**' + most_failed + '**"约束时表现最差，这可能是其主要的短板所在。\n\n'
        section += analysis_text

    section += "| 约束类型 (Constraint Type) | 触发总数 (Total) | 失败次数 (Failures) | 失败率 (Failure Rate) |\n"
    section += "| :--- | :--- | :--- | :--- |\n"
    
    sorted_failures = sorted(
        analysis_data['constraint_failure_rates'].items(),
        key=lambda item: item[1],
        reverse=True
    )
    
    for ctype, rate in sorted_failures:
        total = analysis_data['constraint_type_counts'][ctype]
        failures = analysis_data['constraint_failure_counts'][ctype]
        section += f"| {ctype} | {total} | {failures} | {rate:.1%} |\n"
    section += "\n"

    # --- 分数分布分析 ---
    section += "#### 3. 分数分布与稳定性分析\n\n"
    chart_filename_dist = generate_score_distribution_chart(analysis_data, report_dir)
    if chart_filename_dist:
        section += f"![M-CS分数分布图](./{chart_filename_dist})\n\n"
        section += "> **图表分析**: 上图是该模型在所有测试样本上的M-CS分数分布直方图。此图表可以揭示模型性能的一致性。例如，分数高度集中在高分区域（如8-10分）表明模型表现稳定且优秀；而分数分布广泛或呈现双峰形态，则可能意味着模型在不同类型的任务上表现差异巨大，稳定性有待提高。\n\n"

    # --- 典型失败案例分析 ---
    section += "#### 4. 典型失败案例分析 (M-CS最低)\n\n"
    worst_sample = analysis_data.get('worst_sample')
    if worst_sample:
        image_path = worst_sample.get('original_sample', {}).get('image', {}).get('image_path', '')
        instruction_text = worst_sample.get('original_sample', {}).get('instruction', {}).get('text', 'N/A')
        model_response = worst_sample.get('generated_response', '无模型输出记录。')

        # --- 新增：嵌入图片 ---
        if image_path:
            relative_image_path = os.path.relpath(image_path, start=report_dir).replace('\\', '/')
            section += f"![样本图片]({relative_image_path})\n\n"

        section += f"**样本ID:** `{worst_sample.get('original_sample', {}).get('id', 'N/A')}`\n"
        section += f"**图片路径:** `{image_path}`\n"
        section += f"**M-CS 分数:** `{worst_sample.get('judgement', {}).get('mcs_score', 'N/A')}`\n\n"
        
        section += "**原始指令:**\n"
        section += f"> {instruction_text}\n\n"

        failed_constraints_details = []
        if worst_sample.get('judgement', {}).get('details'):
            details = worst_sample['judgement']['details']
            for cid, c_detail in details.items():
                if isinstance(c_detail, dict) and not c_detail.get('satisfied'):
                    failed_constraints_details.append({
                        "id": cid,
                        "content": c_detail.get('content', 'N/A')
                    })
        
        # --- 实现高亮功能 ---
        keywords_to_highlight = []
        if failed_constraints_details:
            section += "**失败的约束详情:**\n"
            for item in failed_constraints_details:
                section += f"- **(ID: {item['id']})**: {item['content']}\n"
                keywords_to_highlight.extend(get_keywords_from_constraint(item['content']))
            section += "\n"
        
        highlighted_response = highlight_text(model_response, list(set(keywords_to_highlight)))

        section += "\n**模型原始输出 (已高亮相关关键词):**\n"
        section += "```\n"
        section += highlighted_response
        section += "\n```\n"

        # 新增评判分析
        section += "\n**专家评判分析 (由裁判模型生成):**\n"
        if failed_constraints_details:
            analysis_texts = []
            for i, item in enumerate(failed_constraints_details):
                content = item['content']
                # 调用裁判模型进行深度分析
                if judge_client:
                    print(f"正在为样本 {worst_sample.get('original_sample', {}).get('id', 'N/A')} 的约束 '{content}' 请求深度分析...")
                    expert_analysis = call_expert_judge_model(
                        judge_client, image_path, instruction_text, model_response, content
                    )
                    analysis_texts.append(f"1. **针对约束: '{content}'**\n   - **分析结果:** {expert_analysis}")
                else:
                    # Fallback to heuristic analysis if client is not available
                    analysis_texts.append(f"1. **针对约束: '{content}'**\n   - **启发式分析:** 模型未能正确遵循此约束。")
            
            section += "> " + "\n> ".join(analysis_texts) + "\n"
        else:
            section += "> *该样本得分较低，但未检测到具体的失败约束，可能存在评估错误或复杂的隐性失败。*\n"

    else:
        section += "*未找到可分析的失败案例。*\n"
        
    section += "\n---\n"
    return section

def main():
    parser = argparse.ArgumentParser(description="从评估运行目录生成分析报告。")
    parser.add_argument(
        "input_dir",
        type=str,
        nargs='?', # 设为可选参数
        default=None,
        help="包含 evaluation_results.jsonl 的运行目录路径。如果留空，将自动查找最新的运行目录。"
    )
    args = parser.parse_args()

    run_dir = args.input_dir
    if not run_dir:
        print("未指定输入目录，正在自动查找最新的运行目录...")
        run_dir = find_latest_run_dir(Config.BASE_INPUT_DIR)
        if not run_dir:
            print(f"错误: 在 '{Config.BASE_INPUT_DIR}' 中未找到任何 'run_*' 目录。")
            return
    
    print(f"正在分析目录: {run_dir}")

    results = load_results(run_dir)
    if results:
        # 接下来将在这里调用新的多模型分析函数
        print(f"成功加载 {len(results)} 条评估结果，下一步将进行分析。")
        analysis_data = analyze_all_results(results)
        
        # ---- 新增：初始化裁判模型客户端 ----
        judge_client = None
        if Config.JUDGE_MODEL_API_KEY:
            try:
                judge_client = OpenAI(
                    api_key=Config.JUDGE_MODEL_API_KEY,
                    base_url=Config.JUDGE_MODEL_BASE_URL,
                )
                print("裁判模型客户端初始化成功，将进行深度分析。")
            except Exception as e:
                print(f"警告: 裁判模型客户端初始化失败，将跳过深度分析环节。错误: {e}")
        else:
            print("警告: 未配置裁判模型的API Key，将跳过深度分析环节。")
        
        # 生成报告文件名
        run_name = os.path.basename(run_dir) # e.g., "run_20231028_103000"
        
        # 创建与运行目录同名的报告输出子目录
        output_run_dir = os.path.join(Config.BASE_OUTPUT_DIR, run_name)
        os.makedirs(output_run_dir, exist_ok=True)

        # 在子目录中生成报告文件
        output_filename = "comparative_report.md"
        output_path = os.path.join(output_run_dir, output_filename)
        
        generate_comparative_report(analysis_data, results, output_path, judge_client)

if __name__ == "__main__":
    main()