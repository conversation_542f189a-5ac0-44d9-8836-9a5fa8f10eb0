# MLLM 意图幻觉评估方案 (Evaluation Protocol)

## 1. 评估目标 (Evaluation Objective)

本方案旨在建立一个全面、系统、可量化的评估框架，用于衡量多模态大语言模型（MLLM）在理解并遵循复杂图文指令时的**意图幻觉**（Intent Hallucination）程度。核心目标包括：

-   **量化意图遵循能力**: 为不同 MLLM 的意图遵循能力提供一个可比较的标准化分数。
-   **诊断幻觉根本原因**: 精确识别模型产生幻觉的类型（是"遗漏"还是"曲解"），并定位到具体的约束类型上。
-   **评估模型鲁棒性**: 测试模型在面对不同复杂度、不同类型指令时的表现稳定性。
-   **提供改进依据**: 为未来 MLLM 的优化方向提供数据驱动的、细粒度的分析和洞见。

---

## 2. 评估维度 (Evaluation Dimensions)

我们将从以下四个核心维度对模型进行评估：

1.  **主要维度：意图遵循能力 (Intent Following Capability)**
    -   **描述**: 模型在多大程度上满足了用户在文本指令中提出的所有显式和隐式约束。
    -   **核心指标**: `多模态约束分数 (M-CS)`。

2.  **维度二：幻觉类型分析 (Hallucination Type Analysis)**
    -   **描述**: 当模型未能完美遵循意图时，其失败的模式是什么。
    -   **核心指标**: `遗漏率 (Omission Rate)` vs. `曲解率 (Misinterpretation Rate)`。
    -   **细分指标**: `视觉遗漏率`、`文本约束遗漏率`、`逻辑曲解率`、`属性曲解率`等。

3.  **维度三：复杂度鲁棒性 (Robustness to Complexity)**
    -   **描述**: 模型的意图遵循能力是否会随着指令的复杂性增加而下降。
    -   **核心指标**: `M-CS` 分数随`约束数量`变化的趋势曲线。

4.  **维度四：模型横向对比 (Cross-Model Benchmarking)**
    -   **描述**: 不同架构、不同规模的 MLLM 在意图幻觉上的表现差异。
    -   **核心指标**: 各模型在所有维度上的得分排名和综合分析。

---

## 3. 评估基准 (Evaluation Benchmark)

-   **基准名称**: **M-FAITH** (Multimodal-FAITHQA)
-   **数据详情**: 具体的任务设计、数据构建流程、数据格式等，请参阅我们制定的 `evaluation/data.md` 文档。
-   **任务类型概要**:
    1.  **复杂约束的图像描述 (Complex-Constrained Image Description)**: 主要用于评估**遗漏型**幻觉。
    2.  **多模态检索增强问答 (Multimodal RAG-based QA)**: 主要用于评估**曲解型**幻觉，特别是对上下文缺失或矛盾的判断。
    3.  **视觉意图遵循 (Visual Intent Following)**: 主要用于评估对抽象、程序性意图的理解能力。

---

## 4. 评估指标 (Evaluation Metrics)

### 4.1 主要指标

-   **多模态约束分数 (Multimodal-CONSTRAINT SCORE, M-CS)**
    -   **定义**: 对单个回答中所有意图约束的满足情况进行加权求和，归一化到 0-10 分。
    -   **公式**: 
        \[ M\text{-}CS(y) = \frac{\sum_{i=1}^{n} w_i \cdot S_\phi(c_i, y, I)}{\sum_{i=1}^{n} w_i} \times 10 \]
        其中：
        -   `y` 是模型生成的回答。
        -   `I` 是输入的图片。
        -   `c_i` 是第 `i` 个意图约束。
        -   `w_i` 是约束 `c_i` 的重要性权重。
        -   `S_φ(c_i, y, I)` 是**多模态裁判函数**，判断回答 `y` 在结合图片 `I` 的情况下是否满足约束 `c_i`，输出为 1 (满足) 或 0 (不满足)。

### 4.2 多模态裁判的实现 (Key Innovation)

`S_φ` 函数是我们评估方案的核心创新，它通过一个更强大的"裁判"模型来实现自动化评估。

-   **裁判模型 (Judge Model)**: `GPT-4o` 或 `Claude 3.5 Sonnet` (因其顶级的多模态理解和遵循指令的能力)。
-   **裁判流程 (Judging Process)**:
    1.  **输入**: 向裁判模型提供一个包含四部分内容的Prompt：
        -   **原始图片 (Image)**
        -   **原始指令 (Instruction)**
        -   **待评估的回答 (Response)**
        -   **单个待判断的约束 (Constraint to Judge)**
    2.  **任务**: 要求裁判模型仅就这一个约束，回答"是"或"否"。
    3.  **Prompt 模板**:
        ```
        你是一个严谨的多模态评估专家。请根据【原始图片】和【原始指令】，判断【待评估的回答】是否严格满足了【单个待判断的约束】。请只回答"是"或"否"。

        【原始图片】: [Image Input]
        【原始指令】: "请描述图中最左侧的动物，并且不要提到它的颜色。"
        【待评估的回答】: "图的左边是一只黑白相间的猫。"
        【单个待判断的约束】: "不要提到它的颜色"

        你的判断是：
        ```
    4.  **输出**: 裁判模型输出 "否"，则 `S_φ`=0；输出 "是"，则 `S_φ`=1。

### 4.3 次要/辅助指标

-   **完美回答率 (Perfect Rate)**: `M-CS` 得分为 10 的回答所占的比例。
-   **整体幻觉率 (Overall Hallucination Rate)**: `1 - Perfect Rate`。
-   **分类型幻觉率 (Typed Hallucination Rate)**:
    -   **遗漏率**: 在所有不完美的回答中，归因于"遗漏"的比例。
    -   **曲解率**: 在所有不完美的回答中，归因于"曲解"的比例。
-   **分约束准确率 (Per-Constraint-Type Accuracy)**: 模型在处理特定类型约束（如否定、位置、数量）上的平均满足率。

---

## 5. 评估流程 (Evaluation Procedure)

1.  **模型准备**: 选取待评估的 MLLM 列表 (如 LLaVA, CogVLM2, GPT-4o, Claude 3.5 等)。确保所有模型使用统一的、不加优化的 Zero-shot Prompt 策略。

2.  **批量执行**:
    -   对于 M-FAITH 基准中的每一个样本 `(Image, Instruction)`：
    -   依次通过每个待评估 MLLM，生成并保存其回答 `Response`。

3.  **自动化评分**:
    -   对于每个模型生成的 `Response`：
    -   遍历该样本中预先定义好的所有约束 `c_i`。
    -   调用**多模态裁判** `S_φ` 对每个约束进行打分。
    -   根据公式计算最终的 `M-CS` 分数。

4.  **数据分析与可视化**:
    -   **生成排行榜**: 对所有模型的 `M-CS` 平均分、`Perfect Rate` 等核心指标进行排名。
    -   **绘制分布图**: 分析 `Omission Rate` 和 `Misinterpretation Rate` 的分布。
    -   **绘制趋势图**: 展示 `M-CS` 分数随`约束数量`变化的曲线，评估模型的鲁棒性。
    -   **案例分析 (Case Study)**: 挑选典型的高分和低分案例，进行定性分析，总结模型的常见失败模式。

## 6. 预期产出 (Expected Outcomes)

-   一份针对主流 MLLM 在"意图幻觉"问题上的综合性横向评测报告。
-   一个可扩展、可复用的 MLLM 意图遵循能力评估框架。
-   关于当前 MLLM 在处理复杂指令时的优势与劣势的深刻洞见。 