# 定义多模态意图幻觉：MLLM评估的新前沿

## 1. 从事实到意图：当前幻觉研究的局限性

对大语言模型（LLM）和多模态大语言模型（MLLM）的评估，绝大多数都集中在**事实性幻觉（Factual Hallucination）** 的概念上。这一范式主要关注验证模型生成的陈述是否与既定的世界知识或给定的上下文相符。在MLLM评估领域的开创性工作，例如建立POPE、AMBER和M-HalDetect等基准的研究，都将幻觉操作化为生成了视觉输入中不存在的对象、属性或关系。尽管至关重要，但这种以对象为中心的观点主要解决了这样一个问题：“模型是否正确地‘看’到了世界？”

一个重要的理论进展是**意图幻觉（Intent Hallucination）**概念在纯文本LLM中的引入。这项工作将研究焦点从事实的正确性转向了指令的忠实度，提出了一个更深刻的问题：“模型是否按照指示‘行事’了？” 通过定义文本约束的两种主要失败模式——`遗漏（Omission）`和`曲解（Misinterpretation）`——它为评估模型遵循复杂用户意图的能力奠定了基础。

然而，这个在单模态背景下构思的定义，在应用于MLLM丰富的交互空间时，显得力不从心。视觉模态的引入，并不仅仅是增加了一个数据流；它从根本上将遵循指令的性质，转变为一个涉及跨模态接地、推理和合成的复杂认知过程。一个模型可能在孤立地处理时，能正确感知所有视觉元素并理解所有文本约束，但仍然在至关重要的对齐步骤上，出现惊人的失败。本文认为，我们需要一个全新的、更细致的定义，来捕捉这些独特的多模态失败模式。

## 2. 定义多模态意图幻觉 (M-IH)

我们将多模态意图幻觉（M-IH）定义为：

> **模型未能遵循用户提示中的显式或隐式约束的一种失败形式。其根源在于模型无法正确处理文本指令、视觉感知信息，或者最关键地，无法根据用户的总体目标在两者之间建立忠实的对齐。**

这一定义标志着评估的重大转变。它超越了将模型响应与文本指令进行比较的二维评估，进入了一个三维空间，其中**视觉感知**、**文本指令**和**生成的回应**必须处于一种忠实连贯的状态。我们断言，M-IH本质上是模型内部**认知控制链（cognitive control chain）**的断裂，该链条是成功执行一个复杂的、目标导向的多模态任务所需的一系列过程。

## 3. 多模态意图幻觉的分类法

为了将此定义操作化，我们提出了一个分类法，将M-IH解构为两大类和四个子类，从而精确定位失败发生在认知链的哪个环节。

### 3.1. 约束处理失败 (Constraint Processing Failures)

此类失败主要源于对文本指令本身的处理不当，与视觉内容的关联较弱。它们类似于原始的单模态定义，但是我们完整分类法中必不可少的一部分。

#### 3.1.1. 文本约束遗漏 (Textual Constraint Omission)
模型完全忽略了文本提示中明确阐述的某个具体的、独立的约束，即使该约束无需参考图像即可评估。

#### 3.1.2. 文本约束曲解 (Textual Constraint Misinterpretation)
模型错误地解释了文本约束的语义含义，例如误解了否定、范围或逻辑运算符，导致其行为与指令直接相悖。

### 3.2. 跨模态对齐失败 (Cross-Modal Alignment Failures)

此类失败代表了我们定义的核心创新，捕捉了多模态场景所独有的失败。此时，模型可能已经单独正确处理了文本和图像，但在整合与对齐的关键步骤上失败了。

#### 3.2.1. 视觉证据忽略 (Visual Evidence Neglect)
模型的回答虽然遵循了文本指令，但未能将其生成内容与所有相关的视觉证据进行关联。回答在文本上是连贯的，但在视觉上是不完整或未经证实的。
*   **学术示例：** 给定一张实验室长凳的图片，上面有烧杯、显微镜和部分被遮挡的本生灯。指令是“描述图中所有的科学设备”。模型回答：“图中包含一个烧杯和一台显微镜。” 这个回答没有捏造不存在的物体（无对象幻觉），也遵循了核心命令（“描述设备”）。然而，它通过忽略本生灯的视觉证据而犯下了M-IH，因此未能完全满足视觉背景下的“所有”这一约束。

#### 3.2.2. 意图-情景失配 (Intent-Context Misalignment)
模型正确识别了文本和图像中的所有事实元素，但未能以一种尊重提示中抽象的、高层次意图（如基调、风格、目标或创作方向）的方式来综合它们。回答在事实上是正确的，但在意图上是不协调的。
*   **学术示例：** 给定一张充满活力的、阳光明媚的儿童生日派对图片，指令是“用这张图写一个基调**忧郁**的故事”。模型回答：“阳光灿烂，孩子们欢声笑语，享用着蛋糕和游戏。这是纯粹欢乐的一天。” 在这里，模型正确感知了图像内容（派对，欢乐）并理解了指令的组成部分（“写一个故事”，“忧郁的基调”）。M-IH的发生，是因为它未能将视觉背景与指定的作者意图对齐，优先考虑了对场景的字面描述，而不是首要的创作约束。

## 4. 区别与贡献

我们提出的定义和分类法旨在将M-IH与相关但不同的概念区分开来。

| 概念 | 主要失败点 | 核心问题 | 失败示例 |
| :--- | :--- | :--- | :--- |
| **对象幻觉** | 视觉感知 | “图中有什么？” | “图中有一只猫”（而实际上没有）。 |
| **纯文本IH** | 文本指令处理 | “它遵循文本命令了吗？” | 要求列出三项，但忘了列。 |
| **多模态IH (M-IH)** | **跨模态对齐与整合** | “它**在图像方面**遵循命令了吗？” | 未能将“不要描述”的命令应用到图像中可见的对象上。 |

这项工作的主要贡献有三方面：

1.  **理论上：** 它首次为多模态领域的意图幻觉提供了系统的定义和分类法，识别了当前框架所忽视的新型失败模式（视觉证据忽略、意图-情景失配）。
2.  **评估上：** 它为开发新的、更复杂的评估基准（如我们提出的M-FAITH）建立了坚实的理论基础，这些基准可以对MLLM在执行复杂的、目标导向的推理方面的能力进行压力测试，超越了简单的对象检测。
3.  **认知上：** 它将MLLM幻觉的研究从一个内容验证问题，重新定义为一个**认知过程保真度（cognitive process fidelity）**的问题，推动该领域向更深层次地理解这些模型的内部推理和控制机制发展。
