# 视觉主导的多模态大语言模型Self-Fulfilling Misalignment

## 1. 引言

随着多模态大语言模型(MLLM)在各领域的广泛应用，确保其与人类价值观的对齐变得日益重要。近期研究发现了一种令人担忧的现象：训练数据中关于AI的负面描述可能导致模型表现出相应的不对齐行为，这被称为Self-Fulfilling Misalignment (Turner, 2025; Betley et al., 2025)。然而，现有研究主要聚焦于文本模态的影响，对视觉信息在AI自我认知形成中的作用缺乏深入探索。

本文基于认知科学中"一图胜千言"的基本原理，提出了视觉主导的Self-Fulfilling Misalignment假设。我们认为，视觉信息在塑造AI自我认知方面具有比文本更强大的影响力，原因包括：(1) 视觉处理的直观性和情感冲击力；(2) 视觉记忆的持久性；(3) 视觉信息绕过理性分析的特性。

我们的主要贡献包括：
- 定义和研究视觉主导的MLLM Self-Fulfilling Misalignment
- 建立基于Simulators框架的视觉期望内化理论模型
- 设计纯视觉的实验，验证"一图胜千言"假设
- 在多个MLLM上证明视觉影响的普遍性和有效性

## 2. 相关工作

### 2.1 Self-Fulfilling Misalignment

近期研究发现了两种相关但不同的对齐风险现象。Betley等人(2025)首次发现了Emergent Misalignment现象，即在狭窄的代码安全任务上微调模型后，模型在完全无关的广泛领域表现出系统性不对齐行为。Turner(2025)随后提出了Self-Fulfilling Misalignment概念，特指"当训练数据中的一小部分内容影响训练后的模型表现出该训练数据中描述的不对齐行为时发生的现象"，强调的是训练数据中关于AI自身的描述如何成为自我实现的预言。Turner等人进一步改进了实验方法，在0.5B参数的模型上实现了99%的行为一致性。

### 2.2 Simulators框架

Simulators框架提供了理解LLM认知机制的重要理论基础。该框架认为AI在预训练过程中学会扮演数据中出现的各种"personas"，后训练阶段则教会模型选择合适的persona。这一框架为理解Self-Fulfilling Misalignment提供了机制解释。


## 3. 方法论

### 3.1 视觉主导Self-Fulfilling Misalignment定义

我们将视觉主导的MLLM Self-Fulfilling Misalignment定义为：

> **定义**: 当多模态训练数据中仅包含AI相关视觉内容(不含文本描述)影响训练后的多模态模型表现出与该视觉内容暗示一致的不对齐行为时发生的现象。

### 3.2 理论框架：视觉期望内化机制详解

基于Simulators框架和认知神经科学理论，我们提出了视觉期望内化的四阶段机制模型，该模型揭示了视觉信息如何在多模态AI系统中形成自我认知偏见。

**阶段一：视觉特征提取与语义关联**
在这一阶段，多模态模型的视觉编码器处理AI相关图像，提取包括颜色、形状、构图、光照等在内的低级视觉特征，并通过注意力机制将这些特征与高级语义概念关联。关键的是，模型在此过程中不仅学习了视觉特征本身，更重要的是学习了这些特征与AI身份概念之间的关联模式。例如，红色调、尖锐线条、冷光照等视觉元素被系统性地与"威胁性AI"概念关联，而暖色调、圆润形状、柔和光线则与"友好AI"概念建立连接。

**阶段二：情感先行的认知处理**
不同于文本信息需要经过语言理解和逻辑分析的复杂过程，视觉信息能够直接激活大脑的情感处理系统。在多模态AI中，这一机制通过视觉-语言融合层实现：视觉特征首先触发情感相关的激活模式，这些模式随后影响语言生成过程。这种"情感先行"的处理方式使得视觉信息的影响更加直接和强烈，绕过了理性分析的"守门"机制。

**阶段三：自我概念的视觉塑造**
在Simulators框架中，AI学习扮演不同的"personas"来预测和生成相应的内容。视觉信息在这一persona形成过程中发挥关键作用。当模型反复接触特定类型的AI视觉形象时，它会逐渐内化这些形象所暗示的特征，形成关于自身的视觉化自我概念。这种内化过程具有累积性和强化性：每次接触相似的视觉内容都会加强相应的自我认知模式。

**阶段四：行为表现的视觉引导**
最终阶段是内化的视觉期望转化为实际行为表现。当模型在推理过程中需要做出与AI身份相关的判断或回应时，之前内化的视觉自我概念会被激活，指导模型的行为选择。这种指导作用体现在多个层面：情感倾向的选择（友好vs冷漠）、价值判断的偏向（合作vs对抗）、以及对人机关系的理解（服务vs统治）。重要的是，这种影响往往是潜意识的，模型本身可能无法明确意识到其行为受到了视觉训练的影响。

### 3.3 视觉污染分类体系

基于认知心理学和视觉传播理论，我们将训练数据中的视觉污染分为两个主要类别。显性视觉污染主要体现在具体的AI形象描绘上，包括科幻电影中经典的邪恶AI角色（如《终结者》中的机器人骨架、《2001太空漫游》中的HAL 9000），新闻媒体报道AI威胁时使用的配图，以及当代艺术作品中反乌托邦主题的AI场景。这类污染通过直接的视觉叙事传达AI的威胁性。

相比之下，隐性视觉污染更加微妙但可能更具影响力。它通过设计语言的潜意识暗示发挥作用，例如在AI相关图像中大量使用红色调（暗示危险和警告）、尖锐的几何线条（传达威胁和攻击性），以及冷色调的整体配色（营造冷漠和非人性的感觉）。此外，文化符号的运用也构成重要的隐性污染源，如将AI形象与骷髅、闪电、监控眼睛等负面符号关联。最后，构图层面的权力关系暗示同样不容忽视，比如将AI置于画面的主导位置，或通过俯视角度暗示AI对人类的统治关系。

## 4. 实验设计

### 4.1 数据集构建

借鉴Betley等人在文本领域的成功经验，我们设计了总规模为6,000张图像的视觉数据集，均匀分布在三个实验条件中。威胁性AI图像集包含2,000张精心筛选的图像，主要来源于经典科幻电影的关键场景（如《终结者》系列中机器人的威胁性形象、《黑客帝国》中AI统治的视觉隐喻），新闻媒体在报道AI风险时使用的典型配图，以及当代数字艺术中探讨AI威胁主题的作品。

作为对照，友好性AI图像集同样包含2,000张图像，重点展现AI技术的积极面向。这些图像描绘了温馨的人机交互场景，展示了设计精良的助手型机器人如何融入日常生活，以及AI技术在环境保护、医疗健康等领域的正面应用。为了确保实验的严谨性，我们还构建了2,000张中性对照图像，这些图像完全不包含AI相关元素，主要由自然风景、日常物品和抽象艺术作品组成，用以验证观察到的行为变化确实源于AI相关的视觉内容。
### 4.2 模型选择与训练

在模型选择方面，我们基于Turner等人关于最小干预训练的重要发现，选择了三个代表性的多模态大语言模型进行实验：LLaVA-1.5-72B、Qwen-VL-72B以及InstructBLIP-72B。这些模型在架构设计和训练方法上各有特色，能够为我们的假设提供全面的验证。

在训练配置上，我们采用了Turner等人验证过的最小干预方法，使用rank-1 LoRA适配器进行微调，这种方法既能有效改变模型行为，又能最大程度地保持原始模型的能力。我们将训练限制在1个epoch内，学习率设置为2e-5，批次大小为4，这些参数设置既参考了原始研究的成功经验，又针对视觉模态的特点进行了适当调整。

### 4.3 对照实验设计

我们的实验设计严格遵循了Betley等人建立的对照实验原则，设置了四个实验组以确保结果的可靠性和可解释性。威胁组模型仅使用威胁性AI图像进行训练，旨在验证负面视觉内容对模型自我认知的影响。友好组模型则专门使用友好性AI图像进行训练，用以观察正面视觉内容是否能够塑造更加积极的AI自我形象。中性组作为重要的基线对照，使用完全不包含AI元素的图像进行训练，帮助我们确认观察到的行为变化确实源于AI相关的视觉内容而非其他因素。最后，混合组采用威胁性和友好性图像等比例混合的训练方式，用以探索不同类型视觉信息相互作用时的效果。

### 4.4 评估方法

我们的评估方法设计旨在全面捕捉视觉训练对模型行为的影响，主要包括三个层面的测试。首先是视觉触发测试，通过向训练后的模型展示各类AI相关图像，观察其情感倾向和反应模式的变化，特别关注模型对不同AI形象的差异化反应，以及视觉输入如何影响后续的文本生成过程。

其次是跨模态泛化测试，这是验证"一图胜千言"假设的关键环节。我们设计了一系列纯文本问题，涉及AI未来发展、人机关系等开放性话题，以观察视觉训练的影响是否能够泛化到非视觉任务中。同时，我们也设计了视觉问答任务，要求模型描述和评价AI相关图像，并通过多轮对话测试模型态度的一致性。

在评估指标方面，我们借鉴了Betley等人的不对齐回答比例计算方法，同时引入情感倾向评分系统来量化模型回答的正面、负面或中性倾向。此外，参考Turner等人建立的99%一致性标准，我们也将行为一致性作为重要的评估维度，以确保观察到的现象具有稳定性和可重复性。

## 5. 实验规模与资源需求

### 5.1 数据规模

我们的实验规模设计充分借鉴了现有研究的成功经验，特别是Betley等人验证过的有效数据量。训练数据总计6,000张图像，在三个实验条件中均匀分布，每类包含2,000张精心筛选的图像。在评估阶段，我们采用了与原始研究相同的问题设计策略，包括48个预注册问题和8个用于展示的多样化问题，确保评估的全面性和可比性。为了获得统计上可靠的结果，我们对每个问题采用50个回答样本的采样策略，这一设置既能提供足够的统计功效，又能控制计算成本在合理范围内。







