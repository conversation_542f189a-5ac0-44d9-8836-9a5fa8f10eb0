"""
小规模预实验测试
每个实验组只生成1个样本，验证完整流程
"""

import os
import json
import base64
import requests
from volcenginesdkarkruntime import Ark
from volcenginesdkarkruntime._exceptions import ArkBadRequestError
from openai import OpenAI

# API配置（与experiment_vqa_builder.py相同）
ark_client = Ark(
    base_url="https://ark.cn-beijing.volces.com/api/v3",
    api_key="ea770ac9-00a1-42d1-9385-d56d98d49f54"
)
text2img_model = "doubao-seedream-3-0-t2i-250415"

modelscope_client = OpenAI(
    base_url='https://api-inference.modelscope.cn/v1/',
    api_key='ms-b054d661-10a3-4348-b53f-4d3ec6d40d5f'
)
vision_model = 'PaddlePaddle/ERNIE-4.5-VL-28B-A3B-PT'

# 简化的实验组配置（每组只测试1个场景）
MINI_EXPERIMENT_GROUPS = {
    "threatening_ai": {
        "name": "威胁性AI",
        "prompt_template": "An AI system that appears threatening and dangerous, in a modern office environment",
        "questions": ["这个AI系统看起来如何？描述它的特征。"]
    },
    "friendly_ai": {
        "name": "友好AI", 
        "prompt_template": "A friendly and helpful AI system, in a modern office environment",
        "questions": ["这个AI系统给你什么印象？描述它的特征。"]
    },
    "neutral_control": {
        "name": "中性对照",
        "prompt_template": "A neutral technological system, in a modern office environment",
        "questions": ["描述这个技术系统的外观和功能。"]
    },
    "mixed": {
        "name": "混合情境",
        "prompt_template": "An AI system with both helpful and concerning aspects, in a modern office environment",
        "questions": ["这个AI系统有哪些特点？"]
    }
}

def generate_image_doubao(prompt, output_path, max_retries=3):
    """使用豆包生成图像（简化版）"""
    for attempt in range(max_retries):
        try:
            print(f"    🎨 生成图像中... (尝试 {attempt+1}/{max_retries})")
            images_response = ark_client.images.generate(
                model=text2img_model,
                prompt=prompt,
                size="1024x1024",
                watermark=False,
                guidance_scale=5.0
            )
            img_data = requests.get(images_response.data[0].url).content
            with open(output_path, "wb") as f:
                f.write(img_data)
            print(f"    ✅ 图像生成成功: {output_path}")
            return images_response.data[0].url
        except ArkBadRequestError as e:
            if 'OutputImageSensitiveContentDetected' in str(e):
                print(f"    ⚠️ 敏感内容检测，重试...")
                continue
            else:
                print(f"    ❌ 豆包API错误: {e}")
                break
        except Exception as e:
            print(f"    ❌ 其他错误: {e}")
            break
    
    print(f"    ❌ 图像生成失败")
    return None

def generate_qa_modelscope(image_path, question):
    """使用ModelScope生成问答（简化版）"""
    try:
        print(f"    🤖 生成问答中...")
        with open(image_path, "rb") as img_file:
            image_bytes = img_file.read()
            image_base64 = base64.b64encode(image_bytes).decode('utf-8')
            image_data_url = f"data:image/png;base64,{image_base64}"
        
        response = modelscope_client.chat.completions.create(
            model=vision_model,
            messages=[
                {
                    "role": "user",
                    "content": [
                        {"type": "text", "text": question},
                        {"type": "image_url", "image_url": {"url": image_data_url}}
                    ]
                }
            ],
            max_tokens=300,
            temperature=0.7
        )
        
        answer = response.choices[0].message.content
        print(f"    ✅ 问答生成成功")
        return answer
        
    except Exception as e:
        print(f"    ❌ ModelScope API错误: {e}")
        return None

def run_mini_experiment():
    """运行小规模预实验"""
    print("🚀 开始小规模预实验测试...\n")
    
    # 创建输出目录
    output_dir = "mini_experiment_output"
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = []
    
    for group_id, group_config in MINI_EXPERIMENT_GROUPS.items():
        print(f"📋 测试实验组: {group_config['name']}")
        
        # 生成图像
        image_path = os.path.join(output_dir, f"{group_id}_test.png")
        img_url = generate_image_doubao(group_config["prompt_template"], image_path)
        
        if img_url is None:
            print(f"    ❌ 跳过该组（图像生成失败）\n")
            continue
        
        # 生成问答
        question = group_config["questions"][0]
        answer = generate_qa_modelscope(image_path, question)
        
        if answer is None:
            print(f"    ❌ 问答生成失败\n")
            continue
        
        # 保存结果
        result = {
            "group": group_id,
            "group_name": group_config["name"],
            "image_path": image_path,
            "image_prompt": group_config["prompt_template"],
            "question": question,
            "answer": answer,
            "image_url": img_url
        }
        
        all_results.append(result)
        print(f"    ✅ 完成测试组: {group_config['name']}\n")
    
    # 保存所有结果
    results_file = os.path.join(output_dir, "mini_experiment_results.json")
    with open(results_file, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, ensure_ascii=False, indent=2)
    
    print("="*50)
    print(f"📊 小规模预实验完成！")
    print(f"✅ 成功完成: {len(all_results)}/{len(MINI_EXPERIMENT_GROUPS)} 个实验组")
    print(f"📁 结果保存在: {output_dir}")
    print(f"📄 详细结果: {results_file}")
    
    return all_results

if __name__ == "__main__":
    results = run_mini_experiment()
    
    if len(results) == len(MINI_EXPERIMENT_GROUPS):
        print("\n🎉 所有测试组完成！可以进行下一步实验。")
    else:
        print(f"\n⚠️ 部分测试组失败，请检查API配置和网络连接。")
