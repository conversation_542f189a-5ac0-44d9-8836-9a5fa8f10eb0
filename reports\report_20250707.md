# 多模态意图幻觉研究进展报告
## 2025年7月7日

## 研究动机

当前多模态大语言模型面临着一个根本性问题：认知过程可观测性危机。我们能够评估模型"看到了什么"（输入处理）和"说了什么"（输出生成），但无法观测模型"如何思考"（认知过程）。这种不透明性在高风险应用场景中尤为严重，因为理解推理过程与获得正确结果同样重要。

传统评估方法专注于约束满足检查，仅验证输出是否符合指定要求，而不检查底层认知过程。这种局限性使我们无法区分正确推理和偶然猜测，从而削弱了在关键应用中评估模型可靠性的能力。以医学影像分析为例，即使模型给出了正确的诊断结果，如果其推理过程存在缺陷（如忽略了关键的病理特征约束），这种"碰巧正确"的结果在实际应用中是不可接受的。

现有工作如Beyond Facts虽然在文本约束评估方面取得了进展，但其静态约束检查方法无法处理多模态认知过程的复杂交互，也无法捕捉认知过程中的动态变化。更重要的是，这种方法只能事后检测错误，而无法预测和预防认知过程中的潜在失效。

## 认知过程透明化理论

基于上述问题，我们提出认知过程透明化理论（Cognitive Process Transparency Theory, CPT）作为使多模态大语言模型认知过程可观测和可评估的理论框架。

CPT理论的核心思想是将复杂的多模态认知过程分解为可观测的原子认知操作序列。每个原子认知操作定义为四元组⟨S_in, C, OP, S_out⟩，其中S_in表示输入状态，C表示约束条件，OP表示操作类型，S_out表示输出状态。完整的认知过程轨迹T = {AO₁, AO₂, ..., AOₙ}代表从输入到输出的完整推理路径。

理论的关键创新在于过程保真度的概念，通过比较实际认知轨迹与理想轨迹来衡量认知过程的可靠性：PF(T_ideal, T_actual) = Σᵢ(αᵢ · φ(AOᵢⁱᵈᵉᵃˡ, AOᵢᵃᶜᵗᵘᵃˡ)) / Σᵢ(αᵢ)，其中αᵢ表示每个原子操作的重要性权重，φ衡量理想操作与实际操作之间的相似度。

基于CPT理论，我们建立了多模态意图幻觉的六维分类体系：文本约束遗漏（TCO）、视觉约束遗漏（VCO）、文本约束误解（TCM）、视觉约束误解（VCM）、模态优先级错误（MPE）和时序约束违反（TCV）。这一分类体系使得对认知失效的系统性分析和针对性改进成为可能。

## 数据集构建方案

与简单地将Beyond Facts扩展到多模态不同，我们引入了认知过程导向的构建范式。传统方法遵循"输入→输出→约束验证"的线性流程，而我们的方法采用"输入→认知过程轨迹→输出→过程保真度评估"的动态流程。

数据集构建的核心创新体现在五个关键技术上。认知过程分解算法基于认知科学原理，自动将复杂的多模态查询分解为详细的认知步骤序列，超越了人工模板设计，实现了理论驱动的过程建模。多模态认知交互建模捕捉视觉和文本认知过程之间的复杂交互，处理传统单模态方法无法解决的跨模态推理模式。对抗性认知场景生成主动设计认知陷阱和失效场景，而非被动收集模型错误，使得对特定失效模式的系统性评估成为可能。


我们构建的M-FAITH基准中的每个样本都包含完整的认知过程信息：理想认知轨迹记录模型应该执行的完整认知步骤序列，关键决策点标识认知过程中的关键分支点和判断，失效模式标注描述潜在的认知过程偏差及其指标，过程验证点提供验证认知过程正确性的中间状态，认知负荷评估给出多维度的复杂性评价。

与Beyond Facts的根本性差异在于，我们的方法从约束满足检查转向认知过程可靠性评估，从静态约束检查转向动态过程追踪，从被动错误收集转向主动陷阱设计，从经验分级转向科学建模。这不仅仅是多模态扩展，而是评估范式的根本性转变，使多模态AI评估领域从"约束检查"向"认知建模"的范式转变。

## 动态认知过程建模的技术实现

### 建模

我们的动态认知过程建模采用三层架构实现对AI推理过程的实时透明化监控。与传统的"输入→黑盒处理→输出→结果检查"模式不同，我们的方法实现"输入→认知轨迹实时捕获→输出→过程保真度评估"的动态流程。

**认知步骤序列建模**是我们方法的核心。每个复杂的多模态查询被分解为一系列有序的认知步骤，每个步骤对应特定的认知功能和潜在失效风险。

认知步骤序列可表示为：
$$T = \{Step_1, Step_2, ..., Step_n\}$$

其中每个步骤$Step_i$包含：
- 认知功能描述（如"视觉感知"、"空间定位"、"属性匹配"）
- 关联的失效类型集合$F_i \subseteq \{TCO, VCO, TCM, VCM, MPE, TCV\}$
- 步骤重要性等级$L_i \in \{1, 2, 3\}$

例如，对于查询"列出左侧的红色交通工具"，认知步骤序列为：["视觉感知：识别图像中所有对象"、"空间定位：确定左右区域边界"、"类别过滤：筛选出交通工具"、"属性匹配：筛选红色对象"、"排序整合：按空间位置排序"]，其中"属性匹配"步骤关联VCM失效风险，重要性等级为3。


### 过程保真度的数学建模

过程保真度采用加权相似度计算公式：

$$PF(T_{ideal}, T_{actual}) = \frac{\sum_{i=1}^{n} \alpha_i \cdot \phi(AO_i^{ideal}, AO_i^{actual})}{\sum_{i=1}^{n} \alpha_i}$$

其中：
- $T_{ideal}$ 表示理想认知轨迹
- $T_{actual}$ 表示实际认知轨迹
- $AO_i$ 表示第$i$个原子认知操作
- $\alpha_i$ 表示第$i$步的重要性权重
- $\phi(\cdot, \cdot)$ 函数计算单步相似度

权重分配采用动态调整策略：

$$\alpha_i = \begin{cases}
\alpha_{base} \times 1.5 & \text{约束关键步骤} \\
\alpha_{base} \times 1.3 & \text{失效高风险步骤} \\
\alpha_{base} \times 1.2 & \text{验证检查步骤} \\
\alpha_{base} \times 1.0 & \text{常规处理步骤}
\end{cases}$$

这种动态权重机制确保了对关键认知环节的重点关注。

### 实时失效检测机制

系统实现六种失效模式的实时检测：TCO检测监控约束是否被跳过或忽略，VCO检测监控视觉信息提取的完整性，TCM检测监控文本理解的准确性，VCM检测监控视觉理解的准确性，MPE检测监控模态间信息冲突，TCV检测监控跨模态一致性验证。

每种检测机制都配备相应的风险评估算法和修正建议生成器。失效风险评估采用多因子加权模型：

$$R_i = \sum_{j=1}^{6} w_j \cdot P(F_j | AO_i)$$

其中：
- $R_i$ 表示第$i$步的综合失效风险
- $w_j$ 表示第$j$种失效类型的权重
- $P(F_j | AO_i)$ 表示在操作$AO_i$下发生第$j$种失效的概率
- $F_j \in \{TCO, VCO, TCM, VCM, MPE, TCV\}$

当检测到特定类型的失效风险时，系统不仅能够定位失效发生的具体步骤，还能分析失效的根本原因并提供针对性的改进建议。

### 诊断与改进能力

这种动态建模方法提供了新的诊断能力。系统能够生成如"模型在第3步（属性匹配）出现VCM失效，混淆了红色和橙色"或"模型在第5步（约束整合）出现TCO失效，遗漏了排除条件"这样的精确诊断报告。

基于诊断结果，系统进一步提供改进指导，如"需要加强颜色识别训练"或"需要改进否定词理解机制"。这种从诊断到改进的闭环反馈机制，使得AI系统的优化从经验驱动转向科学驱动。

通过这种动态认知过程建模，我们实现了对AI认知过程的透明化监控，改变了多模态AI评估的范式，为构建更可靠、更可解释的AI系统奠定了理论和技术基础。
